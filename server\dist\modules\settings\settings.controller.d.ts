import { SettingsService } from './settings.service';
import { UpdateSettingsDto } from './dto/update-settings.dto';
import { SettingsResponseDto } from './dto/settings-response.dto';
export declare class SettingsController {
    private readonly settingsService;
    constructor(settingsService: SettingsService);
    findAll(): Promise<SettingsResponseDto[]>;
    findOne(id: string): Promise<SettingsResponseDto>;
    findBy<PERSON>ey(key: string): Promise<SettingsResponseDto>;
    update(id: string, updateSettingsDto: UpdateSettingsDto): Promise<SettingsResponseDto>;
    updateBy<PERSON><PERSON>(key: string, body: {
        value: string;
    }): Promise<SettingsResponseDto>;
    initializeDefaults(): Promise<void>;
}
