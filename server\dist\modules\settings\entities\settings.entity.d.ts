import { Document } from 'mongoose';
export type SettingsDocument = Settings & Document;
export declare class Settings {
    id: string;
    key: string;
    value: string;
    description?: string;
    type: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare const SettingsSchema: import("mongoose").Schema<Settings, import("mongoose").Model<Settings, any, any, any, Document<unknown, any, Settings, any> & Settings & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Settings, Document<unknown, {}, import("mongoose").FlatRecord<Settings>, {}> & import("mongoose").FlatRecord<Settings> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
export type SettingsEntity = Settings;
