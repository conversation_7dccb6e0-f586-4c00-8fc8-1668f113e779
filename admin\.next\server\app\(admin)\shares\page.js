(()=>{var e={};e.id=40,e.ids=[40],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50573:(e,t,r)=>{Promise.resolve().then(r.bind(r,74736))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74541:(e,t,r)=>{Promise.resolve().then(r.bind(r,93486))},74736:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\shares\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91387:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>o});var s=r(65239),i=r(48088),n=r(88170),a=r.n(n),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);let o={children:["",{children:["(admin)",{children:["shares",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74736)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(admin)/shares/page",pathname:"/shares",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},93486:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>z});var s=r(60687),i=r(43210),n=r(99053),a=r(94733),l=r(70084),d=r(28173),o=r(35899),c=r(33519),p=r(52378),h=r(48111),u=r(21411),x=r(94132),m=r(11585),y=r(96625),A=r(4691),j=r(27783),v=r(7872),g=r(2535),f=r(59823),w=r(56306),b=r(26323),k=r(34308),q=r(23575),C=r(3788),P=r(53082),S=r(43910);let{Title:I,Text:_}=n.A,{TextArea:D}=a.A,{Option:O}=l.A;function z(){let[e,t]=(0,i.useState)([]),[r,n]=(0,i.useState)(!1),[z,F]=(0,i.useState)(!1),[R,G]=(0,i.useState)(null),[U]=d.A.useForm(),E=async()=>{n(!0);try{let e=await S.Dw.getAllShareConfigs();t(e)}catch(e){o.Ay.error("获取分享配置失败"),console.error("获取分享配置失败:",e)}finally{n(!1)}},L=e=>{G(e||null),F(!0),e?U.setFieldsValue({name:e.name,title:e.title,path:e.path,imageUrl:e.imageUrl,description:e.description,type:e.type,isActive:e.isActive,sortOrder:e.sortOrder}):(U.resetFields(),U.setFieldsValue({type:"custom",isActive:!0,sortOrder:1}))},W=()=>{F(!1),G(null),U.resetFields()},T=async()=>{try{let e=await U.validateFields();R?(await S.Dw.updateShareConfig(R.id,e),o.Ay.success("分享配置更新成功")):(await S.Dw.createShareConfig(e),o.Ay.success("分享配置创建成功")),W(),E()}catch(e){if(e&&"object"==typeof e&&"errorFields"in e)o.Ay.error("请检查表单输入");else{let t=e&&"object"==typeof e&&"message"in e?e.message:"操作失败";o.Ay.error(t)}}},M=async e=>{try{await S.Dw.toggleShareConfig(e.id),o.Ay.success(`${e.isActive?"禁用":"启用"}成功`),E()}catch(t){let e=t&&"object"==typeof t&&"message"in t?t.message:"操作失败";o.Ay.error(e)}},V=async e=>{try{await S.Dw.deleteShareConfig(e.id),o.Ay.success("删除成功"),E()}catch(t){let e=t&&"object"==typeof t&&"message"in t?t.message:"删除失败";o.Ay.error(e)}},K=e=>({default:"blue",result:"green",level:"orange",achievement:"purple",custom:"gray"})[e]||"gray",$=e=>{let t=S.WS.find(t=>t.value===e);return t?.label||e},B=[{title:"配置名称",dataIndex:"name",key:"name",width:150,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:500},children:e}),(0,s.jsxs)(_,{type:"secondary",style:{fontSize:"12px"},children:["ID: ",t.id]})]})},{title:"分享标题",dataIndex:"title",key:"title",width:200,render:e=>(0,s.jsx)(c.A,{title:e,children:(0,s.jsx)("div",{style:{maxWidth:"180px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e})})},{title:"分享路径",dataIndex:"path",key:"path",width:200,render:e=>(0,s.jsx)(c.A,{title:e,children:(0,s.jsx)(_,{code:!0,style:{maxWidth:"180px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",display:"block"},children:e})})},{title:"类型",dataIndex:"type",key:"type",width:100,render:e=>(0,s.jsx)(p.A,{color:K(e),children:$(e)})},{title:"状态",dataIndex:"isActive",key:"isActive",width:80,render:e=>(0,s.jsx)(p.A,{color:e?"success":"default",icon:e?(0,s.jsx)(w.A,{}):(0,s.jsx)(b.A,{}),children:e?"启用":"禁用"})},{title:"排序",dataIndex:"sortOrder",key:"sortOrder",width:80,align:"center"},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:150,render:e=>new Date(e).toLocaleString()},{title:"操作",key:"action",width:200,render:(e,t)=>(0,s.jsxs)(h.A,{size:"small",children:[(0,s.jsx)(c.A,{title:"编辑",children:(0,s.jsx)(u.Ay,{type:"text",icon:(0,s.jsx)(k.A,{}),onClick:()=>L(t)})}),(0,s.jsx)(c.A,{title:t.isActive?"禁用":"启用",children:(0,s.jsx)(u.Ay,{type:"text",icon:t.isActive?(0,s.jsx)(b.A,{}):(0,s.jsx)(w.A,{}),onClick:()=>M(t)})}),"default"!==t.type&&(0,s.jsx)(c.A,{title:"删除",children:(0,s.jsx)(x.A,{title:"确定要删除这个分享配置吗？",onConfirm:()=>V(t),okText:"确定",cancelText:"取消",children:(0,s.jsx)(u.Ay,{type:"text",danger:!0,icon:(0,s.jsx)(q.A,{})})})})]})}];return(0,s.jsxs)("div",{style:{padding:"24px"},children:[(0,s.jsxs)(m.A,{children:[(0,s.jsx)("div",{style:{marginBottom:"24px"},children:(0,s.jsxs)(y.A,{justify:"space-between",align:"middle",children:[(0,s.jsxs)(A.A,{children:[(0,s.jsxs)(I,{level:3,style:{margin:0},children:[(0,s.jsx)(C.A,{style:{marginRight:"8px"}}),"分享管理"]}),(0,s.jsx)(_,{type:"secondary",children:"管理微信小程序的分享配置，包括分享标题、路径和图片等"})]}),(0,s.jsx)(A.A,{children:(0,s.jsx)(u.Ay,{type:"primary",icon:(0,s.jsx)(P.A,{}),onClick:()=>L(),children:"新建分享配置"})})]})}),(0,s.jsx)(j.A,{columns:B,dataSource:e,rowKey:"id",loading:r,pagination:{total:e.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`},scroll:{x:1200}})]}),(0,s.jsx)(v.A,{title:R?"编辑分享配置":"新建分享配置",open:z,onOk:T,onCancel:W,width:600,destroyOnClose:!0,children:(0,s.jsxs)(d.A,{form:U,layout:"vertical",initialValues:{type:"custom",isActive:!0,sortOrder:1},children:[(0,s.jsx)(d.A.Item,{name:"name",label:"配置名称",rules:[{required:!0,message:"请输入配置名称"}],children:(0,s.jsx)(a.A,{placeholder:"请输入配置名称"})}),(0,s.jsx)(d.A.Item,{name:"title",label:"分享标题",rules:[{required:!0,message:"请输入分享标题"}],children:(0,s.jsx)(a.A,{placeholder:"请输入分享标题"})}),(0,s.jsx)(d.A.Item,{name:"path",label:"分享路径",rules:[{required:!0,message:"请输入分享路径"}],children:(0,s.jsx)(l.A,{placeholder:"请选择或输入分享路径",mode:"tags",allowClear:!0,children:S.cm.map(e=>(0,s.jsx)(O,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:e.label}),(0,s.jsx)(_,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})}),(0,s.jsx)(d.A.Item,{name:"imageUrl",label:"分享图片URL",rules:[{type:"url",message:"请输入有效的URL"}],children:(0,s.jsx)(a.A,{placeholder:"请输入分享图片URL（可选）"})}),(0,s.jsx)(d.A.Item,{name:"description",label:"分享描述",children:(0,s.jsx)(D,{placeholder:"请输入分享描述（可选）",rows:3,maxLength:200,showCount:!0})}),(0,s.jsxs)(y.A,{gutter:16,children:[(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(d.A.Item,{name:"type",label:"分享类型",rules:[{required:!0,message:"请选择分享类型"}],children:(0,s.jsx)(l.A,{placeholder:"请选择分享类型",children:S.WS.map(e=>(0,s.jsx)(O,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:e.label}),(0,s.jsx)(_,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})})}),(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(d.A.Item,{name:"sortOrder",label:"排序权重",rules:[{required:!0,message:"请输入排序权重"}],children:(0,s.jsx)(g.A,{min:1,max:999,placeholder:"排序权重",style:{width:"100%"}})})})]}),(0,s.jsx)(d.A.Item,{name:"isActive",label:"启用状态",valuePropName:"checked",children:(0,s.jsx)(f.A,{checkedChildren:"启用",unCheckedChildren:"禁用"})})]})})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,433,658,816,204,331,553,84,173,579,542,145,915,756],()=>r(91387));module.exports=s})();