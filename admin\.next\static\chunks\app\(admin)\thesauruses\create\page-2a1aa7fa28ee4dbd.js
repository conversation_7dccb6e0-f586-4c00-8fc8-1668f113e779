(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[221],{21073:(e,t,r)=>{"use strict";r.d(t,{Au:()=>s,Io:()=>o,LQ:()=>n,zN:()=>c});var a=r(29799);let s={getAll:async()=>(await a.F.get("/api/v1/thesauruses")).data,getById:async e=>(await a.F.get("/api/v1/thesauruses/".concat(e))).data,create:async e=>(await a.F.post("/api/v1/thesauruses",e)).data,update:async(e,t)=>(await a.F.patch("/api/v1/thesauruses/".concat(e),t)).data,delete:async e=>{await a.F.delete("/api/v1/thesauruses/".concat(e))},addPhrase:async(e,t)=>(await a.<PERSON>.post("/api/v1/thesauruses/".concat(e,"/phrases"),t)).data,removePhrase:async(e,t)=>(await a.F.delete("/api/v1/thesauruses/".concat(e,"/phrases/").concat(t))).data},n=s.create;s.update,s.delete;let o=s.delete;s.getById,s.getAll;let c=s.getAll},29799:(e,t,r)=>{"use strict";r.d(t,{F:()=>c});var a=r(23464),s=r(19868);let n={BASE_URL:"http://127.0.0.1:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}},o=a.A.create({baseURL:n.BASE_URL,timeout:n.TIMEOUT,headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),o.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){let{status:t,data:r}=e.response;switch(t){case 401:s.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:s.Ay.error("没有权限访问该资源");break;case 404:s.Ay.error("请求的资源不存在");break;case 500:s.Ay.error("服务器内部错误");break;default:s.Ay.error((null==r?void 0:r.message)||"请求失败")}}else e.request?s.Ay.error("网络连接失败，请检查网络"):s.Ay.error("请求配置错误");return Promise.reject(e)});let c={get:(e,t)=>o.get(e,t),post:(e,t,r)=>o.post(e,t,r),put:(e,t,r)=>o.put(e,t,r),patch:(e,t,r)=>o.patch(e,t,r),delete:(e,t)=>o.delete(e,t)}},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useServerInsertedHTML")&&r.d(t,{useServerInsertedHTML:function(){return a.useServerInsertedHTML}})},44435:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var a=r(95155),s=r(12115),n=r(97605),o=r(44670),c=r(19868),u=r(505),l=r(56020),i=r(30662),d=r(35695),h=r(21073);let{Title:p}=n.A,A=()=>{let[e]=o.A.useForm(),t=(0,d.useRouter)(),[r,n]=(0,s.useState)(!1),A=async e=>{n(!0);try{await (0,h.LQ)(e),c.Ay.success("词库创建成功！"),t.push("/thesauruses")}catch(e){c.Ay.error(e.message||"词库创建失败！")}finally{n(!1)}};return(0,a.jsxs)(u.A,{children:[(0,a.jsx)(p,{level:3,children:"创建新词库"}),(0,a.jsxs)(o.A,{form:e,layout:"vertical",onFinish:A,children:[(0,a.jsx)(o.A.Item,{name:"name",label:"词库名称",rules:[{required:!0,message:"请输入词库名称"}],children:(0,a.jsx)(l.A,{placeholder:"例如：日常用语"})}),(0,a.jsx)(o.A.Item,{name:"description",label:"描述 (可选)",children:(0,a.jsx)(l.A.TextArea,{rows:3,placeholder:"词库简介"})}),(0,a.jsxs)(o.A.Item,{children:[(0,a.jsx)(i.Ay,{type:"primary",htmlType:"submit",loading:r,children:"创建词库"}),(0,a.jsx)(i.Ay,{style:{marginLeft:8},onClick:()=>t.back(),children:"取消"})]})]})]})}},69765:(e,t,r)=>{Promise.resolve().then(r.bind(r,44435))}},e=>{var t=t=>e(e.s=t);e.O(0,[8340,547,7469,7497,44,7605,4670,8441,1684,7358],()=>t(69765)),_N_E=e.O()}]);