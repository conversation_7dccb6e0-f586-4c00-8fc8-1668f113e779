import { PhraseService } from './phrase.service';
import { CreatePhraseDto } from './dto/create-phrase.dto';
import { UpdatePhraseDto } from './dto/update-phrase.dto';
import { PhraseResponseDto } from './dto/phrase-response.dto';
export declare class PhraseController {
    private readonly phraseService;
    constructor(phraseService: PhraseService);
    create(createPhraseDto: CreatePhraseDto): Promise<PhraseResponseDto>;
    findAll(): Promise<PhraseResponseDto[]>;
    findOne(id: string): Promise<PhraseResponseDto>;
    update(id: string, updatePhraseDto: UpdatePhraseDto): Promise<PhraseResponseDto>;
    remove(id: string): Promise<void>;
}
