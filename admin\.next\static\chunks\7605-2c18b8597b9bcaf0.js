(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7605],{11499:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],l=0;l<e.rangeCount;l++)n.push(e.getRangeAt(l));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},69068:(e,t,n)=>{"use strict";var l=n(11499),r={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var n,o,a,i,c,s,u,d,p=!1;t||(t={}),a=t.debug||!1;try{if(c=l(),s=document.createRange(),u=document.getSelection(),(d=document.createElement("span")).textContent=e,d.ariaHidden="true",d.style.all="unset",d.style.position="fixed",d.style.top=0,d.style.clip="rect(0, 0, 0, 0)",d.style.whiteSpace="pre",d.style.webkitUserSelect="text",d.style.MozUserSelect="text",d.style.msUserSelect="text",d.style.userSelect="text",d.addEventListener("copy",function(n){if(n.stopPropagation(),t.format)if(n.preventDefault(),void 0===n.clipboardData){a&&console.warn("unable to use e.clipboardData"),a&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var l=r[t.format]||r.default;window.clipboardData.setData(l,e)}else n.clipboardData.clearData(),n.clipboardData.setData(t.format,e);t.onCopy&&(n.preventDefault(),t.onCopy(n.clipboardData))}),document.body.appendChild(d),s.selectNodeContents(d),u.addRange(s),!document.execCommand("copy"))throw Error("copy command was unsuccessful");p=!0}catch(l){a&&console.error("unable to copy using execCommand: ",l),a&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),p=!0}catch(l){a&&console.error("unable to copy using clipboardData: ",l),a&&console.error("falling back to prompt"),n="message"in t?t.message:"Copy to clipboard: #{key}, Enter",o=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",i=n.replace(/#{\s*key\s*}/g,o),window.prompt(i,e)}}finally{u&&("function"==typeof u.removeRange?u.removeRange(s):u.removeAllRanges()),d&&document.body.removeChild(d),c()}return p}},70129:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var l=n(79630),r=n(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};var a=n(62764);let i=r.forwardRef(function(e,t){return r.createElement(a.A,(0,l.A)({},e,{ref:t,icon:o}))})},79659:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var l=n(79630),r=n(12115);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var a=n(62764);let i=r.forwardRef(function(e,t){return r.createElement(a.A,(0,l.A)({},e,{ref:t,icon:o}))})},97605:(e,t,n)=>{"use strict";n.d(t,{A:()=>eh});var l=n(12115),r=n(85757),o=n(79659),a=n(29300),i=n.n(a),c=n(32417),s=n(63715),u=n(49172),d=n(48804),p=n(17980),f=n(74686),m=n(19824),g=n(15982),b=n(8530),y=n(26922),v=n(79630);let h={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"};var x=n(62764),O=l.forwardRef(function(e,t){return l.createElement(x.A,(0,v.A)({},e,{ref:t,icon:h}))}),E=n(17233),w=n(80163),S=n(37497),j=n(18184),C=n(45431),k=n(94842),A=n(85573);let R=(e,t,n,l)=>{let{titleMarginBottom:r,fontWeightStrong:o}=l;return{marginBottom:r,color:n,fontWeight:o,fontSize:e,lineHeight:t}},T=e=>{let t={};return[1,2,3,4,5].forEach(n=>{t["\n      h".concat(n,"&,\n      div&-h").concat(n,",\n      div&-h").concat(n," > textarea,\n      h").concat(n,"\n    ")]=R(e["fontSizeHeading".concat(n)],e["lineHeightHeading".concat(n)],e.colorTextHeading,e)}),t},D=e=>{let{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},(0,j.Y1)(e)),{userSelect:"text",["&[disabled], &".concat(t,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},I=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:k.bK[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:e.fontWeightStrong},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),H=e=>{let{componentCls:t,paddingSM:n}=e;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(n).mul(-1).equal(),marginBottom:"calc(1em - ".concat((0,A.zA)(n),")")},["".concat(t,"-edit-content-confirm")]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorIcon,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},M=e=>({["".concat(e.componentCls,"-copy-success")]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},["".concat(e.componentCls,"-copy-icon-only")]:{marginInlineStart:0}}),z=()=>({"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),P=e=>{let{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,["&".concat(t,"-secondary")]:{color:e.colorTextDescription},["&".concat(t,"-success")]:{color:e.colorSuccessText},["&".concat(t,"-warning")]:{color:e.colorWarningText},["&".concat(t,"-danger")]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},["&".concat(t,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},T(e)),{["\n      & + h1".concat(t,",\n      & + h2").concat(t,",\n      & + h3").concat(t,",\n      & + h4").concat(t,",\n      & + h5").concat(t,"\n      ")]:{marginTop:n},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:n}}}),I(e)),D(e)),{["\n        ".concat(t,"-expand,\n        ").concat(t,"-collapse,\n        ").concat(t,"-edit,\n        ").concat(t,"-copy\n      ")]:Object.assign(Object.assign({},(0,j.Y1)(e)),{marginInlineStart:e.marginXXS})}),H(e)),M(e)),z()),{"&-rtl":{direction:"rtl"}})}},B=(0,C.OF)("Typography",e=>[P(e)],()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"})),N=e=>{let{prefixCls:t,"aria-label":n,className:r,style:o,direction:a,maxLength:c,autoSize:s=!0,value:u,onSave:d,onCancel:p,onEnd:f,component:m,enterIcon:g=l.createElement(O,null)}=e,b=l.useRef(null),y=l.useRef(!1),v=l.useRef(null),[h,x]=l.useState(u);l.useEffect(()=>{x(u)},[u]),l.useEffect(()=>{var e;if(null==(e=b.current)?void 0:e.resizableTextArea){let{textArea:e}=b.current.resizableTextArea;e.focus();let{length:t}=e.value;e.setSelectionRange(t,t)}},[]);let j=()=>{d(h.trim())},[C,k,A]=B(t),R=i()(t,"".concat(t,"-edit-content"),{["".concat(t,"-rtl")]:"rtl"===a,["".concat(t,"-").concat(m)]:!!m},r,k,A);return C(l.createElement("div",{className:R,style:o},l.createElement(S.A,{ref:b,maxLength:c,value:h,onChange:e=>{let{target:t}=e;x(t.value.replace(/[\n\r]/g,""))},onKeyDown:e=>{let{keyCode:t}=e;y.current||(v.current=t)},onKeyUp:e=>{let{keyCode:t,ctrlKey:n,altKey:l,metaKey:r,shiftKey:o}=e;v.current!==t||y.current||n||l||r||o||(t===E.A.ENTER?(j(),null==f||f()):t===E.A.ESC&&p())},onCompositionStart:()=>{y.current=!0},onCompositionEnd:()=>{y.current=!1},onBlur:()=>{j()},"aria-label":n,rows:1,autoSize:s}),null!==g?(0,w.Ob)(g,{className:"".concat(t,"-edit-content-confirm")}):null))};var L=n(69068),W=n.n(L),F=n(18885);let U=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t&&null==e?[]:Array.isArray(e)?e:[e]},V=e=>{let{copyConfig:t,children:n}=e,[r,o]=l.useState(!1),[a,i]=l.useState(!1),c=l.useRef(null),s=()=>{c.current&&clearTimeout(c.current)},u={};t.format&&(u.format=t.format),l.useEffect(()=>s,[]);let d=(0,F.A)(e=>(function(e,t,n,l){return new(n||(n=Promise))(function(r,o){function a(e){try{c(l.next(e))}catch(e){o(e)}}function i(e){try{c(l.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?r(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,i)}c((l=l.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var l;null==e||e.preventDefault(),null==e||e.stopPropagation(),i(!0);try{let r="function"==typeof t.text?yield t.text():t.text;W()(r||U(n,!0).join("")||"",u),i(!1),o(!0),s(),c.current=setTimeout(()=>{o(!1)},3e3),null==(l=t.onCopy)||l.call(t,e)}catch(e){throw i(!1),e}}));return{copied:r,copyLoading:a,onClick:d}};function q(e,t){return l.useMemo(()=>{let n=!!e;return[n,Object.assign(Object.assign({},t),n&&"object"==typeof e?e:null)]},[e])}let K=e=>{let t=(0,l.useRef)(void 0);return(0,l.useEffect)(()=>{t.current=e}),t.current},X=(e,t,n)=>(0,l.useMemo)(()=>!0===e?{title:null!=t?t:n}:(0,l.isValidElement)(e)?{title:e}:"object"==typeof e?Object.assign({title:null!=t?t:n},e):{title:e},[e,t,n]);var _=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,l=Object.getOwnPropertySymbols(e);r<l.length;r++)0>t.indexOf(l[r])&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(n[l[r]]=e[l[r]]);return n};let Y=l.forwardRef((e,t)=>{let{prefixCls:n,component:r="article",className:o,rootClassName:a,setContentRef:c,children:s,direction:u,style:d}=e,p=_(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:m,direction:b,className:y,style:v}=(0,g.TP)("typography"),h=c?(0,f.K4)(t,c):t,x=m("typography",n),[O,E,w]=B(x),S=i()(x,y,{["".concat(x,"-rtl")]:"rtl"===(null!=u?u:b)},o,a,E,w),j=Object.assign(Object.assign({},v),d);return O(l.createElement(r,Object.assign({className:S,style:j,ref:h},p),s))});var Q=n(92638),G=n(70129),J=n(33501);function Z(e){return!1===e?[!1,!1]:Array.isArray(e)?e:[e]}function $(e,t,n){return!0===e||void 0===e?t:e||n&&t}let ee=e=>["string","number"].includes(typeof e),et=e=>{let{prefixCls:t,copied:n,locale:r,iconOnly:o,tooltips:a,icon:c,tabIndex:s,onCopy:u,loading:d}=e,p=Z(a),f=Z(c),{copied:m,copy:g}=null!=r?r:{},b=n?m:g,v=$(p[+!!n],b),h="string"==typeof v?v:b;return l.createElement(y.A,{title:v},l.createElement("button",{type:"button",className:i()("".concat(t,"-copy"),{["".concat(t,"-copy-success")]:n,["".concat(t,"-copy-icon-only")]:o}),onClick:u,"aria-label":h,tabIndex:s},n?$(f[1],l.createElement(Q.A,null),!0):$(f[0],d?l.createElement(J.A,null):l.createElement(G.A,null),!0)))},en=l.forwardRef((e,t)=>{let{style:n,children:r}=e,o=l.useRef(null);return l.useImperativeHandle(t,()=>({isExceed:()=>{let e=o.current;return e.scrollHeight>e.clientHeight},getHeight:()=>o.current.clientHeight})),l.createElement("span",{"aria-hidden":!0,ref:o,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},n)},r)}),el=e=>e.reduce((e,t)=>e+(ee(t)?String(t).length:1),0);function er(e,t){let n=0,l=[];for(let r=0;r<e.length;r+=1){if(n===t)return l;let o=e[r],a=n+(ee(o)?String(o).length:1);if(a>t){let e=t-n;return l.push(String(o).slice(0,e)),l}l.push(o),n=a}return e}let eo={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function ea(e){let{enableMeasure:t,width:n,text:o,children:a,rows:i,expanded:c,miscDeps:d,onEllipsis:p}=e,f=l.useMemo(()=>(0,s.A)(o),[o]),m=l.useMemo(()=>el(f),[o]),g=l.useMemo(()=>a(f,!1),[o]),[b,y]=l.useState(null),v=l.useRef(null),h=l.useRef(null),x=l.useRef(null),O=l.useRef(null),E=l.useRef(null),[w,S]=l.useState(!1),[j,C]=l.useState(0),[k,A]=l.useState(0),[R,T]=l.useState(null);(0,u.A)(()=>{t&&n&&m?C(1):C(0)},[n,o,i,t,f]),(0,u.A)(()=>{var e,t,n,l;if(1===j)C(2),T(h.current&&getComputedStyle(h.current).whiteSpace);else if(2===j){let r=!!(null==(e=x.current)?void 0:e.isExceed());C(r?3:4),y(r?[0,m]:null),S(r);let o=(null==(t=x.current)?void 0:t.getHeight())||0;A(Math.max(o,(1===i?0:(null==(n=O.current)?void 0:n.getHeight())||0)+((null==(l=E.current)?void 0:l.getHeight())||0))+1),p(r)}},[j]);let D=b?Math.ceil((b[0]+b[1])/2):0;(0,u.A)(()=>{var e;let[t,n]=b||[0,0];if(t!==n){let l=((null==(e=v.current)?void 0:e.getHeight())||0)>k,r=D;n-t==1&&(r=l?t:n),y(l?[t,r]:[r,n])}},[b,D]);let I=l.useMemo(()=>{if(!t)return a(f,!1);if(3!==j||!b||b[0]!==b[1]){let e=a(f,!1);return[4,0].includes(j)?e:l.createElement("span",{style:Object.assign(Object.assign({},eo),{WebkitLineClamp:i})},e)}return a(c?f:er(f,b[0]),w)},[c,j,b,f].concat((0,r.A)(d))),H={width:n,margin:0,padding:0,whiteSpace:"nowrap"===R?"normal":"inherit"};return l.createElement(l.Fragment,null,I,2===j&&l.createElement(l.Fragment,null,l.createElement(en,{style:Object.assign(Object.assign(Object.assign({},H),eo),{WebkitLineClamp:i}),ref:x},g),l.createElement(en,{style:Object.assign(Object.assign(Object.assign({},H),eo),{WebkitLineClamp:i-1}),ref:O},g),l.createElement(en,{style:Object.assign(Object.assign(Object.assign({},H),eo),{WebkitLineClamp:1}),ref:E},a([],!0))),3===j&&b&&b[0]!==b[1]&&l.createElement(en,{style:Object.assign(Object.assign({},H),{top:400}),ref:v},a(er(f,D),!0)),1===j&&l.createElement("span",{style:{whiteSpace:"inherit"},ref:h}))}let ei=e=>{let{enableEllipsis:t,isEllipsis:n,children:r,tooltipProps:o}=e;return(null==o?void 0:o.title)&&t?l.createElement(y.A,Object.assign({open:!!n&&void 0},o),r):r};var ec=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,l=Object.getOwnPropertySymbols(e);r<l.length;r++)0>t.indexOf(l[r])&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(n[l[r]]=e[l[r]]);return n};let es=["delete","mark","code","underline","strong","keyboard","italic"],eu=l.forwardRef((e,t)=>{var n;let{prefixCls:a,className:v,style:h,type:x,disabled:O,children:E,ellipsis:w,editable:S,copyable:j,component:C,title:k}=e,A=ec(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:R,direction:T}=l.useContext(g.QO),[D]=(0,b.A)("Text"),I=l.useRef(null),H=l.useRef(null),M=R("typography",a),z=(0,p.A)(A,es),[P,B]=q(S),[L,W]=(0,d.A)(!1,{value:B.editing}),{triggerType:F=["icon"]}=B,U=e=>{var t;e&&(null==(t=B.onStart)||t.call(B)),W(e)},_=K(L);(0,u.A)(()=>{var e;!L&&_&&(null==(e=H.current)||e.focus())},[L]);let Q=e=>{null==e||e.preventDefault(),U(!0)},[G,J]=q(j),{copied:Z,copyLoading:$,onClick:en}=V({copyConfig:J,children:E}),[el,er]=l.useState(!1),[eo,eu]=l.useState(!1),[ed,ep]=l.useState(!1),[ef,em]=l.useState(!1),[eg,eb]=l.useState(!0),[ey,ev]=q(w,{expandable:!1,symbol:e=>e?null==D?void 0:D.collapse:null==D?void 0:D.expand}),[eh,ex]=(0,d.A)(ev.defaultExpanded||!1,{value:ev.expanded}),eO=ey&&(!eh||"collapsible"===ev.expandable),{rows:eE=1}=ev,ew=l.useMemo(()=>eO&&(void 0!==ev.suffix||ev.onEllipsis||ev.expandable||P||G),[eO,ev,P,G]);(0,u.A)(()=>{ey&&!ew&&(er((0,m.F)("webkitLineClamp")),eu((0,m.F)("textOverflow")))},[ew,ey]);let[eS,ej]=l.useState(eO),eC=l.useMemo(()=>!ew&&(1===eE?eo:el),[ew,eo,el]);(0,u.A)(()=>{ej(eC&&eO)},[eC,eO]);let ek=eO&&(eS?ef:ed),eA=eO&&1===eE&&eS,eR=eO&&eE>1&&eS,eT=(e,t)=>{var n;ex(t.expanded),null==(n=ev.onExpand)||n.call(ev,e,t)},[eD,eI]=l.useState(0),eH=e=>{var t;ep(e),ed!==e&&(null==(t=ev.onEllipsis)||t.call(ev,e))};l.useEffect(()=>{let e=I.current;if(ey&&eS&&e){let t=function(e){let t=document.createElement("em");e.appendChild(t);let n=e.getBoundingClientRect(),l=t.getBoundingClientRect();return e.removeChild(t),n.left>l.left||l.right>n.right||n.top>l.top||l.bottom>n.bottom}(e);ef!==t&&em(t)}},[ey,eS,E,eR,eg,eD]),l.useEffect(()=>{let e=I.current;if("undefined"==typeof IntersectionObserver||!e||!eS||!eO)return;let t=new IntersectionObserver(()=>{eb(!!e.offsetParent)});return t.observe(e),()=>{t.disconnect()}},[eS,eO]);let eM=X(ev.tooltip,B.text,E),ez=l.useMemo(()=>{if(ey&&!eS)return[B.text,E,k,eM.title].find(ee)},[ey,eS,k,eM.title,ek]);if(L)return l.createElement(N,{value:null!=(n=B.text)?n:"string"==typeof E?E:"",onSave:e=>{var t;null==(t=B.onChange)||t.call(B,e),U(!1)},onCancel:()=>{var e;null==(e=B.onCancel)||e.call(B),U(!1)},onEnd:B.onEnd,prefixCls:M,className:v,style:h,direction:T,component:C,maxLength:B.maxLength,autoSize:B.autoSize,enterIcon:B.enterIcon});let eP=()=>{let{expandable:e,symbol:t}=ev;return e?l.createElement("button",{type:"button",key:"expand",className:"".concat(M,"-").concat(eh?"collapse":"expand"),onClick:e=>eT(e,{expanded:!eh}),"aria-label":eh?D.collapse:null==D?void 0:D.expand},"function"==typeof t?t(eh):t):null},eB=()=>{if(!P)return;let{icon:e,tooltip:t,tabIndex:n}=B,r=(0,s.A)(t)[0]||(null==D?void 0:D.edit),a="string"==typeof r?r:"";return F.includes("icon")?l.createElement(y.A,{key:"edit",title:!1===t?"":r},l.createElement("button",{type:"button",ref:H,className:"".concat(M,"-edit"),onClick:Q,"aria-label":a,tabIndex:n},e||l.createElement(o.A,{role:"button"}))):null},eN=()=>G?l.createElement(et,Object.assign({key:"copy"},J,{prefixCls:M,copied:Z,locale:D,onCopy:en,loading:$,iconOnly:null==E})):null,eL=e=>[e&&eP(),eB(),eN()],eW=e=>[e&&!eh&&l.createElement("span",{"aria-hidden":!0,key:"ellipsis"},"..."),ev.suffix,eL(e)];return l.createElement(c.A,{onResize:e=>{let{offsetWidth:t}=e;eI(t)},disabled:!eO},n=>l.createElement(ei,{tooltipProps:eM,enableEllipsis:eO,isEllipsis:ek},l.createElement(Y,Object.assign({className:i()({["".concat(M,"-").concat(x)]:x,["".concat(M,"-disabled")]:O,["".concat(M,"-ellipsis")]:ey,["".concat(M,"-ellipsis-single-line")]:eA,["".concat(M,"-ellipsis-multiple-line")]:eR},v),prefixCls:a,style:Object.assign(Object.assign({},h),{WebkitLineClamp:eR?eE:void 0}),component:C,ref:(0,f.K4)(n,I,t),direction:T,onClick:F.includes("text")?Q:void 0,"aria-label":null==ez?void 0:ez.toString(),title:k},z),l.createElement(ea,{enableMeasure:eO&&!eS,text:E,rows:eE,width:eD,onEllipsis:eH,expanded:eh,miscDeps:[Z,eh,$,P,G,D].concat((0,r.A)(es.map(t=>e[t])))},(t,n)=>(function(e,t){let{mark:n,code:r,underline:o,delete:a,strong:i,keyboard:c,italic:s}=e,u=t;function d(e,t){t&&(u=l.createElement(e,{},u))}return d("strong",i),d("u",o),d("del",a),d("code",r),d("mark",n),d("kbd",c),d("i",s),u})(e,l.createElement(l.Fragment,null,t.length>0&&n&&!eh&&ez?l.createElement("span",{key:"show-content","aria-hidden":!0},t):t,eW(n)))))))});var ed=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,l=Object.getOwnPropertySymbols(e);r<l.length;r++)0>t.indexOf(l[r])&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(n[l[r]]=e[l[r]]);return n};let ep=l.forwardRef((e,t)=>{var{ellipsis:n,rel:r}=e,o=ed(e,["ellipsis","rel"]);let a=Object.assign(Object.assign({},o),{rel:void 0===r&&"_blank"===o.target?"noopener noreferrer":r});return delete a.navigate,l.createElement(eu,Object.assign({},a,{ref:t,ellipsis:!!n,component:"a"}))}),ef=l.forwardRef((e,t)=>l.createElement(eu,Object.assign({ref:t},e,{component:"div"})));var em=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,l=Object.getOwnPropertySymbols(e);r<l.length;r++)0>t.indexOf(l[r])&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(n[l[r]]=e[l[r]]);return n};let eg=l.forwardRef((e,t)=>{var{ellipsis:n}=e,r=em(e,["ellipsis"]);let o=l.useMemo(()=>n&&"object"==typeof n?(0,p.A)(n,["expandable","rows"]):n,[n]);return l.createElement(eu,Object.assign({ref:t},r,{ellipsis:o,component:"span"}))});var eb=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,l=Object.getOwnPropertySymbols(e);r<l.length;r++)0>t.indexOf(l[r])&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(n[l[r]]=e[l[r]]);return n};let ey=[1,2,3,4,5],ev=l.forwardRef((e,t)=>{let{level:n=1}=e,r=eb(e,["level"]),o=ey.includes(n)?"h".concat(n):"h1";return l.createElement(eu,Object.assign({ref:t},r,{component:o}))});Y.Text=eg,Y.Link=ep,Y.Title=ev,Y.Paragraph=ef;let eh=Y}}]);