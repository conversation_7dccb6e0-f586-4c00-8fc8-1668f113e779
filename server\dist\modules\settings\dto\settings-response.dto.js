"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class SettingsResponseDto {
    id;
    key;
    value;
    description;
    type;
    createdAt;
    updatedAt;
}
exports.SettingsResponseDto = SettingsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置的唯一ID' }),
    __metadata("design:type", String)
], SettingsResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置键名' }),
    __metadata("design:type", String)
], SettingsResponseDto.prototype, "key", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置值' }),
    __metadata("design:type", String)
], SettingsResponseDto.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置描述', required: false }),
    __metadata("design:type", String)
], SettingsResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置类型', enum: ['string', 'number', 'boolean', 'url'] }),
    __metadata("design:type", String)
], SettingsResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2024-01-01 12:00:00' }),
    __metadata("design:type", String)
], SettingsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新时间', example: '2024-01-01 12:00:00' }),
    __metadata("design:type", String)
], SettingsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=settings-response.dto.js.map