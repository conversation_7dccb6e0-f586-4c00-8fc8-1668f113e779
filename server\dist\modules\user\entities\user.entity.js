"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSchema = exports.User = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const swagger_1 = require("@nestjs/swagger");
let User = class User {
    id;
    phone;
    openid;
    nickname;
    avatarUrl;
    unlockedLevels;
    completedLevelIds;
    totalGames;
    totalCompletions;
    lastPlayTime;
    isVip;
    vipExpiresAt;
    dailyUnlockLimit;
    dailyUnlockCount;
    dailyShared;
    lastPlayDate;
    totalShares;
    createdAt;
    updatedAt;
};
exports.User = User;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户的唯一ID（8位随机数字）', example: '12345678' }),
    (0, mongoose_1.Prop)({ required: true, unique: true, length: 8 }),
    __metadata("design:type", String)
], User.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户手机号', example: '13800138000' }),
    (0, mongoose_1.Prop)({ required: false, unique: true, sparse: true }),
    __metadata("design:type", String)
], User.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信用户的openid', required: false }),
    (0, mongoose_1.Prop)({ unique: true, sparse: true }),
    __metadata("design:type", String)
], User.prototype, "openid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户昵称', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], User.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户头像URL', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], User.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户当前已开启的关卡数', example: 5 }),
    (0, mongoose_1.Prop)({ default: 1 }),
    __metadata("design:type", Number)
], User.prototype, "unlockedLevels", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户已通关的关卡ID列表', type: [String] }),
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], User.prototype, "completedLevelIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户总游戏次数' }),
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "totalGames", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户总通关次数' }),
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "totalCompletions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后游戏时间' }),
    (0, mongoose_1.Prop)({ default: Date.now }),
    __metadata("design:type", Date)
], User.prototype, "lastPlayTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP状态', example: false }),
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "isVip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP过期时间', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], User.prototype, "vipExpiresAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日解锁限制次数', example: 15 }),
    (0, mongoose_1.Prop)({ default: 15 }),
    __metadata("design:type", Number)
], User.prototype, "dailyUnlockLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当日解锁次数', example: 3 }),
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "dailyUnlockCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当日是否已分享', example: false }),
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "dailyShared", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后游戏日期（YYYY-MM-DD）', example: '2025-06-19' }),
    (0, mongoose_1.Prop)({ default: () => new Date().toISOString().split('T')[0] }),
    __metadata("design:type", String)
], User.prototype, "lastPlayDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总分享次数', example: 5 }),
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "totalShares", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户注册时间' }),
    __metadata("design:type", Date)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户信息最后更新时间' }),
    __metadata("design:type", Date)
], User.prototype, "updatedAt", void 0);
exports.User = User = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], User);
exports.UserSchema = mongoose_1.SchemaFactory.createForClass(User);
//# sourceMappingURL=user.entity.js.map