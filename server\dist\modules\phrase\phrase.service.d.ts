import { Model } from 'mongoose';
import { CreatePhraseDto } from './dto/create-phrase.dto';
import { UpdatePhraseDto } from './dto/update-phrase.dto';
import { PhraseDocument } from './entities/phrase.entity';
import { PhraseResponseDto } from './dto/phrase-response.dto';
export declare class PhraseService {
    private phraseModel;
    constructor(phraseModel: Model<PhraseDocument>);
    private _findEntityById;
    private _mapToPhraseResponseDto;
    create(createPhraseDto: CreatePhraseDto): Promise<PhraseResponseDto>;
    findAll(): Promise<PhraseResponseDto[]>;
    findOne(id: string): Promise<PhraseResponseDto>;
    update(id: string, updatePhraseDto: UpdatePhraseDto): Promise<PhraseResponseDto>;
    remove(id: string): Promise<void>;
}
