"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsSchema = exports.Settings = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const swagger_1 = require("@nestjs/swagger");
let Settings = class Settings {
    id;
    key;
    value;
    description;
    type;
    createdAt;
    updatedAt;
};
exports.Settings = Settings;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置的唯一ID' }),
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], Settings.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置键名' }),
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], Settings.prototype, "key", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置值' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Settings.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置描述', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Settings.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置类型', enum: ['string', 'number', 'boolean', 'url'] }),
    (0, mongoose_1.Prop)({ required: true, enum: ['string', 'number', 'boolean', 'url'], default: 'string' }),
    __metadata("design:type", String)
], Settings.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间 (Date Object)' }),
    __metadata("design:type", Date)
], Settings.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新时间 (Date Object)' }),
    __metadata("design:type", Date)
], Settings.prototype, "updatedAt", void 0);
exports.Settings = Settings = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Settings);
exports.SettingsSchema = mongoose_1.SchemaFactory.createForClass(Settings);
//# sourceMappingURL=settings.entity.js.map