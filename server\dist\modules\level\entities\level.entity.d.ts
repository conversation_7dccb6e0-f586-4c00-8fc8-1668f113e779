import { Document } from 'mongoose';
export type LevelDocument = Level & Document;
export declare class Level {
    id: string;
    name: string;
    difficulty: number;
    description?: string;
    thesaurusIds: string[];
    phraseIds: string[];
    createdAt: Date;
    updatedAt: Date;
}
export declare const LevelSchema: import("mongoose").Schema<Level, import("mongoose").Model<Level, any, any, any, Document<unknown, any, Level, any> & Level & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Level, Document<unknown, {}, import("mongoose").FlatRecord<Level>, {}> & import("mongoose").FlatRecord<Level> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
export type LevelEntity = Level;
