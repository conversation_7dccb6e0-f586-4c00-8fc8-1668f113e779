(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7040],{29799:(e,t,a)=>{"use strict";a.d(t,{F:()=>n});var r=a(23464),s=a(19868);let l={BASE_URL:"http://127.0.0.1:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}},i=r.A.create({baseURL:l.BASE_URL,timeout:l.TIMEOUT,headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>{let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),i.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){let{status:t,data:a}=e.response;switch(t){case 401:s.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:s.Ay.error("没有权限访问该资源");break;case 404:s.Ay.error("请求的资源不存在");break;case 500:s.Ay.error("服务器内部错误");break;default:s.Ay.error((null==a?void 0:a.message)||"请求失败")}}else e.request?s.Ay.error("网络连接失败，请检查网络"):s.Ay.error("请求配置错误");return Promise.reject(e)});let n={get:(e,t)=>i.get(e,t),post:(e,t,a)=>i.post(e,t,a),put:(e,t,a)=>i.put(e,t,a),patch:(e,t,a)=>i.patch(e,t,a),delete:(e,t)=>i.delete(e,t)}},39362:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>_});var r=a(95155),s=a(12115),l=a(97605),i=a(56020),n=a(20778),c=a(44670),d=a(19868),o=a(26922),h=a(37974),u=a(12320),p=a(30662),A=a(27212),x=a(505),y=a(19361),g=a(74947),m=a(51087),v=a(32814),j=a(10642),f=a(13324),w=a(90765),b=a(63625),S=a(79659),C=a(56170),k=a(44318),I=a(46996),F=a(73884);let{Title:z,Text:L}=l.A,{TextArea:E}=i.A,{Option:O}=n.A;function _(){let[e,t]=(0,s.useState)([]),[a,l]=(0,s.useState)(!1),[_,R]=(0,s.useState)(!1),[U,B]=(0,s.useState)(null),[D]=c.A.useForm(),T=async()=>{l(!0);try{let e=await F.Dw.getAllShareConfigs();t(e)}catch(e){d.Ay.error("获取分享配置失败"),console.error("获取分享配置失败:",e)}finally{l(!1)}};(0,s.useEffect)(()=>{T()},[]);let P=e=>{B(e||null),R(!0),e?D.setFieldsValue({name:e.name,title:e.title,path:e.path,imageUrl:e.imageUrl,description:e.description,type:e.type,isActive:e.isActive,sortOrder:e.sortOrder}):(D.resetFields(),D.setFieldsValue({type:"custom",isActive:!0,sortOrder:1}))},q=()=>{R(!1),B(null),D.resetFields()},M=async()=>{try{let e=await D.validateFields();U?(await F.Dw.updateShareConfig(U.id,e),d.Ay.success("分享配置更新成功")):(await F.Dw.createShareConfig(e),d.Ay.success("分享配置创建成功")),q(),T()}catch(e){if(e&&"object"==typeof e&&"errorFields"in e)d.Ay.error("请检查表单输入");else{let t=e&&"object"==typeof e&&"message"in e?e.message:"操作失败";d.Ay.error(t)}}},W=async e=>{try{await F.Dw.toggleShareConfig(e.id),d.Ay.success("".concat(e.isActive?"禁用":"启用","成功")),T()}catch(t){let e=t&&"object"==typeof t&&"message"in t?t.message:"操作失败";d.Ay.error(e)}},N=async e=>{try{await F.Dw.deleteShareConfig(e.id),d.Ay.success("删除成功"),T()}catch(t){let e=t&&"object"==typeof t&&"message"in t?t.message:"删除失败";d.Ay.error(e)}},V=e=>({default:"blue",result:"green",level:"orange",achievement:"purple",custom:"gray"})[e]||"gray",X=e=>{let t=F.WS.find(t=>t.value===e);return(null==t?void 0:t.label)||e},H=[{title:"配置名称",dataIndex:"name",key:"name",width:150,render:(e,t)=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{style:{fontWeight:500},children:e}),(0,r.jsxs)(L,{type:"secondary",style:{fontSize:"12px"},children:["ID: ",t.id]})]})},{title:"分享标题",dataIndex:"title",key:"title",width:200,render:e=>(0,r.jsx)(o.A,{title:e,children:(0,r.jsx)("div",{style:{maxWidth:"180px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e})})},{title:"分享路径",dataIndex:"path",key:"path",width:200,render:e=>(0,r.jsx)(o.A,{title:e,children:(0,r.jsx)(L,{code:!0,style:{maxWidth:"180px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",display:"block"},children:e})})},{title:"类型",dataIndex:"type",key:"type",width:100,render:e=>(0,r.jsx)(h.A,{color:V(e),children:X(e)})},{title:"状态",dataIndex:"isActive",key:"isActive",width:80,render:e=>(0,r.jsx)(h.A,{color:e?"success":"default",icon:e?(0,r.jsx)(w.A,{}):(0,r.jsx)(b.A,{}),children:e?"启用":"禁用"})},{title:"排序",dataIndex:"sortOrder",key:"sortOrder",width:80,align:"center"},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:150,render:e=>new Date(e).toLocaleString()},{title:"操作",key:"action",width:200,render:(e,t)=>(0,r.jsxs)(u.A,{size:"small",children:[(0,r.jsx)(o.A,{title:"编辑",children:(0,r.jsx)(p.Ay,{type:"text",icon:(0,r.jsx)(S.A,{}),onClick:()=>P(t)})}),(0,r.jsx)(o.A,{title:t.isActive?"禁用":"启用",children:(0,r.jsx)(p.Ay,{type:"text",icon:t.isActive?(0,r.jsx)(b.A,{}):(0,r.jsx)(w.A,{}),onClick:()=>W(t)})}),"default"!==t.type&&(0,r.jsx)(o.A,{title:"删除",children:(0,r.jsx)(A.A,{title:"确定要删除这个分享配置吗？",onConfirm:()=>N(t),okText:"确定",cancelText:"取消",children:(0,r.jsx)(p.Ay,{type:"text",danger:!0,icon:(0,r.jsx)(C.A,{})})})})]})}];return(0,r.jsxs)("div",{style:{padding:"24px"},children:[(0,r.jsxs)(x.A,{children:[(0,r.jsx)("div",{style:{marginBottom:"24px"},children:(0,r.jsxs)(y.A,{justify:"space-between",align:"middle",children:[(0,r.jsxs)(g.A,{children:[(0,r.jsxs)(z,{level:3,style:{margin:0},children:[(0,r.jsx)(k.A,{style:{marginRight:"8px"}}),"分享管理"]}),(0,r.jsx)(L,{type:"secondary",children:"管理微信小程序的分享配置，包括分享标题、路径和图片等"})]}),(0,r.jsx)(g.A,{children:(0,r.jsx)(p.Ay,{type:"primary",icon:(0,r.jsx)(I.A,{}),onClick:()=>P(),children:"新建分享配置"})})]})}),(0,r.jsx)(m.A,{columns:H,dataSource:e,rowKey:"id",loading:a,pagination:{total:e.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"共 ".concat(e," 条记录")},scroll:{x:1200}})]}),(0,r.jsx)(v.A,{title:U?"编辑分享配置":"新建分享配置",open:_,onOk:M,onCancel:q,width:600,destroyOnClose:!0,children:(0,r.jsxs)(c.A,{form:D,layout:"vertical",initialValues:{type:"custom",isActive:!0,sortOrder:1},children:[(0,r.jsx)(c.A.Item,{name:"name",label:"配置名称",rules:[{required:!0,message:"请输入配置名称"}],children:(0,r.jsx)(i.A,{placeholder:"请输入配置名称"})}),(0,r.jsx)(c.A.Item,{name:"title",label:"分享标题",rules:[{required:!0,message:"请输入分享标题"}],children:(0,r.jsx)(i.A,{placeholder:"请输入分享标题"})}),(0,r.jsx)(c.A.Item,{name:"path",label:"分享路径",rules:[{required:!0,message:"请输入分享路径"}],children:(0,r.jsx)(n.A,{placeholder:"请选择或输入分享路径",mode:"tags",allowClear:!0,children:F.cm.map(e=>(0,r.jsx)(O,{value:e.value,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:e.label}),(0,r.jsx)(L,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})}),(0,r.jsx)(c.A.Item,{name:"imageUrl",label:"分享图片URL",rules:[{type:"url",message:"请输入有效的URL"}],children:(0,r.jsx)(i.A,{placeholder:"请输入分享图片URL（可选）"})}),(0,r.jsx)(c.A.Item,{name:"description",label:"分享描述",children:(0,r.jsx)(E,{placeholder:"请输入分享描述（可选）",rows:3,maxLength:200,showCount:!0})}),(0,r.jsxs)(y.A,{gutter:16,children:[(0,r.jsx)(g.A,{span:12,children:(0,r.jsx)(c.A.Item,{name:"type",label:"分享类型",rules:[{required:!0,message:"请选择分享类型"}],children:(0,r.jsx)(n.A,{placeholder:"请选择分享类型",children:F.WS.map(e=>(0,r.jsx)(O,{value:e.value,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:e.label}),(0,r.jsx)(L,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})})}),(0,r.jsx)(g.A,{span:12,children:(0,r.jsx)(c.A.Item,{name:"sortOrder",label:"排序权重",rules:[{required:!0,message:"请输入排序权重"}],children:(0,r.jsx)(j.A,{min:1,max:999,placeholder:"排序权重",style:{width:"100%"}})})})]}),(0,r.jsx)(c.A.Item,{name:"isActive",label:"启用状态",valuePropName:"checked",children:(0,r.jsx)(f.A,{checkedChildren:"启用",unCheckedChildren:"禁用"})})]})})]})}},44318:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(79630),s=a(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M752 664c-28.5 0-54.8 10-75.4 26.7L469.4 540.8a160.68 160.68 0 000-57.6l207.2-149.9C697.2 350 723.5 360 752 360c66.2 0 120-53.8 120-120s-53.8-120-120-120-120 53.8-120 120c0 11.6 1.6 22.7 4.7 33.3L439.9 415.8C410.7 377.1 364.3 352 312 352c-88.4 0-160 71.6-160 160s71.6 160 160 160c52.3 0 98.7-25.1 127.9-63.8l196.8 142.5c-3.1 10.6-4.7 21.8-4.7 33.3 0 66.2 53.8 120 120 120s120-53.8 120-120-53.8-120-120-120zm0-476c28.7 0 52 23.3 52 52s-23.3 52-52 52-52-23.3-52-52 23.3-52 52-52zM312 600c-48.5 0-88-39.5-88-88s39.5-88 88-88 88 39.5 88 88-39.5 88-88 88zm440 236c-28.7 0-52-23.3-52-52s23.3-52 52-52 52 23.3 52 52-23.3 52-52 52z"}}]},name:"share-alt",theme:"outlined"};var i=a(62764);let n=s.forwardRef(function(e,t){return s.createElement(i.A,(0,r.A)({},e,{ref:t,icon:l}))})},63625:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(79630),s=a(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};var i=a(62764);let n=s.forwardRef(function(e,t){return s.createElement(i.A,(0,r.A)({},e,{ref:t,icon:l}))})},73884:(e,t,a)=>{"use strict";a.d(t,{Dw:()=>s,WS:()=>l,cm:()=>i});var r=a(29799);class s{static async getAllShareConfigs(){return(await r.F.get("/api/v1/share")).data}static async getActiveShareConfigs(){return(await r.F.get("/api/v1/share/active")).data}static async getDefaultShareConfig(){return(await r.F.get("/api/v1/share/default")).data}static async getShareConfigByType(e){return(await r.F.get("/api/v1/share/type/".concat(e))).data}static async getShareConfigById(e){return(await r.F.get("/api/v1/share/".concat(e))).data}static async createShareConfig(e){return(await r.F.post("/api/v1/share",e)).data}static async updateShareConfig(e,t){return(await r.F.patch("/api/v1/share/".concat(e),t)).data}static async toggleShareConfig(e){return(await r.F.put("/api/v1/share/".concat(e,"/toggle"))).data}static async deleteShareConfig(e){return(await r.F.delete("/api/v1/share/".concat(e))).data}}let l=[{value:"default",label:"默认分享",description:"通用分享，适用于首页、游戏页等"},{value:"result",label:"结果分享",description:"展示用户成绩的分享"},{value:"level",label:"关卡分享",description:"邀请好友挑战特定关卡"},{value:"achievement",label:"成就分享",description:"展示用户获得的成就"},{value:"custom",label:"自定义分享",description:"特殊活动或自定义场景"}],i=[{label:"首页",value:"/pages/index/index",description:"小程序首页"},{label:"游戏页",value:"/pages/game/game",description:"游戏主页面"},{label:"关卡页",value:"/pages/level/level?id={levelId}",description:"特定关卡页面，{levelId}会被替换为实际关卡ID"},{label:"结果页",value:"/pages/result/result?score={score}",description:"结果展示页面，{score}会被替换为实际分数"},{label:"排行榜",value:"/pages/rank/rank",description:"排行榜页面"}]},81131:(e,t,a)=>{Promise.resolve().then(a.bind(a,39362))},90765:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(79630),s=a(12115);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var i=a(62764);let n=s.forwardRef(function(e,t){return s.createElement(i.A,(0,r.A)({},e,{ref:t,icon:l}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[8340,547,7469,7497,44,6312,778,7605,4670,2343,3726,4124,7238,545,8441,1684,7358],()=>t(81131)),_N_E=e.O()}]);