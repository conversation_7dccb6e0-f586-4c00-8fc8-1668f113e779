"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminUserSchema = exports.AdminUser = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const swagger_1 = require("@nestjs/swagger");
let AdminUser = class AdminUser {
    userId;
    username;
    password;
    roles;
    isActive;
    lastLoginAt;
    createdAt;
    updatedAt;
};
exports.AdminUser = AdminUser;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '管理员用户ID', example: 'admin001' }),
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], AdminUser.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户名', example: 'admin' }),
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], AdminUser.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '密码哈希值' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], AdminUser.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户角色', example: ['admin'] }),
    (0, mongoose_1.Prop)({ type: [String], default: ['admin'] }),
    __metadata("design:type", Array)
], AdminUser.prototype, "roles", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活', example: true }),
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], AdminUser.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后登录时间' }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], AdminUser.prototype, "lastLoginAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], AdminUser.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], AdminUser.prototype, "updatedAt", void 0);
exports.AdminUser = AdminUser = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], AdminUser);
exports.AdminUserSchema = mongoose_1.SchemaFactory.createForClass(AdminUser);
//# sourceMappingURL=admin-user.entity.js.map