export declare function generateVipPackageId(): string;
export declare function generateShortVipPackageId(): string;
export declare function generateSemanticVipPackageId(name: string, duration: number): string;
export declare function generatePaymentOrderNo(): string;
export declare function generateUUID(): string;
export declare function generateShortUUID(): string;
export declare function generateNumericId(): string;
export declare function generateUniqueId(checkFn: (id: string) => Promise<boolean>, generateFn: () => string, maxRetries?: number): Promise<string>;
