"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinCompleteLevelResponseDto = exports.WeixinCompleteLevelDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class WeixinCompleteLevelDto {
    openid;
    levelId;
}
exports.WeixinCompleteLevelDto = WeixinCompleteLevelDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信用户openid', example: 'wx_openid_123456' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: 'openid不能为空' }),
    __metadata("design:type", String)
], WeixinCompleteLevelDto.prototype, "openid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡ID', example: 'level-uuid-1' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '关卡ID不能为空' }),
    __metadata("design:type", String)
], WeixinCompleteLevelDto.prototype, "levelId", void 0);
class WeixinCompleteLevelResponseDto {
    message;
    userId;
    levelId;
    unlockedLevels;
    totalCompletions;
    hasUnlockedNewLevel;
    dailyUnlockCount;
    dailyUnlockLimit;
    remainingUnlocks;
    isVip;
}
exports.WeixinCompleteLevelResponseDto = WeixinCompleteLevelResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应消息', example: '恭喜！关卡通关成功' }),
    __metadata("design:type", String)
], WeixinCompleteLevelResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: '12345678' }),
    __metadata("design:type", String)
], WeixinCompleteLevelResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '完成的关卡ID', example: 'level-uuid-1' }),
    __metadata("design:type", String)
], WeixinCompleteLevelResponseDto.prototype, "levelId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '新解锁的关卡数', example: 2 }),
    __metadata("design:type", Number)
], WeixinCompleteLevelResponseDto.prototype, "unlockedLevels", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总通关次数', example: 5 }),
    __metadata("design:type", Number)
], WeixinCompleteLevelResponseDto.prototype, "totalCompletions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否解锁了新关卡', example: true }),
    __metadata("design:type", Boolean)
], WeixinCompleteLevelResponseDto.prototype, "hasUnlockedNewLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当日解锁次数', example: 3, required: false }),
    __metadata("design:type", Number)
], WeixinCompleteLevelResponseDto.prototype, "dailyUnlockCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日解锁限制', example: 15, required: false }),
    __metadata("design:type", Number)
], WeixinCompleteLevelResponseDto.prototype, "dailyUnlockLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '剩余解锁次数', example: 12, required: false }),
    __metadata("design:type", Number)
], WeixinCompleteLevelResponseDto.prototype, "remainingUnlocks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否为VIP用户', example: false, required: false }),
    __metadata("design:type", Boolean)
], WeixinCompleteLevelResponseDto.prototype, "isVip", void 0);
//# sourceMappingURL=weixin-complete-level.dto.js.map