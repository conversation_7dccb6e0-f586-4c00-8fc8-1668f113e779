(()=>{var e={};e.id=630,e.ids=[630],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12299:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=r(65239),n=r(48088),i=r(88170),a=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["(admin)",{children:["phrases",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,60096)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\phrases\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\phrases\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(admin)/phrases/page",pathname:"/phrases",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22666:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(60687),n=r(43210),i=r(94733),a=r(28173),o=r(35899),d=r(52378),l=r(48111),c=r(21411),p=r(94132),u=r(11585),h=r(27783),x=r(7872),m=r(34308),y=r(23575),g=r(53082),A=r(23870);let{TextArea:b}=i.A;function f(){let[e,t]=(0,n.useState)([]),[r,f]=(0,n.useState)(!1),[j,v]=(0,n.useState)(!1),[w,q]=(0,n.useState)(null),[P]=a.A.useForm(),k=async()=>{f(!0);try{let e=await A.LY.getAll();t(e)}catch(e){o.Ay.error("获取词组列表失败")}finally{f(!1)}},_=async e=>{try{let t=e.tags?e.tags.split(",").map(e=>e.trim()).filter(Boolean):[],r={...e,tags:t};w?(await A.LY.update(w.id,r),o.Ay.success("词组更新成功")):(await A.LY.create(r),o.Ay.success("词组创建成功")),v(!1),q(null),P.resetFields(),k()}catch(e){o.Ay.error(w?"更新词组失败":"创建词组失败")}},C=async e=>{try{await A.LY.delete(e),o.Ay.success("词组删除成功"),k()}catch(e){o.Ay.error("删除词组失败")}},I=e=>{q(e),P.setFieldsValue({...e,tags:e.tags?.join(", ")||""}),v(!0)},S=[{title:"词组",dataIndex:"text",key:"text",width:150},{title:"含义",dataIndex:"meaning",key:"meaning",width:200},{title:"示例",dataIndex:"exampleSentence",key:"exampleSentence",width:250,render:e=>e||"-"},{title:"标签",dataIndex:"tags",key:"tags",width:150,render:e=>(0,s.jsx)(s.Fragment,{children:e?.map(e=>(0,s.jsx)(d.A,{color:"blue",children:e},e))})},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:180},{title:"操作",key:"action",width:150,render:(e,t)=>(0,s.jsxs)(l.A,{size:"middle",children:[(0,s.jsx)(c.Ay,{type:"link",icon:(0,s.jsx)(m.A,{}),onClick:()=>I(t),children:"编辑"}),(0,s.jsx)(p.A,{title:"确定要删除这个词组吗？",onConfirm:()=>C(t.id),okText:"确定",cancelText:"取消",children:(0,s.jsx)(c.Ay,{type:"link",danger:!0,icon:(0,s.jsx)(y.A,{}),children:"删除"})})]})}];return(0,s.jsxs)("div",{children:[(0,s.jsxs)(u.A,{children:[(0,s.jsxs)("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,s.jsx)("h2",{children:"词组管理"}),(0,s.jsx)(c.Ay,{type:"primary",icon:(0,s.jsx)(g.A,{}),onClick:()=>{q(null),P.resetFields(),v(!0)},children:"创建词组"})]}),(0,s.jsx)(h.A,{columns:S,dataSource:e,rowKey:"id",loading:r,pagination:{total:e.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`}})]}),(0,s.jsx)(x.A,{title:w?"编辑词组":"创建词组",open:j,onCancel:()=>{v(!1),q(null),P.resetFields()},footer:null,width:600,children:(0,s.jsxs)(a.A,{form:P,layout:"vertical",onFinish:_,children:[(0,s.jsx)(a.A.Item,{name:"text",label:"词组",rules:[{required:!0,message:"请输入词组"}],children:(0,s.jsx)(i.A,{placeholder:"请输入词组"})}),(0,s.jsx)(a.A.Item,{name:"meaning",label:"含义",rules:[{required:!0,message:"请输入词组含义"}],children:(0,s.jsx)(b,{rows:3,placeholder:"请输入词组含义"})}),(0,s.jsx)(a.A.Item,{name:"exampleSentence",label:"示例",children:(0,s.jsx)(b,{rows:3,placeholder:"请输入使用示例（可选）"})}),(0,s.jsx)(a.A.Item,{name:"tags",label:"标签",help:"多个标签用逗号分隔",children:(0,s.jsx)(i.A,{placeholder:"例如：动物,植物,食物"})}),(0,s.jsx)(a.A.Item,{children:(0,s.jsxs)(l.A,{children:[(0,s.jsx)(c.Ay,{type:"primary",htmlType:"submit",children:w?"更新":"创建"}),(0,s.jsx)(c.Ay,{onClick:()=>{v(!1),q(null),P.resetFields()},children:"取消"})]})})]})})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60096:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\phrases\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\phrases\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69807:(e,t,r)=>{Promise.resolve().then(r.bind(r,60096))},74075:e=>{"use strict";e.exports=require("zlib")},78055:(e,t,r)=>{Promise.resolve().then(r.bind(r,22666))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,433,658,816,204,331,553,84,173,579,542,145,451],()=>r(12299));module.exports=s})();