import { Document } from 'mongoose';
export type AdminUserDocument = AdminUser & Document;
export declare class AdminUser {
    userId: string;
    username: string;
    password: string;
    roles: string[];
    isActive: boolean;
    lastLoginAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export declare const AdminUserSchema: import("mongoose").Schema<AdminUser, import("mongoose").Model<AdminUser, any, any, any, Document<unknown, any, AdminUser, any> & AdminUser & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, AdminUser, Document<unknown, {}, import("mongoose").FlatRecord<AdminUser>, {}> & import("mongoose").FlatRecord<AdminUser> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
