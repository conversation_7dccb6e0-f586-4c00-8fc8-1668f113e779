exports.id=756,exports.ids=[756],exports.modules={10814:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},11235:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},16603:(e,t,a)=>{Promise.resolve().then(a.bind(a,65266)),Promise.resolve().then(a.t.bind(a,28087,23))},24600:(e,t,a)=>{Promise.resolve().then(a.bind(a,10814))},26323:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var s=a(80828),r=a(43210);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};var n=a(21898);let l=r.forwardRef(function(e,t){return r.createElement(n.A,(0,s.A)({},e,{ref:t,icon:i}))})},35692:()=>{},37912:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>F});var s=a(60687),r=a(43210),i=a(85814),n=a.n(i),l=a(16189),o=a(98836),c=a(99053),d=a(63736),h=a(56072),p=a(78620),u=a(60203),m=a(53788),v=a(28859),g=a(81945),b=a(3788),f=a(73237),y=a(94858),x=a(47453),A=a(72061),j=a(80461),k=a(71103);let{Header:P,Content:w,Sider:S,Footer:C}=o.A,I=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,s.jsx)(u.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,s.jsx)(m.A,{})},{key:"phrases",label:"词组管理",path:"/phrases",icon:(0,s.jsx)(v.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,s.jsx)(g.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,s.jsx)(b.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,s.jsx)(f.A,{})},{key:"vip-users",label:"VIP用户管理",path:"/vip-users",icon:(0,s.jsx)(y.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,s.jsx)(x.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,s.jsx)(A.A,{})}];function F({children:e}){let t=(0,l.useRouter)(),a=(0,l.usePathname)(),[i,u]=(0,r.useState)(!1),m=[{key:"logout",icon:(0,s.jsx)(j.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),t.push("/login")}}],v=I.find(e=>a.startsWith(e.path))?.key||"dashboard";return(0,s.jsxs)(o.A,{style:{minHeight:"100vh"},children:[(0,s.jsxs)(S,{collapsible:!0,collapsed:i,onCollapse:e=>u(e),children:[(0,s.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,s.jsx)(c.A.Text,{style:{color:"white",fontSize:i?"10px":"16px",transition:"font-size 0.2s"},children:i?"后台":"游戏管理后台"})}),(0,s.jsx)(d.A,{theme:"dark",selectedKeys:[v],mode:"inline",items:I.map(e=>({key:e.key,icon:e.icon,label:(0,s.jsx)(n(),{href:e.path,children:e.label})}))})]}),(0,s.jsxs)(o.A,{children:[(0,s.jsxs)(P,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,s.jsx)(h.A,{menu:{items:m},placement:"bottomRight",children:(0,s.jsx)(p.A,{style:{cursor:"pointer"},icon:(0,s.jsx)(k.A,{})})}),(0,s.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,s.jsx)(w,{style:{margin:"16px"},children:e}),(0,s.jsxs)(C,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},43910:(e,t,a)=>{"use strict";a.d(t,{Dw:()=>r,WS:()=>i,cm:()=>n});var s=a(98501);class r{static async getAllShareConfigs(){return(await s.F.get("/api/v1/share")).data}static async getActiveShareConfigs(){return(await s.F.get("/api/v1/share/active")).data}static async getDefaultShareConfig(){return(await s.F.get("/api/v1/share/default")).data}static async getShareConfigByType(e){return(await s.F.get(`/api/v1/share/type/${e}`)).data}static async getShareConfigById(e){return(await s.F.get(`/api/v1/share/${e}`)).data}static async createShareConfig(e){return(await s.F.post("/api/v1/share",e)).data}static async updateShareConfig(e,t){return(await s.F.patch(`/api/v1/share/${e}`,t)).data}static async toggleShareConfig(e){return(await s.F.put(`/api/v1/share/${e}/toggle`)).data}static async deleteShareConfig(e){return(await s.F.delete(`/api/v1/share/${e}`)).data}}let i=[{value:"default",label:"默认分享",description:"通用分享，适用于首页、游戏页等"},{value:"result",label:"结果分享",description:"展示用户成绩的分享"},{value:"level",label:"关卡分享",description:"邀请好友挑战特定关卡"},{value:"achievement",label:"成就分享",description:"展示用户获得的成就"},{value:"custom",label:"自定义分享",description:"特殊活动或自定义场景"}],n=[{label:"首页",value:"/pages/index/index",description:"小程序首页"},{label:"游戏页",value:"/pages/game/game",description:"游戏主页面"},{label:"关卡页",value:"/pages/level/level?id={levelId}",description:"特定关卡页面，{levelId}会被替换为实际关卡ID"},{label:"结果页",value:"/pages/result/result?score={score}",description:"结果展示页面，{score}会被替换为实际分数"},{label:"排行榜",value:"/pages/rank/rank",description:"排行榜页面"}]},53395:(e,t,a)=>{Promise.resolve().then(a.bind(a,6468)),Promise.resolve().then(a.bind(a,43741))},56306:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var s=a(80828),r=a(43210);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var n=a(21898);let l=r.forwardRef(function(e,t){return r.createElement(n.A,(0,s.A)({},e,{ref:t,icon:i}))})},59448:(e,t,a)=>{Promise.resolve().then(a.bind(a,37912))},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},76891:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(37413);a(61120);var r=a(68016);a(35692),a(28087);let i=({children:e})=>(0,s.jsxs)("html",{lang:"en",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("meta",{charSet:"utf-8"}),(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,s.jsx)("meta",{name:"description",content:"游戏后台管理系统"}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,s.jsx)("title",{children:"游戏管理后台"})]}),(0,s.jsx)("body",{children:(0,s.jsx)(r.Z,{children:e})})]})},98501:(e,t,a)=>{"use strict";a.d(t,{F:()=>l});var s=a(51060),r=a(35899);let i={BASE_URL:"http://localhost:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return`${this.BASE_URL}${this.API_PREFIX}`}},n=s.A.create({baseURL:i.BASE_URL,timeout:i.TIMEOUT,headers:{"Content-Type":"application/json"}});n.interceptors.request.use(e=>{let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),n.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){let{status:t,data:a}=e.response;switch(t){case 401:r.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:r.Ay.error("没有权限访问该资源");break;case 404:r.Ay.error("请求的资源不存在");break;case 500:r.Ay.error("服务器内部错误");break;default:r.Ay.error(a?.message||"请求失败")}}else e.request?r.Ay.error("网络连接失败，请检查网络"):r.Ay.error("请求配置错误");return Promise.reject(e)});let l={get:(e,t)=>n.get(e,t),post:(e,t,a)=>n.post(e,t,a),put:(e,t,a)=>n.put(e,t,a),patch:(e,t,a)=>n.patch(e,t,a),delete:(e,t)=>n.delete(e,t)}}};