"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinGlobalConfigDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class WeixinGlobalConfigDto {
    appId;
    isConfigured;
    paymentOrderExpireMinutes;
    environment;
    timestamp;
    helpUrl;
    backgroundMusicUrl;
    version;
    features;
    gameConfig;
}
exports.WeixinGlobalConfigDto = WeixinGlobalConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '微信小程序AppID',
        example: 'wx1234567890abcdef',
        required: true
    }),
    __metadata("design:type", String)
], WeixinGlobalConfigDto.prototype, "appId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '微信配置是否完整',
        example: true,
        required: true
    }),
    __metadata("design:type", Boolean)
], WeixinGlobalConfigDto.prototype, "isConfigured", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '支付订单过期时间（分钟）',
        example: 30,
        required: true
    }),
    __metadata("design:type", Number)
], WeixinGlobalConfigDto.prototype, "paymentOrderExpireMinutes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '当前环境',
        example: 'development',
        enum: ['development', 'production', 'test'],
        required: true
    }),
    __metadata("design:type", String)
], WeixinGlobalConfigDto.prototype, "environment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '服务器时间戳',
        example: '2024-01-01T12:00:00.000Z',
        required: true
    }),
    __metadata("design:type", String)
], WeixinGlobalConfigDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '帮助页面链接',
        example: 'https://help.example.com',
        required: true
    }),
    __metadata("design:type", String)
], WeixinGlobalConfigDto.prototype, "helpUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '背景音乐链接',
        example: 'https://music.example.com/background.mp3',
        required: true
    }),
    __metadata("design:type", String)
], WeixinGlobalConfigDto.prototype, "backgroundMusicUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '应用版本信息',
        example: '1.0.0',
        required: false
    }),
    __metadata("design:type", String)
], WeixinGlobalConfigDto.prototype, "version", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '功能开关配置',
        example: {
            enablePayment: true,
            enableShare: true,
            enableVip: true,
            enableMusic: true
        },
        required: false
    }),
    __metadata("design:type", Object)
], WeixinGlobalConfigDto.prototype, "features", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '游戏配置',
        example: {
            maxLevels: 1000,
            dailyUnlockLimit: 15,
            shareRewardCount: 5
        },
        required: false
    }),
    __metadata("design:type", Object)
], WeixinGlobalConfigDto.prototype, "gameConfig", void 0);
//# sourceMappingURL=weixin-global-config.dto.js.map