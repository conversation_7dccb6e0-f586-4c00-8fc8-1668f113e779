"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThesaurusModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const thesaurus_service_1 = require("./thesaurus.service");
const thesaurus_controller_1 = require("./thesaurus.controller");
const phrase_module_1 = require("../phrase/phrase.module");
const thesaurus_entity_1 = require("./entities/thesaurus.entity");
let ThesaurusModule = class ThesaurusModule {
};
exports.ThesaurusModule = ThesaurusModule;
exports.ThesaurusModule = ThesaurusModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([{ name: thesaurus_entity_1.Thesaurus.name, schema: thesaurus_entity_1.ThesaurusSchema }]),
            (0, common_1.forwardRef)(() => phrase_module_1.PhraseModule),
        ],
        controllers: [thesaurus_controller_1.ThesaurusController],
        providers: [thesaurus_service_1.ThesaurusService],
        exports: [thesaurus_service_1.ThesaurusService],
    })
], ThesaurusModule);
//# sourceMappingURL=thesaurus.module.js.map