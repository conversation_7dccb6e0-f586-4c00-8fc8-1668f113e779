(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8630],{59953:(e,t,l)=>{Promise.resolve().then(l.bind(l,92472))},79659:(e,t,l)=>{"use strict";l.d(t,{A:()=>r});var a=l(79630),s=l(12115);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var i=l(62764);let r=s.forwardRef(function(e,t){return s.createElement(i.A,(0,a.A)({},e,{ref:t,icon:n}))})},92472:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>w});var a=l(95155),s=l(12115),n=l(56020),i=l(44670),r=l(19868),c=l(37974),d=l(12320),o=l(30662),h=l(27212),x=l(505),u=l(51087),m=l(32814),A=l(79659),y=l(56170),j=l(46996),p=l(49179);let{TextArea:g}=n.A;function w(){let[e,t]=(0,s.useState)([]),[l,w]=(0,s.useState)(!1),[f,k]=(0,s.useState)(!1),[v,b]=(0,s.useState)(null),[I]=i.A.useForm(),S=async()=>{w(!0);try{let e=await p.LY.getAll();t(e)}catch(e){r.Ay.error("获取词组列表失败")}finally{w(!1)}};(0,s.useEffect)(()=>{S()},[]);let C=async e=>{try{let t=e.tags?e.tags.split(",").map(e=>e.trim()).filter(Boolean):[],l={...e,tags:t};v?(await p.LY.update(v.id,l),r.Ay.success("词组更新成功")):(await p.LY.create(l),r.Ay.success("词组创建成功")),k(!1),b(null),I.resetFields(),S()}catch(e){r.Ay.error(v?"更新词组失败":"创建词组失败")}},F=async e=>{try{await p.LY.delete(e),r.Ay.success("词组删除成功"),S()}catch(e){r.Ay.error("删除词组失败")}},L=e=>{var t;b(e),I.setFieldsValue({...e,tags:(null==(t=e.tags)?void 0:t.join(", "))||""}),k(!0)},z=[{title:"词组",dataIndex:"text",key:"text",width:150},{title:"含义",dataIndex:"meaning",key:"meaning",width:200},{title:"示例",dataIndex:"exampleSentence",key:"exampleSentence",width:250,render:e=>e||"-"},{title:"标签",dataIndex:"tags",key:"tags",width:150,render:e=>(0,a.jsx)(a.Fragment,{children:null==e?void 0:e.map(e=>(0,a.jsx)(c.A,{color:"blue",children:e},e))})},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:180},{title:"操作",key:"action",width:150,render:(e,t)=>(0,a.jsxs)(d.A,{size:"middle",children:[(0,a.jsx)(o.Ay,{type:"link",icon:(0,a.jsx)(A.A,{}),onClick:()=>L(t),children:"编辑"}),(0,a.jsx)(h.A,{title:"确定要删除这个词组吗？",onConfirm:()=>F(t.id),okText:"确定",cancelText:"取消",children:(0,a.jsx)(o.Ay,{type:"link",danger:!0,icon:(0,a.jsx)(y.A,{}),children:"删除"})})]})}];return(0,a.jsxs)("div",{children:[(0,a.jsxs)(x.A,{children:[(0,a.jsxs)("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,a.jsx)("h2",{children:"词组管理"}),(0,a.jsx)(o.Ay,{type:"primary",icon:(0,a.jsx)(j.A,{}),onClick:()=>{b(null),I.resetFields(),k(!0)},children:"创建词组"})]}),(0,a.jsx)(u.A,{columns:z,dataSource:e,rowKey:"id",loading:l,pagination:{total:e.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"共 ".concat(e," 条记录")}})]}),(0,a.jsx)(m.A,{title:v?"编辑词组":"创建词组",open:f,onCancel:()=>{k(!1),b(null),I.resetFields()},footer:null,width:600,children:(0,a.jsxs)(i.A,{form:I,layout:"vertical",onFinish:C,children:[(0,a.jsx)(i.A.Item,{name:"text",label:"词组",rules:[{required:!0,message:"请输入词组"}],children:(0,a.jsx)(n.A,{placeholder:"请输入词组"})}),(0,a.jsx)(i.A.Item,{name:"meaning",label:"含义",rules:[{required:!0,message:"请输入词组含义"}],children:(0,a.jsx)(g,{rows:3,placeholder:"请输入词组含义"})}),(0,a.jsx)(i.A.Item,{name:"exampleSentence",label:"示例",children:(0,a.jsx)(g,{rows:3,placeholder:"请输入使用示例（可选）"})}),(0,a.jsx)(i.A.Item,{name:"tags",label:"标签",help:"多个标签用逗号分隔",children:(0,a.jsx)(n.A,{placeholder:"例如：动物,植物,食物"})}),(0,a.jsx)(i.A.Item,{children:(0,a.jsxs)(d.A,{children:[(0,a.jsx)(o.Ay,{type:"primary",htmlType:"submit",children:v?"更新":"创建"}),(0,a.jsx)(o.Ay,{onClick:()=>{k(!1),b(null),I.resetFields()},children:"取消"})]})})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8340,547,7469,7497,44,6312,778,4670,2343,3726,4124,7238,9179,8441,1684,7358],()=>t(59953)),_N_E=e.O()}]);