"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const swagger_1 = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const fs = require("fs");
const path = require("path");
const env_checker_1 = require("./config/env-checker");
async function bootstrap() {
    env_checker_1.EnvironmentChecker.checkEnvironmentConfig();
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    const debug = configService.get('server.debug');
    app.enableCors({
        origin: debug ? '*' : ['http://localhost:3000'],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        allowedHeaders: ['Content-Type', 'Authorization'],
    });
    app.setGlobalPrefix('api/v1');
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
    }));
    const config = new swagger_1.DocumentBuilder()
        .setTitle('小游戏 API 文档')
        .setDescription('小游戏项目后端接口定义')
        .setVersion('1.0')
        .addBearerAuth()
        .addTag('auth', '认证模块')
        .addTag('phrases', '词组管理模块')
        .addTag('thesauruses', '词库管理模块')
        .addTag('levels', '关卡管理模块')
        .addTag('users', '用户管理模块')
        .addTag('weixin', '微信小程序模块')
        .addTag('settings', '设置管理模块')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api-docs', app, document);
    const docsDir = path.join(process.cwd(), 'docs');
    if (!fs.existsSync(docsDir)) {
        fs.mkdirSync(docsDir, { recursive: true });
    }
    const swaggerJsonPath = path.join(docsDir, 'swagger-spec.json');
    fs.writeFileSync(swaggerJsonPath, JSON.stringify(document, null, 2));
    const port = configService.get('server.port') || 3001;
    const environment = configService.get('server.environment') || 'development';
    await app.listen(port, '0.0.0.0');
    env_checker_1.EnvironmentChecker.logStartupInfo();
    console.log(`🌐 Application is running on: ${await app.getUrl()}`);
    if (environment !== 'production') {
        console.log(`📚 Swagger UI available at ${await app.getUrl()}/api-docs`);
    }
    console.log(`📄 Swagger JSON specification exported to: ${path.resolve(swaggerJsonPath)}`);
}
bootstrap();
//# sourceMappingURL=main.js.map