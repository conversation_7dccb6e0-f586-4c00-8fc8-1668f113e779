// server/src/main.ts
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common'; // 导入 ValidationPipe
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import { EnvironmentChecker } from './config/env-checker';
// import { dump } from 'js-yaml'; // 如果需要 YAML 格式

async function bootstrap() {
  // 在应用启动前检查环境配置
  EnvironmentChecker.checkEnvironmentConfig();

  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  const debug = configService.get<string>('server.debug');

  // 启用 CORS
  app.enableCors({
    origin: debug ? '*' : ['http://localhost:3000'], // 允许admin项目的域名
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
  });

  // API 请求前缀，例如 /api/v1
  app.setGlobalPrefix('api/v1');

  // 全局启用 ValidationPipe 以便 DTO 验证生效
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // 自动移除 DTO 中未定义的属性
      forbidNonWhitelisted: true, // 如果存在未定义的属性，则抛出错误
      transform: true, // 自动转换负载类型 (例如 string to number)
    }),
  );

  const config = new DocumentBuilder()
    .setTitle('小游戏 API 文档')
    .setDescription('小游戏项目后端接口定义')
    .setVersion('1.0')
    .addBearerAuth() // 如果使用 JWT 等 Bearer Token 认证
    .addTag('auth', '认证模块') // 为认证模块添加中文描述
    .addTag('phrases', '词组管理模块') // 为词组模块添加中文描述
    .addTag('thesauruses', '词库管理模块') // 为词库模块添加中文描述
    .addTag('levels', '关卡管理模块') // 为关卡模块添加中文描述
    .addTag('users', '用户管理模块') // 为用户模块添加中文描述
    .addTag('weixin', '微信小程序模块') // 为微信小程序模块添加中文描述
    .addTag('settings', '设置管理模块') // 为设置模块添加中文描述
    .build();
  const document = SwaggerModule.createDocument(app, config);

  // 设置 Swagger UI 的访问路径，例如 /api-docs
  SwaggerModule.setup('api-docs', app, document);

  // 导出 Swagger JSON 到 server/docs 目录
  // 注意：这里的路径是相对于项目启动的根目录（通常是 server/）
  const docsDir = path.join(process.cwd(), 'docs'); // process.cwd() 指向项目根目录 (server/)
  if (!fs.existsSync(docsDir)) {
    fs.mkdirSync(docsDir, { recursive: true });
  }
  const swaggerJsonPath = path.join(docsDir, 'swagger-spec.json');
  fs.writeFileSync(swaggerJsonPath, JSON.stringify(document, null, 2));

  // 如果需要 YAML 格式
  // const swaggerYamlPath = path.join(docsDir, 'swagger-spec.yaml');
  // fs.writeFileSync(swaggerYamlPath, dump(document));

  const port = configService.get<number>('server.port') || 3001;
  const environment =
    configService.get<string>('server.environment') || 'development';

  await app.listen(port, '0.0.0.0');

  // 显示启动信息
  EnvironmentChecker.logStartupInfo();
  console.log(`🌐 Application is running on: ${await app.getUrl()}`);

  if (environment !== 'production') {
    console.log(`📚 Swagger UI available at ${await app.getUrl()}/api-docs`);
  }

  console.log(
    `📄 Swagger JSON specification exported to: ${path.resolve(swaggerJsonPath)}`,
  );
  // console.log(`Swagger YAML specification exported to: ${path.resolve(swaggerYamlPath)}`); // 如果导出 YAML
}
bootstrap();
