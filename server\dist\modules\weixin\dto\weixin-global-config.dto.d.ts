export declare class WeixinGlobalConfigDto {
    appId: string;
    isConfigured: boolean;
    paymentOrderExpireMinutes: number;
    environment: string;
    timestamp: string;
    helpUrl: string;
    backgroundMusicUrl: string;
    version?: string;
    features?: {
        enablePayment: boolean;
        enableShare: boolean;
        enableVip: boolean;
        enableMusic: boolean;
    };
    gameConfig?: {
        maxLevels: number;
        dailyUnlockLimit: number;
        shareRewardCount: number;
    };
}
