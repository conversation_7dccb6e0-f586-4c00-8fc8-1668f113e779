"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AppService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppService = void 0;
const common_1 = require("@nestjs/common");
const share_service_1 = require("./modules/share/share.service");
let AppService = AppService_1 = class AppService {
    shareService;
    logger = new common_1.Logger(AppService_1.name);
    constructor(shareService) {
        this.shareService = shareService;
    }
    async onModuleInit() {
        this.logger.log('🚀 应用初始化开始...');
        try {
            await this.shareService.initializeDefaultConfig();
            this.logger.log('✅ 应用初始化完成');
        }
        catch (error) {
            this.logger.error('❌ 应用初始化失败:', error);
        }
    }
    getHello() {
        return 'Hello World!';
    }
};
exports.AppService = AppService;
exports.AppService = AppService = AppService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [share_service_1.ShareService])
], AppService);
//# sourceMappingURL=app.service.js.map