"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const uuid_1 = require("uuid");
const settings_entity_1 = require("./entities/settings.entity");
const date_formatter_1 = require("../../common/utils/date-formatter");
let SettingsService = class SettingsService {
    settingsModel;
    constructor(settingsModel) {
        this.settingsModel = settingsModel;
    }
    async create(createSettingsDto) {
        const existingSettings = await this.settingsModel.findOne({ key: createSettingsDto.key }).exec();
        if (existingSettings) {
            throw new common_1.BadRequestException(`设置键名 "${createSettingsDto.key}" 已存在`);
        }
        const newSettings = new this.settingsModel({
            id: (0, uuid_1.v4)(),
            key: createSettingsDto.key,
            value: createSettingsDto.value,
            description: createSettingsDto.description,
            type: createSettingsDto.type,
        });
        const savedSettings = await newSettings.save();
        return this._mapToSettingsResponseDto(savedSettings);
    }
    async findAll() {
        const settings = await this.settingsModel.find().exec();
        return settings.map(setting => this._mapToSettingsResponseDto(setting));
    }
    async findOne(id) {
        const settings = await this.getSettingsEntity(id);
        return this._mapToSettingsResponseDto(settings);
    }
    async findByKey(key) {
        const settings = await this.settingsModel.findOne({ key }).exec();
        if (!settings) {
            throw new common_1.NotFoundException(`未找到键名为 "${key}" 的设置`);
        }
        return this._mapToSettingsResponseDto(settings);
    }
    async update(id, updateSettingsDto) {
        const settings = await this.getSettingsEntity(id);
        if (updateSettingsDto.key && updateSettingsDto.key !== settings.key) {
            const existingSettings = await this.settingsModel.findOne({ key: updateSettingsDto.key }).exec();
            if (existingSettings) {
                throw new common_1.BadRequestException(`设置键名 "${updateSettingsDto.key}" 已存在`);
            }
        }
        Object.assign(settings, updateSettingsDto);
        const updatedSettings = await settings.save();
        return this._mapToSettingsResponseDto(updatedSettings);
    }
    async updateByKey(key, value) {
        const settings = await this.settingsModel.findOne({ key }).exec();
        if (!settings) {
            throw new common_1.NotFoundException(`未找到键名为 "${key}" 的设置`);
        }
        settings.value = value;
        const updatedSettings = await settings.save();
        return this._mapToSettingsResponseDto(updatedSettings);
    }
    async remove(id) {
        const settings = await this.getSettingsEntity(id);
        await this.settingsModel.deleteOne({ id }).exec();
    }
    async getSettingsEntity(id) {
        const settings = await this.settingsModel.findOne({ id }).exec();
        if (!settings) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的设置`);
        }
        return settings;
    }
    async initializeDefaultSettings() {
        const defaultSettings = [
            {
                key: 'help_url',
                value: 'https://help.example.com',
                description: '帮助页面链接',
                type: 'url',
            },
            {
                key: 'background_music_url',
                value: 'https://music.example.com/background.mp3',
                description: '背景音乐链接',
                type: 'url',
            },
        ];
        for (const setting of defaultSettings) {
            const existing = await this.settingsModel.findOne({ key: setting.key }).exec();
            if (!existing) {
                const newSetting = new this.settingsModel({
                    id: (0, uuid_1.v4)(),
                    ...setting,
                });
                await newSetting.save();
                console.log(`✅ 初始化设置: ${setting.key} = ${setting.value}`);
            }
        }
    }
    async onModuleInit() {
        try {
            await this.initializeDefaultSettings();
            console.log('✅ 设置模块初始化完成');
        }
        catch (error) {
            console.error('❌ 设置模块初始化失败:', error);
        }
    }
    _mapToSettingsResponseDto(settings) {
        return {
            id: settings.id,
            key: settings.key,
            value: settings.value,
            description: settings.description,
            type: settings.type,
            createdAt: (0, date_formatter_1.formatDate)(settings.createdAt),
            updatedAt: (0, date_formatter_1.formatDate)(settings.updatedAt),
        };
    }
};
exports.SettingsService = SettingsService;
exports.SettingsService = SettingsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(settings_entity_1.Settings.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], SettingsService);
//# sourceMappingURL=settings.service.js.map