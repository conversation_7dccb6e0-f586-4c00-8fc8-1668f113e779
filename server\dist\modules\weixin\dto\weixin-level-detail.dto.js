"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinLevelDetailDto = exports.WeixinPhraseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class WeixinPhraseDto {
    id;
    text;
    meaning;
    exampleSentence;
    tags;
}
exports.WeixinPhraseDto = WeixinPhraseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词组ID', example: 'phrase-uuid-1' }),
    __metadata("design:type", String)
], WeixinPhraseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词组文本', example: 'Hello World' }),
    __metadata("design:type", String)
], WeixinPhraseDto.prototype, "text", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词组含义', example: '你好，世界' }),
    __metadata("design:type", String)
], WeixinPhraseDto.prototype, "meaning", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '例句', example: 'When you start programming, the first thing you often do is print "Hello World".', required: false }),
    __metadata("design:type", String)
], WeixinPhraseDto.prototype, "exampleSentence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签', example: ['greeting', 'common'], required: false, type: [String] }),
    __metadata("design:type", Array)
], WeixinPhraseDto.prototype, "tags", void 0);
class WeixinLevelDetailDto {
    id;
    name;
    difficulty;
    description;
    isUnlocked;
    isCompleted;
    phrases;
    createdAt;
}
exports.WeixinLevelDetailDto = WeixinLevelDetailDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡ID', example: 'level-uuid-1' }),
    __metadata("design:type", String)
], WeixinLevelDetailDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡名称', example: '第1关 - 基础词汇' }),
    __metadata("design:type", String)
], WeixinLevelDetailDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡难度', example: 1 }),
    __metadata("design:type", Number)
], WeixinLevelDetailDto.prototype, "difficulty", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡描述', example: '这是第一关，包含基础词汇' }),
    __metadata("design:type", String)
], WeixinLevelDetailDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否已解锁', example: true }),
    __metadata("design:type", Boolean)
], WeixinLevelDetailDto.prototype, "isUnlocked", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否已完成', example: false }),
    __metadata("design:type", Boolean)
], WeixinLevelDetailDto.prototype, "isCompleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡包含的词组列表', type: [WeixinPhraseDto] }),
    __metadata("design:type", Array)
], WeixinLevelDetailDto.prototype, "phrases", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡创建时间', example: '2025-06-18T10:00:00.000Z' }),
    __metadata("design:type", String)
], WeixinLevelDetailDto.prototype, "createdAt", void 0);
//# sourceMappingURL=weixin-level-detail.dto.js.map