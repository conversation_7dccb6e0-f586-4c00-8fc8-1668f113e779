"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinShareConfigListDto = exports.WeixinShareConfigDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class WeixinShareConfigDto {
    title;
    path;
    imageUrl;
    description;
    type;
}
exports.WeixinShareConfigDto = WeixinShareConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享标题', example: '一起来挑战词汇游戏！' }),
    __metadata("design:type", String)
], WeixinShareConfigDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享路径', example: '/pages/index/index' }),
    __metadata("design:type", String)
], WeixinShareConfigDto.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享图片URL', required: false }),
    __metadata("design:type", String)
], WeixinShareConfigDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享描述', required: false }),
    __metadata("design:type", String)
], WeixinShareConfigDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享类型', example: 'default' }),
    __metadata("design:type", String)
], WeixinShareConfigDto.prototype, "type", void 0);
class WeixinShareConfigListDto {
    default;
    configs;
    total;
}
exports.WeixinShareConfigListDto = WeixinShareConfigListDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '默认分享配置' }),
    __metadata("design:type", WeixinShareConfigDto)
], WeixinShareConfigListDto.prototype, "default", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '其他分享配置列表', type: [WeixinShareConfigDto] }),
    __metadata("design:type", Array)
], WeixinShareConfigListDto.prototype, "configs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总配置数量', example: 3 }),
    __metadata("design:type", Number)
], WeixinShareConfigListDto.prototype, "total", void 0);
//# sourceMappingURL=weixin-share-config.dto.js.map