import { AuthService } from './auth.service';
import { LoginAdminDto } from './dto/login-admin.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginAdminDto: LoginAdminDto): Promise<{
        message: string;
        accessToken: string;
        user: {
            userId: string;
            username: string;
            roles: string[];
        };
    }>;
}
