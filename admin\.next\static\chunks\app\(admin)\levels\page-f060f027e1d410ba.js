(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3987],{41552:(e,t,l)=>{Promise.resolve().then(l.bind(l,67465))},67465:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>C});var i=l(95155),s=l(12115),n=l(19868),r=l(32814),a=l(37974),d=l(12320),c=l(30662),h=l(27212),o=l(19361),x=l(74947),u=l(505),j=l(44297),y=l(20778),p=l(51087),A=l(52092),g=l(79659),f=l(56170),m=l(80392),k=l(46996),v=l(35695),w=l(49179);function C(){let[e,t]=(0,s.useState)([]),[l,C]=(0,s.useState)(!1),[b,I]=(0,s.useState)(null),[S,z]=(0,s.useState)(void 0),_=(0,v.useRouter)(),E=async()=>{C(!0);try{let e;e=S?await w.k3.getByDifficulty(S):await w.k3.getAll(),t(e)}catch(e){n.Ay.error("获取关卡列表失败")}finally{C(!1)}},T=async()=>{try{let e=await w.k3.getCount();I(e)}catch(e){n.Ay.error("获取关卡统计失败")}};(0,s.useEffect)(()=>{E(),T()},[S]);let B=async e=>{try{await w.k3.delete(e),n.Ay.success("关卡删除成功"),E(),T()}catch(e){n.Ay.error("删除关卡失败")}},N=async e=>{try{let t=await w.k3.getWithPhrases(e);r.A.info({title:"关卡详情 - ".concat(t.name),width:800,content:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"难度:"})," ",t.difficulty]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"描述:"})," ",t.description||"无"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"词库数量:"})," ",t.thesaurusIds.length]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"词组数量:"})," ",t.phrases.length]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"包含的词组:"}),(0,i.jsx)("div",{style:{marginTop:8,maxHeight:300,overflow:"auto"},children:t.phrases.map(e=>(0,i.jsx)(a.A,{style:{margin:4},children:e.text},e.id))})]})]})})}catch(e){n.Ay.error("获取关卡详情失败")}},O=e=>["","green","blue","orange","red","purple"][e]||"default",P=[{title:"关卡名称",dataIndex:"name",key:"name",width:200},{title:"难度",dataIndex:"difficulty",key:"difficulty",width:100,render:e=>(0,i.jsxs)(a.A,{color:O(e),children:[e," 级"]})},{title:"描述",dataIndex:"description",key:"description",width:250,render:e=>e||"-"},{title:"词库数量",dataIndex:"thesaurusIds",key:"thesaurusCount",width:100,render:e=>e.length},{title:"词组数量",dataIndex:"phraseIds",key:"phraseCount",width:100,render:e=>e.length},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:180},{title:"操作",key:"action",width:200,render:(e,t)=>(0,i.jsxs)(d.A,{size:"small",wrap:!0,children:[(0,i.jsx)(c.Ay,{type:"link",size:"small",icon:(0,i.jsx)(A.A,{}),onClick:()=>N(t.id),children:"详情"}),(0,i.jsx)(c.Ay,{type:"link",size:"small",icon:(0,i.jsx)(g.A,{}),onClick:()=>_.push("/levels/".concat(t.id,"/edit")),children:"编辑"}),(0,i.jsx)(h.A,{title:"确定要删除这个关卡吗？",onConfirm:()=>B(t.id),okText:"确定",cancelText:"取消",children:(0,i.jsx)(c.Ay,{type:"link",danger:!0,size:"small",icon:(0,i.jsx)(f.A,{}),children:"删除"})})]})}];return(0,i.jsxs)("div",{children:[b&&(0,i.jsxs)(o.A,{gutter:16,style:{marginBottom:16},children:[(0,i.jsx)(x.A,{span:8,children:(0,i.jsx)(u.A,{children:(0,i.jsx)(j.A,{title:"当前关卡总数",value:b.total,prefix:(0,i.jsx)(m.A,{})})})}),(0,i.jsx)(x.A,{span:8,children:(0,i.jsx)(u.A,{children:(0,i.jsx)(j.A,{title:"最大关卡限制",value:b.maxLevels,prefix:(0,i.jsx)(m.A,{})})})}),(0,i.jsx)(x.A,{span:8,children:(0,i.jsx)(u.A,{children:(0,i.jsx)(j.A,{title:"剩余可创建",value:b.remaining,prefix:(0,i.jsx)(m.A,{}),valueStyle:{color:b.remaining>0?"#3f8600":"#cf1322"}})})})]}),(0,i.jsxs)(u.A,{children:[(0,i.jsxs)("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,i.jsx)("h2",{children:"关卡管理"}),(0,i.jsxs)(d.A,{children:[(0,i.jsx)(y.A,{placeholder:"按难度筛选",allowClear:!0,style:{width:120},value:S,onChange:z,options:[{label:"1级",value:1},{label:"2级",value:2},{label:"3级",value:3},{label:"4级",value:4},{label:"5级",value:5}]}),(0,i.jsx)(c.Ay,{type:"primary",icon:(0,i.jsx)(k.A,{}),onClick:()=>_.push("/levels/create"),disabled:(null==b?void 0:b.remaining)===0,children:"创建关卡"})]})]}),(0,i.jsx)(p.A,{columns:P,dataSource:e,rowKey:"id",loading:l,pagination:{total:e.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"共 ".concat(e," 条记录")}})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8340,547,7469,6312,778,2343,3726,4124,7238,3800,9179,8441,1684,7358],()=>t(41552)),_N_E=e.O()}]);