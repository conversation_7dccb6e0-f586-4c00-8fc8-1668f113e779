{"version": 3, "file": "phrase-response.dto.js", "sourceRoot": "", "sources": ["../../../../src/modules/phrase/dto/phrase-response.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAE9C,MAAa,iBAAiB;IAE5B,EAAE,CAAS;IAGX,IAAI,CAAS;IAGb,OAAO,CAAS;IAOhB,eAAe,CAAU;IAGzB,IAAI,CAAY;IAGhB,SAAS,CAAS;IAGlB,SAAS,CAAS;CACnB;AAzBD,8CAyBC;AAvBC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;;6CAC9E;AAGX;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;;+CAChD;AAGb;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;;kDACvC;AAOhB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,kFAAkF;QAC3F,QAAQ,EAAE,KAAK;KAChB,CAAC;;0DACuB;AAGzB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;;+CACrF;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;;oDACnD;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;;oDACrD"}