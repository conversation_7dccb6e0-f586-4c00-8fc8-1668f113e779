"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhraseController = void 0;
const common_1 = require("@nestjs/common");
const phrase_service_1 = require("./phrase.service");
const create_phrase_dto_1 = require("./dto/create-phrase.dto");
const update_phrase_dto_1 = require("./dto/update-phrase.dto");
const swagger_1 = require("@nestjs/swagger");
const phrase_response_dto_1 = require("./dto/phrase-response.dto");
let PhraseController = class PhraseController {
    phraseService;
    constructor(phraseService) {
        this.phraseService = phraseService;
    }
    create(createPhraseDto) {
        return this.phraseService.create(createPhraseDto);
    }
    findAll() {
        return this.phraseService.findAll();
    }
    findOne(id) {
        return this.phraseService.findOne(id);
    }
    update(id, updatePhraseDto) {
        return this.phraseService.update(id, updatePhraseDto);
    }
    remove(id) {
        return this.phraseService.remove(id);
    }
};
exports.PhraseController = PhraseController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建新词组' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '词组创建成功', type: phrase_response_dto_1.PhraseResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_phrase_dto_1.CreatePhraseDto]),
    __metadata("design:returntype", void 0)
], PhraseController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有词组列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取词组列表', type: [phrase_response_dto_1.PhraseResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PhraseController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取单个词组' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取词组', type: phrase_response_dto_1.PhraseResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '词组未找到' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PhraseController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新指定ID的词组' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '词组更新成功', type: phrase_response_dto_1.PhraseResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '词组未找到' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_phrase_dto_1.UpdatePhraseDto]),
    __metadata("design:returntype", void 0)
], PhraseController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '删除指定ID的词组' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '词组删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '词组未找到' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PhraseController.prototype, "remove", null);
exports.PhraseController = PhraseController = __decorate([
    (0, swagger_1.ApiTags)('phrases'),
    (0, common_1.Controller)('phrases'),
    __metadata("design:paramtypes", [phrase_service_1.PhraseService])
], PhraseController);
//# sourceMappingURL=phrase.controller.js.map