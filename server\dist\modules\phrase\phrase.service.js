"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhraseService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const phrase_entity_1 = require("./entities/phrase.entity");
const uuid_1 = require("uuid");
const date_formatter_1 = require("../../common/utils/date-formatter");
let PhraseService = class PhraseService {
    phraseModel;
    constructor(phraseModel) {
        this.phraseModel = phraseModel;
    }
    async _findEntityById(id) {
        const phrase = await this.phraseModel.findOne({ id }).exec();
        if (!phrase) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的词组`);
        }
        return phrase;
    }
    _mapToPhraseResponseDto(phrase) {
        return {
            id: phrase.id,
            text: phrase.text,
            meaning: phrase.meaning,
            exampleSentence: phrase.exampleSentence,
            tags: phrase.tags,
            createdAt: (0, date_formatter_1.formatDate)(phrase.createdAt),
            updatedAt: (0, date_formatter_1.formatDate)(phrase.updatedAt),
        };
    }
    async create(createPhraseDto) {
        const newPhrase = new this.phraseModel({
            id: (0, uuid_1.v4)(),
            ...createPhraseDto,
        });
        const savedPhrase = await newPhrase.save();
        return this._mapToPhraseResponseDto(savedPhrase);
    }
    async findAll() {
        const phrases = await this.phraseModel.find().exec();
        return phrases.map(phrase => this._mapToPhraseResponseDto(phrase));
    }
    async findOne(id) {
        const phrase = await this._findEntityById(id);
        return this._mapToPhraseResponseDto(phrase);
    }
    async update(id, updatePhraseDto) {
        const updatedPhrase = await this.phraseModel.findOneAndUpdate({ id }, { ...updatePhraseDto, updatedAt: new Date() }, { new: true }).exec();
        if (!updatedPhrase) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的词组`);
        }
        return this._mapToPhraseResponseDto(updatedPhrase);
    }
    async remove(id) {
        const result = await this.phraseModel.deleteOne({ id }).exec();
        if (result.deletedCount === 0) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的词组`);
        }
    }
};
exports.PhraseService = PhraseService;
exports.PhraseService = PhraseService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(phrase_entity_1.Phrase.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], PhraseService);
//# sourceMappingURL=phrase.service.js.map