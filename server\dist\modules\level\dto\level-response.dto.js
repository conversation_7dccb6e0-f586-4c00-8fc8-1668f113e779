"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LevelResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class LevelResponseDto {
    id;
    name;
    difficulty;
    description;
    createdAt;
    updatedAt;
    thesaurusIds;
    phraseIds;
}
exports.LevelResponseDto = LevelResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡的唯一ID', example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], LevelResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡名称', example: '第一关：入门' }),
    __metadata("design:type", String)
], LevelResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡难度', example: 1 }),
    __metadata("design:type", Number)
], LevelResponseDto.prototype, "difficulty", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡描述', example: '这是教学关卡', required: false }),
    __metadata("design:type", String)
], LevelResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2023-10-27 10:30:00' }),
    __metadata("design:type", String)
], LevelResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新时间', example: '2023-10-27 10:35:00' }),
    __metadata("design:type", String)
], LevelResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关联的词库ID列表', type: [String], example: ['uuid1-thesaurus-xxx'] }),
    __metadata("design:type", Array)
], LevelResponseDto.prototype, "thesaurusIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡包含的词组ID列表', type: [String], example: ['uuid1-phrase-abc'] }),
    __metadata("design:type", Array)
], LevelResponseDto.prototype, "phraseIds", void 0);
//# sourceMappingURL=level-response.dto.js.map