import { LevelService } from './level.service';
import { CreateLevelDto } from './dto/create-level.dto';
import { UpdateLevelDto } from './dto/update-level.dto';
import { LevelResponseDto } from './dto/level-response.dto';
import { AddPhraseToLevelDto } from './dto/add-phrase-to-level.dto';
export declare class LevelController {
    private readonly levelService;
    constructor(levelService: LevelService);
    create(createLevelDto: CreateLevelDto): Promise<LevelResponseDto>;
    findAll(): Promise<LevelResponseDto[]>;
    getLevelCount(): Promise<{
        total: number;
        maxLevels: number;
        remaining: number;
    }>;
    findByDifficulty(difficulty: string): Promise<LevelResponseDto[]>;
    findOne(id: string): Promise<LevelResponseDto>;
    findOneWithPhrases(id: string): Promise<any>;
    update(id: string, updateLevelDto: UpdateLevelDto): Promise<LevelResponseDto>;
    remove(id: string): Promise<void>;
    addPhraseToLevel(levelId: string, addPhraseDto: AddPhraseToLevelDto): Promise<LevelResponseDto>;
    removePhraseFromLevel(levelId: string, phraseId: string): Promise<LevelResponseDto>;
}
