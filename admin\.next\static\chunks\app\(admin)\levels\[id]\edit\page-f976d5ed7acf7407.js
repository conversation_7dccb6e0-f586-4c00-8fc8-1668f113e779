(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5144],{16853:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>g});var t=l(95155),a=l(12115),r=l(35695),i=l(97605),n=l(56020),d=l(44670),c=l(19868),u=l(505),o=l(16467),h=l(95108),m=l(1828),p=l(94600),y=l(20778),A=l(12320),x=l(30662),j=l(49179);let{Title:f}=i.A,{TextArea:I}=n.A;function g(){let[e]=d.A.useForm(),s=(0,r.useRouter)(),l=(0,r.useParams)().id,[i,g]=(0,a.useState)(!1),[v,w]=(0,a.useState)(!0),[b,k]=(0,a.useState)([]),[C,_]=(0,a.useState)([]),[L,S]=(0,a.useState)(null),E=async()=>{try{let e=await j.Au.getAll();k(e)}catch(e){c.Ay.error("获取词库列表失败")}},O=async()=>{try{let e=await j.LY.getAll();_(e)}catch(e){c.Ay.error("获取词组列表失败")}},B=async()=>{try{let s=await j.k3.getById(l);S(s),e.setFieldsValue({name:s.name,difficulty:s.difficulty,description:s.description,thesaurusIds:s.thesaurusIds,phraseIds:s.phraseIds})}catch(e){c.Ay.error("获取关卡信息失败"),s.push("/levels")}};(0,a.useEffect)(()=>{let e=async()=>{w(!0),await Promise.all([E(),O(),B()]),w(!1)};l&&e()},[l]);let F=async e=>{if((!e.thesaurusIds||0===e.thesaurusIds.length)&&(!e.phraseIds||0===e.phraseIds.length))return void c.Ay.error("请至少选择词库或词组中的一种");g(!0);try{let t={name:e.name,difficulty:e.difficulty,description:e.description,thesaurusIds:e.thesaurusIds||[],phraseIds:e.phraseIds||[]};await j.k3.update(l,t),c.Ay.success("关卡更新成功"),s.push("/levels")}catch(e){c.Ay.error("更新关卡失败")}finally{g(!1)}};return v?(0,t.jsx)(u.A,{children:(0,t.jsxs)("div",{style:{textAlign:"center",padding:"50px 0"},children:[(0,t.jsx)(o.A,{size:"large"}),(0,t.jsx)("div",{style:{marginTop:16},children:"加载中..."})]})}):(0,t.jsx)("div",{children:(0,t.jsxs)(u.A,{children:[(0,t.jsx)(f,{level:2,children:"编辑关卡"}),L&&(0,t.jsx)(h.A,{message:"正在编辑关卡：".concat(L.name),description:"创建时间：".concat(L.createdAt,"，最后更新：").concat(L.updatedAt),type:"info",style:{marginBottom:16},showIcon:!0}),(0,t.jsxs)(d.A,{form:e,layout:"vertical",onFinish:F,children:[(0,t.jsx)(d.A.Item,{name:"name",label:"关卡名称",rules:[{required:!0,message:"请输入关卡名称"}],children:(0,t.jsx)(n.A,{placeholder:"例如：第1关 - 基础词汇"})}),(0,t.jsx)(d.A.Item,{name:"difficulty",label:"难度等级 (1-5)",rules:[{required:!0,message:"请选择关卡难度"}],children:(0,t.jsx)(m.A,{min:1,max:5,marks:{1:"简单",2:"容易",3:"中等",4:"困难",5:"极难"}})}),(0,t.jsx)(d.A.Item,{name:"description",label:"关卡描述",children:(0,t.jsx)(I,{rows:3,placeholder:"请输入关卡描述（可选）"})}),(0,t.jsx)(p.A,{children:"选择关卡内容"}),(0,t.jsx)(h.A,{message:"提示：您可以通过选择词库或直接选择词组来设置关卡内容，至少需要选择其中一种方式。",type:"info",style:{marginBottom:16}}),(0,t.jsx)(d.A.Item,{name:"thesaurusIds",label:"选择词库（推荐）",help:"选择词库后，关卡将包含该词库中的所有词组",children:(0,t.jsx)(y.A,{mode:"multiple",allowClear:!0,style:{width:"100%"},placeholder:"请选择要关联的词库",filterOption:(e,s)=>{var l;return null==s||null==(l=s.label)?void 0:l.toLowerCase().includes(e.toLowerCase())},options:b.map(e=>({label:e.name,value:e.id}))})}),(0,t.jsx)(d.A.Item,{name:"phraseIds",label:"直接选择词组",help:"如果不选择词库，可以直接选择具体的词组",children:(0,t.jsx)(y.A,{mode:"multiple",allowClear:!0,style:{width:"100%"},placeholder:"请选择要包含的词组",filterOption:(e,s)=>{var l;return null==s||null==(l=s.label)?void 0:l.toLowerCase().includes(e.toLowerCase())},options:C.map(e=>({label:"".concat(e.text," - ").concat(e.meaning),value:e.id}))})}),(0,t.jsx)(d.A.Item,{children:(0,t.jsxs)(A.A,{children:[(0,t.jsx)(x.Ay,{type:"primary",htmlType:"submit",loading:i,children:"更新关卡"}),(0,t.jsx)(x.Ay,{onClick:()=>s.back(),children:"取消"})]})})]})]})})}},63769:(e,s,l)=>{Promise.resolve().then(l.bind(l,16853))}},e=>{var s=s=>e(e.s=s);e.O(0,[8340,547,7469,7497,44,6312,778,7605,4670,2689,4702,9179,8441,1684,7358],()=>s(63769)),_N_E=e.O()}]);