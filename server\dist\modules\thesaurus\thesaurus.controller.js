"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThesaurusController = void 0;
const common_1 = require("@nestjs/common");
const thesaurus_service_1 = require("./thesaurus.service");
const create_thesaurus_dto_1 = require("./dto/create-thesaurus.dto");
const update_thesaurus_dto_1 = require("./dto/update-thesaurus.dto");
const swagger_1 = require("@nestjs/swagger");
const thesaurus_response_dto_1 = require("./dto/thesaurus-response.dto");
const add_phrase_to_thesaurus_dto_1 = require("./dto/add-phrase-to-thesaurus.dto");
let ThesaurusController = class ThesaurusController {
    thesaurusService;
    constructor(thesaurusService) {
        this.thesaurusService = thesaurusService;
    }
    create(createThesaurusDto) {
        return this.thesaurusService.create(createThesaurusDto);
    }
    findAll() {
        return this.thesaurusService.findAll();
    }
    findOne(id) {
        return this.thesaurusService.findOne(id);
    }
    update(id, updateThesaurusDto) {
        return this.thesaurusService.update(id, updateThesaurusDto);
    }
    remove(id) {
        return this.thesaurusService.remove(id);
    }
    addPhraseToThesaurus(thesaurusId, addPhraseDto) {
        return this.thesaurusService.addPhrase(thesaurusId, addPhraseDto.phraseId);
    }
    removePhraseFromThesaurus(thesaurusId, phraseId) {
        return this.thesaurusService.removePhrase(thesaurusId, phraseId);
    }
};
exports.ThesaurusController = ThesaurusController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建新词库' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '词库创建成功', type: thesaurus_response_dto_1.ThesaurusResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_thesaurus_dto_1.CreateThesaurusDto]),
    __metadata("design:returntype", void 0)
], ThesaurusController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有词库列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取词库列表', type: [thesaurus_response_dto_1.ThesaurusResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ThesaurusController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取单个词库' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '词库的UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取词库', type: thesaurus_response_dto_1.ThesaurusResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '词库未找到' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ThesaurusController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新指定ID的词库' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '词库的UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '词库更新成功', type: thesaurus_response_dto_1.ThesaurusResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '词库未找到' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_thesaurus_dto_1.UpdateThesaurusDto]),
    __metadata("design:returntype", void 0)
], ThesaurusController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '删除指定ID的词库' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '词库的UUID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '词库删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '词库未找到' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ThesaurusController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/phrases'),
    (0, swagger_1.ApiOperation)({ summary: '向指定词库添加词组' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '词库的UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '词组添加成功', type: thesaurus_response_dto_1.ThesaurusResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '词库或词组未找到' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误或词组已存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, add_phrase_to_thesaurus_dto_1.AddPhraseToThesaurusDto]),
    __metadata("design:returntype", void 0)
], ThesaurusController.prototype, "addPhraseToThesaurus", null);
__decorate([
    (0, common_1.Delete)(':id/phrases/:phraseId'),
    (0, swagger_1.ApiOperation)({ summary: '从指定词库移除词组' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '词库的UUID' }),
    (0, swagger_1.ApiParam)({ name: 'phraseId', description: '词组的UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '词组移除成功', type: thesaurus_response_dto_1.ThesaurusResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '词库或词组未在词库中找到' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('phraseId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], ThesaurusController.prototype, "removePhraseFromThesaurus", null);
exports.ThesaurusController = ThesaurusController = __decorate([
    (0, swagger_1.ApiTags)('thesauruses'),
    (0, common_1.Controller)('thesauruses'),
    __metadata("design:paramtypes", [thesaurus_service_1.ThesaurusService])
], ThesaurusController);
//# sourceMappingURL=thesaurus.controller.js.map