import { OnModuleInit } from '@nestjs/common';
import { Model } from 'mongoose';
import { SettingsDocument } from './entities/settings.entity';
import { CreateSettingsDto } from './dto/create-settings.dto';
import { UpdateSettingsDto } from './dto/update-settings.dto';
import { SettingsResponseDto } from './dto/settings-response.dto';
export declare class SettingsService implements OnModuleInit {
    private settingsModel;
    constructor(settingsModel: Model<SettingsDocument>);
    create(createSettingsDto: CreateSettingsDto): Promise<SettingsResponseDto>;
    findAll(): Promise<SettingsResponseDto[]>;
    findOne(id: string): Promise<SettingsResponseDto>;
    findByKey(key: string): Promise<SettingsResponseDto>;
    update(id: string, updateSettingsDto: UpdateSettingsDto): Promise<SettingsResponseDto>;
    updateByKey(key: string, value: string): Promise<SettingsResponseDto>;
    remove(id: string): Promise<void>;
    getSettingsEntity(id: string): Promise<SettingsDocument>;
    initializeDefaultSettings(): Promise<void>;
    onModuleInit(): Promise<void>;
    private _mapToSettingsResponseDto;
}
