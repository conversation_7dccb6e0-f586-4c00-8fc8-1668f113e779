"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSettingsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateSettingsDto {
    key;
    value;
    description;
    type;
}
exports.CreateSettingsDto = CreateSettingsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置键名', example: 'help_url' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSettingsDto.prototype, "key", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置值', example: 'https://help.example.com' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSettingsDto.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设置描述', example: '帮助页面链接', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSettingsDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '设置类型',
        enum: ['string', 'number', 'boolean', 'url'],
        example: 'url'
    }),
    (0, class_validator_1.IsEnum)(['string', 'number', 'boolean', 'url']),
    __metadata("design:type", String)
], CreateSettingsDto.prototype, "type", void 0);
//# sourceMappingURL=create-settings.dto.js.map