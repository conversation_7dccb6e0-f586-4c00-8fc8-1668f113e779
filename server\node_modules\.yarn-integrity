{"systemParams": "win32-x64-127", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@eslint/eslintrc@^3.2.0", "@eslint/js@^9.18.0", "@nestjs/cli@^11.0.0", "@nestjs/common@^11.0.1", "@nestjs/config@^4.0.2", "@nestjs/core@^11.0.1", "@nestjs/jwt@^11.0.0", "@nestjs/mapped-types@^2.1.0", "@nestjs/mongoose@^11.0.3", "@nestjs/passport@^11.0.5", "@nestjs/platform-express@^11.0.1", "@nestjs/schematics@^11.0.0", "@nestjs/swagger@^11.2.0", "@nestjs/testing@^11.0.1", "@swc/cli@^0.6.0", "@swc/core@^1.10.7", "@types/bcrypt@^5.0.2", "@types/express@^5.0.0", "@types/jest@^29.5.14", "@types/node@^24.0.1", "@types/passport-jwt@^4.0.1", "@types/passport@^0", "@types/supertest@^6.0.2", "@types/uuid@^10.0.0", "axios@^1.10.0", "bcrypt@^6.0.0", "class-transformer@^0.5.1", "class-validator@^0.14.2", "cross-env@^7.0.3", "date-fns@3.6.0", "eslint-config-prettier@^10.0.1", "eslint-plugin-prettier@^5.2.2", "eslint@^9.18.0", "globals@^16.0.0", "jest@^29.7.0", "joi@^17.13.3", "mongoose@^8.15.1", "passport-jwt@^4.0.1", "passport@^0.7.0", "prettier@^3.4.2", "reflect-metadata@^0.2.2", "rxjs@^7.8.1", "source-map-support@^0.5.21", "supertest@^7.0.0", "swagger-ui-express@^5.0.1", "ts-jest@^29.2.5", "ts-loader@^9.5.2", "ts-node@^10.9.2", "tsconfig-paths@^4.2.0", "typescript-eslint@^8.20.0", "typescript@^5.7.3", "uuid@^11.1.0"], "lockfileEntries": {"@ampproject/remapping@^2.2.0": "https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.3.0.tgz", "@angular-devkit/core@19.2.6": "https://registry.npmmirror.com/@angular-devkit/core/-/core-19.2.6.tgz", "@angular-devkit/core@19.2.8": "https://registry.npmmirror.com/@angular-devkit/core/-/core-19.2.8.tgz", "@angular-devkit/schematics-cli@19.2.8": "https://registry.npmmirror.com/@angular-devkit/schematics-cli/-/schematics-cli-19.2.8.tgz", "@angular-devkit/schematics@19.2.6": "https://registry.npmmirror.com/@angular-devkit/schematics/-/schematics-19.2.6.tgz", "@angular-devkit/schematics@19.2.8": "https://registry.npmmirror.com/@angular-devkit/schematics/-/schematics-19.2.8.tgz", "@babel/code-frame@^7.0.0": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/code-frame@^7.12.13": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/code-frame@^7.16.7": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/code-frame@^7.27.1": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/compat-data@^7.27.2": "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.27.7.tgz", "@babel/core@^7.11.6": "https://registry.npmmirror.com/@babel/core/-/core-7.27.7.tgz", "@babel/core@^7.12.3": "https://registry.npmmirror.com/@babel/core/-/core-7.27.7.tgz", "@babel/core@^7.23.9": "https://registry.npmmirror.com/@babel/core/-/core-7.27.7.tgz", "@babel/generator@^7.27.5": "https://registry.npmmirror.com/@babel/generator/-/generator-7.27.5.tgz", "@babel/generator@^7.7.2": "https://registry.npmmirror.com/@babel/generator/-/generator-7.27.5.tgz", "@babel/helper-compilation-targets@^7.27.2": "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "@babel/helper-module-imports@^7.27.1": "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "@babel/helper-module-transforms@^7.27.3": "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "@babel/helper-plugin-utils@^7.0.0": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.10.4": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.12.13": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.14.5": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.27.1": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.8.0": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-string-parser@^7.27.1": "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "@babel/helper-validator-identifier@^7.27.1": "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "@babel/helper-validator-option@^7.27.1": "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "@babel/helpers@^7.27.6": "https://registry.npmmirror.com/@babel/helpers/-/helpers-7.27.6.tgz", "@babel/parser@^7.1.0": "https://registry.npmmirror.com/@babel/parser/-/parser-7.27.7.tgz", "@babel/parser@^7.14.7": "https://registry.npmmirror.com/@babel/parser/-/parser-7.27.7.tgz", "@babel/parser@^7.20.7": "https://registry.npmmirror.com/@babel/parser/-/parser-7.27.7.tgz", "@babel/parser@^7.23.9": "https://registry.npmmirror.com/@babel/parser/-/parser-7.27.7.tgz", "@babel/parser@^7.27.2": "https://registry.npmmirror.com/@babel/parser/-/parser-7.27.7.tgz", "@babel/parser@^7.27.5": "https://registry.npmmirror.com/@babel/parser/-/parser-7.27.7.tgz", "@babel/parser@^7.27.7": "https://registry.npmmirror.com/@babel/parser/-/parser-7.27.7.tgz", "@babel/plugin-syntax-async-generators@^7.8.4": "https://registry.npmmirror.com/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "@babel/plugin-syntax-bigint@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz", "@babel/plugin-syntax-class-properties@^7.12.13": "https://registry.npmmirror.com/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "@babel/plugin-syntax-class-static-block@^7.14.5": "https://registry.npmmirror.com/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "@babel/plugin-syntax-import-attributes@^7.24.7": "https://registry.npmmirror.com/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz", "@babel/plugin-syntax-import-meta@^7.10.4": "https://registry.npmmirror.com/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "@babel/plugin-syntax-json-strings@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "@babel/plugin-syntax-jsx@^7.7.2": "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz", "@babel/plugin-syntax-logical-assignment-operators@^7.10.4": "https://registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "@babel/plugin-syntax-numeric-separator@^7.10.4": "https://registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "@babel/plugin-syntax-object-rest-spread@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "@babel/plugin-syntax-optional-catch-binding@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "@babel/plugin-syntax-optional-chaining@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "@babel/plugin-syntax-private-property-in-object@^7.14.5": "https://registry.npmmirror.com/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "@babel/plugin-syntax-top-level-await@^7.14.5": "https://registry.npmmirror.com/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "@babel/plugin-syntax-typescript@^7.7.2": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz", "@babel/template@^7.27.2": "https://registry.npmmirror.com/@babel/template/-/template-7.27.2.tgz", "@babel/template@^7.3.3": "https://registry.npmmirror.com/@babel/template/-/template-7.27.2.tgz", "@babel/traverse@^7.27.1": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.27.7.tgz", "@babel/traverse@^7.27.3": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.27.7.tgz", "@babel/traverse@^7.27.7": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.27.7.tgz", "@babel/types@^7.0.0": "https://registry.npmmirror.com/@babel/types/-/types-7.27.7.tgz", "@babel/types@^7.20.7": "https://registry.npmmirror.com/@babel/types/-/types-7.27.7.tgz", "@babel/types@^7.27.1": "https://registry.npmmirror.com/@babel/types/-/types-7.27.7.tgz", "@babel/types@^7.27.3": "https://registry.npmmirror.com/@babel/types/-/types-7.27.7.tgz", "@babel/types@^7.27.6": "https://registry.npmmirror.com/@babel/types/-/types-7.27.7.tgz", "@babel/types@^7.27.7": "https://registry.npmmirror.com/@babel/types/-/types-7.27.7.tgz", "@babel/types@^7.3.3": "https://registry.npmmirror.com/@babel/types/-/types-7.27.7.tgz", "@bcoe/v8-coverage@^0.2.3": "https://registry.npmmirror.com/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz", "@colors/colors@1.5.0": "https://registry.npmmirror.com/@colors/colors/-/colors-1.5.0.tgz", "@cspotcode/source-map-support@^0.8.0": "https://registry.npmmirror.com/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz", "@eslint-community/eslint-utils@^4.2.0": "https://registry.npmmirror.com/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "@eslint-community/eslint-utils@^4.7.0": "https://registry.npmmirror.com/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "@eslint-community/regexpp@^4.10.0": "https://registry.npmmirror.com/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "@eslint-community/regexpp@^4.12.1": "https://registry.npmmirror.com/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "@eslint/config-array@^0.21.0": "https://registry.npmmirror.com/@eslint/config-array/-/config-array-0.21.0.tgz", "@eslint/config-helpers@^0.3.0": "https://registry.npmmirror.com/@eslint/config-helpers/-/config-helpers-0.3.0.tgz", "@eslint/core@^0.14.0": "https://registry.npmmirror.com/@eslint/core/-/core-0.14.0.tgz", "@eslint/core@^0.15.1": "https://registry.npmmirror.com/@eslint/core/-/core-0.15.1.tgz", "@eslint/eslintrc@^3.2.0": "https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-3.3.1.tgz", "@eslint/eslintrc@^3.3.1": "https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-3.3.1.tgz", "@eslint/js@9.30.0": "https://registry.npmmirror.com/@eslint/js/-/js-9.30.0.tgz", "@eslint/js@^9.18.0": "https://registry.npmmirror.com/@eslint/js/-/js-9.30.0.tgz", "@eslint/object-schema@^2.1.6": "https://registry.npmmirror.com/@eslint/object-schema/-/object-schema-2.1.6.tgz", "@eslint/plugin-kit@^0.3.1": "https://registry.npmmirror.com/@eslint/plugin-kit/-/plugin-kit-0.3.3.tgz", "@hapi/hoek@^9.0.0": "https://registry.npmmirror.com/@hapi/hoek/-/hoek-9.3.0.tgz", "@hapi/hoek@^9.3.0": "https://registry.npmmirror.com/@hapi/hoek/-/hoek-9.3.0.tgz", "@hapi/topo@^5.1.0": "https://registry.npmmirror.com/@hapi/topo/-/topo-5.1.0.tgz", "@humanfs/core@^0.19.1": "https://registry.npmmirror.com/@humanfs/core/-/core-0.19.1.tgz", "@humanfs/node@^0.16.6": "https://registry.npmmirror.com/@humanfs/node/-/node-0.16.6.tgz", "@humanwhocodes/module-importer@^1.0.1": "https://registry.npmmirror.com/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "@humanwhocodes/retry@^0.3.0": "https://registry.npmmirror.com/@humanwhocodes/retry/-/retry-0.3.1.tgz", "@humanwhocodes/retry@^0.4.2": "https://registry.npmmirror.com/@humanwhocodes/retry/-/retry-0.4.3.tgz", "@inquirer/checkbox@^4.1.2": "https://registry.npmmirror.com/@inquirer/checkbox/-/checkbox-4.1.8.tgz", "@inquirer/checkbox@^4.1.5": "https://registry.npmmirror.com/@inquirer/checkbox/-/checkbox-4.1.8.tgz", "@inquirer/confirm@^5.1.6": "https://registry.npmmirror.com/@inquirer/confirm/-/confirm-5.1.12.tgz", "@inquirer/confirm@^5.1.9": "https://registry.npmmirror.com/@inquirer/confirm/-/confirm-5.1.12.tgz", "@inquirer/core@^10.1.13": "https://registry.npmmirror.com/@inquirer/core/-/core-10.1.13.tgz", "@inquirer/editor@^4.2.10": "https://registry.npmmirror.com/@inquirer/editor/-/editor-4.2.13.tgz", "@inquirer/editor@^4.2.7": "https://registry.npmmirror.com/@inquirer/editor/-/editor-4.2.13.tgz", "@inquirer/expand@^4.0.12": "https://registry.npmmirror.com/@inquirer/expand/-/expand-4.0.15.tgz", "@inquirer/expand@^4.0.9": "https://registry.npmmirror.com/@inquirer/expand/-/expand-4.0.15.tgz", "@inquirer/figures@^1.0.12": "https://registry.npmmirror.com/@inquirer/figures/-/figures-1.0.12.tgz", "@inquirer/input@^4.1.6": "https://registry.npmmirror.com/@inquirer/input/-/input-4.1.12.tgz", "@inquirer/input@^4.1.9": "https://registry.npmmirror.com/@inquirer/input/-/input-4.1.12.tgz", "@inquirer/number@^3.0.12": "https://registry.npmmirror.com/@inquirer/number/-/number-3.0.15.tgz", "@inquirer/number@^3.0.9": "https://registry.npmmirror.com/@inquirer/number/-/number-3.0.15.tgz", "@inquirer/password@^4.0.12": "https://registry.npmmirror.com/@inquirer/password/-/password-4.0.15.tgz", "@inquirer/password@^4.0.9": "https://registry.npmmirror.com/@inquirer/password/-/password-4.0.15.tgz", "@inquirer/prompts@7.3.2": "https://registry.npmmirror.com/@inquirer/prompts/-/prompts-7.3.2.tgz", "@inquirer/prompts@7.4.1": "https://registry.npmmirror.com/@inquirer/prompts/-/prompts-7.4.1.tgz", "@inquirer/rawlist@^4.0.12": "https://registry.npmmirror.com/@inquirer/rawlist/-/rawlist-4.1.3.tgz", "@inquirer/rawlist@^4.0.9": "https://registry.npmmirror.com/@inquirer/rawlist/-/rawlist-4.1.3.tgz", "@inquirer/search@^3.0.12": "https://registry.npmmirror.com/@inquirer/search/-/search-3.0.15.tgz", "@inquirer/search@^3.0.9": "https://registry.npmmirror.com/@inquirer/search/-/search-3.0.15.tgz", "@inquirer/select@^4.0.9": "https://registry.npmmirror.com/@inquirer/select/-/select-4.2.3.tgz", "@inquirer/select@^4.1.1": "https://registry.npmmirror.com/@inquirer/select/-/select-4.2.3.tgz", "@inquirer/type@^3.0.7": "https://registry.npmmirror.com/@inquirer/type/-/type-3.0.7.tgz", "@isaacs/balanced-match@^4.0.1": "https://registry.npmmirror.com/@isaacs/balanced-match/-/balanced-match-4.0.1.tgz", "@isaacs/brace-expansion@^5.0.0": "https://registry.npmmirror.com/@isaacs/brace-expansion/-/brace-expansion-5.0.0.tgz", "@isaacs/cliui@^8.0.2": "https://registry.npmmirror.com/@isaacs/cliui/-/cliui-8.0.2.tgz", "@istanbuljs/load-nyc-config@^1.0.0": "https://registry.npmmirror.com/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz", "@istanbuljs/schema@^0.1.2": "https://registry.npmmirror.com/@istanbuljs/schema/-/schema-0.1.3.tgz", "@istanbuljs/schema@^0.1.3": "https://registry.npmmirror.com/@istanbuljs/schema/-/schema-0.1.3.tgz", "@jest/console@^29.7.0": "https://registry.npmmirror.com/@jest/console/-/console-29.7.0.tgz", "@jest/core@^29.7.0": "https://registry.npmmirror.com/@jest/core/-/core-29.7.0.tgz", "@jest/environment@^29.7.0": "https://registry.npmmirror.com/@jest/environment/-/environment-29.7.0.tgz", "@jest/expect-utils@^29.7.0": "https://registry.npmmirror.com/@jest/expect-utils/-/expect-utils-29.7.0.tgz", "@jest/expect@^29.7.0": "https://registry.npmmirror.com/@jest/expect/-/expect-29.7.0.tgz", "@jest/fake-timers@^29.7.0": "https://registry.npmmirror.com/@jest/fake-timers/-/fake-timers-29.7.0.tgz", "@jest/globals@^29.7.0": "https://registry.npmmirror.com/@jest/globals/-/globals-29.7.0.tgz", "@jest/reporters@^29.7.0": "https://registry.npmmirror.com/@jest/reporters/-/reporters-29.7.0.tgz", "@jest/schemas@^29.6.3": "https://registry.npmmirror.com/@jest/schemas/-/schemas-29.6.3.tgz", "@jest/source-map@^29.6.3": "https://registry.npmmirror.com/@jest/source-map/-/source-map-29.6.3.tgz", "@jest/test-result@^29.7.0": "https://registry.npmmirror.com/@jest/test-result/-/test-result-29.7.0.tgz", "@jest/test-sequencer@^29.7.0": "https://registry.npmmirror.com/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz", "@jest/transform@^29.7.0": "https://registry.npmmirror.com/@jest/transform/-/transform-29.7.0.tgz", "@jest/types@^29.6.3": "https://registry.npmmirror.com/@jest/types/-/types-29.6.3.tgz", "@jridgewell/gen-mapping@^0.3.5": "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.11.tgz", "@jridgewell/resolve-uri@^3.0.3": "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "@jridgewell/resolve-uri@^3.1.0": "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "@jridgewell/source-map@^0.3.3": "https://registry.npmmirror.com/@jridgewell/source-map/-/source-map-0.3.9.tgz", "@jridgewell/sourcemap-codec@^1.4.10": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.3.tgz", "@jridgewell/sourcemap-codec@^1.4.14": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.3.tgz", "@jridgewell/sourcemap-codec@^1.5.0": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.3.tgz", "@jridgewell/trace-mapping@0.3.9": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz", "@jridgewell/trace-mapping@^0.3.12": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.28.tgz", "@jridgewell/trace-mapping@^0.3.18": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.28.tgz", "@jridgewell/trace-mapping@^0.3.24": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.28.tgz", "@jridgewell/trace-mapping@^0.3.25": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.28.tgz", "@lukeed/csprng@^1.0.0": "https://registry.npmmirror.com/@lukeed/csprng/-/csprng-1.1.0.tgz", "@microsoft/tsdoc@0.15.1": "https://registry.npmmirror.com/@microsoft/tsdoc/-/tsdoc-0.15.1.tgz", "@mongodb-js/saslprep@^1.1.9": "https://registry.npmmirror.com/@mongodb-js/saslprep/-/saslprep-1.2.2.tgz", "@napi-rs/nice-android-arm-eabi@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-android-arm-eabi/-/nice-android-arm-eabi-1.0.1.tgz#9a0cba12706ff56500df127d6f4caf28ddb94936", "@napi-rs/nice-android-arm64@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-android-arm64/-/nice-android-arm64-1.0.1.tgz#32fc32e9649bd759d2a39ad745e95766f6759d2f", "@napi-rs/nice-darwin-arm64@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-darwin-arm64/-/nice-darwin-arm64-1.0.1.tgz#d3c44c51b94b25a82d45803e2255891e833e787b", "@napi-rs/nice-darwin-x64@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-darwin-x64/-/nice-darwin-x64-1.0.1.tgz#f1b1365a8370c6a6957e90085a9b4873d0e6a957", "@napi-rs/nice-freebsd-x64@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-freebsd-x64/-/nice-freebsd-x64-1.0.1.tgz#4280f081efbe0b46c5165fdaea8b286e55a8f89e", "@napi-rs/nice-linux-arm-gnueabihf@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-linux-arm-gnueabihf/-/nice-linux-arm-gnueabihf-1.0.1.tgz#07aec23a9467ed35eb7602af5e63d42c5d7bd473", "@napi-rs/nice-linux-arm64-gnu@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-linux-arm64-gnu/-/nice-linux-arm64-gnu-1.0.1.tgz#038a77134cc6df3c48059d5a5e199d6f50fb9a90", "@napi-rs/nice-linux-arm64-musl@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-linux-arm64-musl/-/nice-linux-arm64-musl-1.0.1.tgz#715d0906582ba0cff025109f42e5b84ea68c2bcc", "@napi-rs/nice-linux-ppc64-gnu@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-linux-ppc64-gnu/-/nice-linux-ppc64-gnu-1.0.1.tgz#ac1c8f781c67b0559fa7a1cd4ae3ca2299dc3d06", "@napi-rs/nice-linux-riscv64-gnu@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-linux-riscv64-gnu/-/nice-linux-riscv64-gnu-1.0.1.tgz#b0a430549acfd3920ffd28ce544e2fe17833d263", "@napi-rs/nice-linux-s390x-gnu@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-linux-s390x-gnu/-/nice-linux-s390x-gnu-1.0.1.tgz#5b95caf411ad72a965885217db378c4d09733e97", "@napi-rs/nice-linux-x64-gnu@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-linux-x64-gnu/-/nice-linux-x64-gnu-1.0.1.tgz#a98cdef517549f8c17a83f0236a69418a90e77b7", "@napi-rs/nice-linux-x64-musl@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-linux-x64-musl/-/nice-linux-x64-musl-1.0.1.tgz#5e26843eafa940138aed437c870cca751c8a8957", "@napi-rs/nice-win32-arm64-msvc@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-win32-arm64-msvc/-/nice-win32-arm64-msvc-1.0.1.tgz#bd62617d02f04aa30ab1e9081363856715f84cd8", "@napi-rs/nice-win32-ia32-msvc@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-win32-ia32-msvc/-/nice-win32-ia32-msvc-1.0.1.tgz#b8b7aad552a24836027473d9b9f16edaeabecf18", "@napi-rs/nice-win32-x64-msvc@1.0.1": "https://registry.npmmirror.com/@napi-rs/nice-win32-x64-msvc/-/nice-win32-x64-msvc-1.0.1.tgz", "@napi-rs/nice@^1.0.1": "https://registry.npmmirror.com/@napi-rs/nice/-/nice-1.0.1.tgz", "@nestjs/cli@^11.0.0": "https://registry.npmmirror.com/@nestjs/cli/-/cli-11.0.7.tgz", "@nestjs/common@^11.0.1": "https://registry.npmmirror.com/@nestjs/common/-/common-11.1.3.tgz", "@nestjs/config@^4.0.2": "https://registry.npmmirror.com/@nestjs/config/-/config-4.0.2.tgz", "@nestjs/core@^11.0.1": "https://registry.npmmirror.com/@nestjs/core/-/core-11.1.3.tgz", "@nestjs/jwt@^11.0.0": "https://registry.npmmirror.com/@nestjs/jwt/-/jwt-11.0.0.tgz", "@nestjs/mapped-types@2.1.0": "https://registry.npmmirror.com/@nestjs/mapped-types/-/mapped-types-2.1.0.tgz", "@nestjs/mapped-types@^2.1.0": "https://registry.npmmirror.com/@nestjs/mapped-types/-/mapped-types-2.1.0.tgz", "@nestjs/mongoose@^11.0.3": "https://registry.npmmirror.com/@nestjs/mongoose/-/mongoose-11.0.3.tgz", "@nestjs/passport@^11.0.5": "https://registry.npmmirror.com/@nestjs/passport/-/passport-11.0.5.tgz", "@nestjs/platform-express@^11.0.1": "https://registry.npmmirror.com/@nestjs/platform-express/-/platform-express-11.1.3.tgz", "@nestjs/schematics@^11.0.0": "https://registry.npmmirror.com/@nestjs/schematics/-/schematics-11.0.5.tgz", "@nestjs/schematics@^11.0.1": "https://registry.npmmirror.com/@nestjs/schematics/-/schematics-11.0.5.tgz", "@nestjs/swagger@^11.2.0": "https://registry.npmmirror.com/@nestjs/swagger/-/swagger-11.2.0.tgz", "@nestjs/testing@^11.0.1": "https://registry.npmmirror.com/@nestjs/testing/-/testing-11.1.3.tgz", "@noble/hashes@^1.1.5": "https://registry.npmmirror.com/@noble/hashes/-/hashes-1.8.0.tgz", "@nodelib/fs.scandir@2.1.5": "https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "@nodelib/fs.stat@2.0.5": "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.stat@^2.0.2": "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.walk@^1.2.3": "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "@nuxt/opencollective@0.4.1": "https://registry.npmmirror.com/@nuxt/opencollective/-/opencollective-0.4.1.tgz", "@paralleldrive/cuid2@^2.2.2": "https://registry.npmmirror.com/@paralleldrive/cuid2/-/cuid2-2.2.2.tgz", "@pkgr/core@^0.2.4": "https://registry.npmmirror.com/@pkgr/core/-/core-0.2.7.tgz", "@scarf/scarf@=1.4.0": "https://registry.npmmirror.com/@scarf/scarf/-/scarf-1.4.0.tgz", "@sec-ant/readable-stream@^0.4.1": "https://registry.npmmirror.com/@sec-ant/readable-stream/-/readable-stream-0.4.1.tgz", "@sideway/address@^4.1.5": "https://registry.npmmirror.com/@sideway/address/-/address-4.1.5.tgz", "@sideway/formula@^3.0.1": "https://registry.npmmirror.com/@sideway/formula/-/formula-3.0.1.tgz", "@sideway/pinpoint@^2.0.0": "https://registry.npmmirror.com/@sideway/pinpoint/-/pinpoint-2.0.0.tgz", "@sinclair/typebox@^0.27.8": "https://registry.npmmirror.com/@sinclair/typebox/-/typebox-0.27.8.tgz", "@sindresorhus/is@^5.2.0": "https://registry.npmmirror.com/@sindresorhus/is/-/is-5.6.0.tgz", "@sinonjs/commons@^3.0.0": "https://registry.npmmirror.com/@sinonjs/commons/-/commons-3.0.1.tgz", "@sinonjs/fake-timers@^10.0.2": "https://registry.npmmirror.com/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz", "@swc/cli@^0.6.0": "https://registry.npmmirror.com/@swc/cli/-/cli-0.6.0.tgz", "@swc/core-darwin-arm64@1.12.9": "https://registry.npmmirror.com/@swc/core-darwin-arm64/-/core-darwin-arm64-1.12.9.tgz#33d28833adf04aed1a5d84aefdb5fdd45dab2676", "@swc/core-darwin-x64@1.12.9": "https://registry.npmmirror.com/@swc/core-darwin-x64/-/core-darwin-x64-1.12.9.tgz#166843f1a8b8000d9932817769cfac0c189dc40c", "@swc/core-linux-arm-gnueabihf@1.12.9": "https://registry.npmmirror.com/@swc/core-linux-arm-gnueabihf/-/core-linux-arm-gnueabihf-1.12.9.tgz#b9b3e29691694e99481386d6d4043f09e90ee66f", "@swc/core-linux-arm64-gnu@1.12.9": "https://registry.npmmirror.com/@swc/core-linux-arm64-gnu/-/core-linux-arm64-gnu-1.12.9.tgz#6b353ddab0931736b5f788154cb11b1942817bc6", "@swc/core-linux-arm64-musl@1.12.9": "https://registry.npmmirror.com/@swc/core-linux-arm64-musl/-/core-linux-arm64-musl-1.12.9.tgz#98d129cf6fd548a58b2c8398842f85305a3f9949", "@swc/core-linux-x64-gnu@1.12.9": "https://registry.npmmirror.com/@swc/core-linux-x64-gnu/-/core-linux-x64-gnu-1.12.9.tgz#23fdc03983ca96ddb7b4f1910195b73b4c441033", "@swc/core-linux-x64-musl@1.12.9": "https://registry.npmmirror.com/@swc/core-linux-x64-musl/-/core-linux-x64-musl-1.12.9.tgz#54bffa601a6b84b0de9116bdaaabd6ae6fa61ee8", "@swc/core-win32-arm64-msvc@1.12.9": "https://registry.npmmirror.com/@swc/core-win32-arm64-msvc/-/core-win32-arm64-msvc-1.12.9.tgz#bfdd139dc88db0b7afe771e25feff5c4525f33fe", "@swc/core-win32-ia32-msvc@1.12.9": "https://registry.npmmirror.com/@swc/core-win32-ia32-msvc/-/core-win32-ia32-msvc-1.12.9.tgz#36ec3cd70e434dea13344b2683117cdc7cb25cf5", "@swc/core-win32-x64-msvc@1.12.9": "https://registry.npmmirror.com/@swc/core-win32-x64-msvc/-/core-win32-x64-msvc-1.12.9.tgz", "@swc/core@^1.10.7": "https://registry.npmmirror.com/@swc/core/-/core-1.12.9.tgz", "@swc/counter@^0.1.3": "https://registry.npmmirror.com/@swc/counter/-/counter-0.1.3.tgz", "@swc/types@^0.1.23": "https://registry.npmmirror.com/@swc/types/-/types-0.1.23.tgz", "@szmarczak/http-timer@^5.0.1": "https://registry.npmmirror.com/@szmarczak/http-timer/-/http-timer-5.0.1.tgz", "@tokenizer/inflate@^0.2.7": "https://registry.npmmirror.com/@tokenizer/inflate/-/inflate-0.2.7.tgz", "@tokenizer/token@^0.3.0": "https://registry.npmmirror.com/@tokenizer/token/-/token-0.3.0.tgz", "@tsconfig/node10@^1.0.7": "https://registry.npmmirror.com/@tsconfig/node10/-/node10-1.0.11.tgz", "@tsconfig/node12@^1.0.7": "https://registry.npmmirror.com/@tsconfig/node12/-/node12-1.0.11.tgz", "@tsconfig/node14@^1.0.0": "https://registry.npmmirror.com/@tsconfig/node14/-/node14-1.0.3.tgz", "@tsconfig/node16@^1.0.2": "https://registry.npmmirror.com/@tsconfig/node16/-/node16-1.0.4.tgz", "@types/babel__core@^7.1.14": "https://registry.npmmirror.com/@types/babel__core/-/babel__core-7.20.5.tgz", "@types/babel__generator@*": "https://registry.npmmirror.com/@types/babel__generator/-/babel__generator-7.27.0.tgz", "@types/babel__template@*": "https://registry.npmmirror.com/@types/babel__template/-/babel__template-7.4.4.tgz", "@types/babel__traverse@*": "https://registry.npmmirror.com/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "@types/babel__traverse@^7.0.6": "https://registry.npmmirror.com/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "@types/bcrypt@^5.0.2": "https://registry.npmmirror.com/@types/bcrypt/-/bcrypt-5.0.2.tgz", "@types/body-parser@*": "https://registry.npmmirror.com/@types/body-parser/-/body-parser-1.19.6.tgz", "@types/connect@*": "https://registry.npmmirror.com/@types/connect/-/connect-3.4.38.tgz", "@types/cookiejar@^2.1.5": "https://registry.npmmirror.com/@types/cookiejar/-/cookiejar-2.1.5.tgz", "@types/eslint-scope@^3.7.7": "https://registry.npmmirror.com/@types/eslint-scope/-/eslint-scope-3.7.7.tgz", "@types/eslint@*": "https://registry.npmmirror.com/@types/eslint/-/eslint-9.6.1.tgz", "@types/estree@*": "https://registry.npmmirror.com/@types/estree/-/estree-1.0.8.tgz", "@types/estree@^1.0.6": "https://registry.npmmirror.com/@types/estree/-/estree-1.0.8.tgz", "@types/express-serve-static-core@^5.0.0": "https://registry.npmmirror.com/@types/express-serve-static-core/-/express-serve-static-core-5.0.6.tgz", "@types/express@*": "https://registry.npmmirror.com/@types/express/-/express-5.0.3.tgz", "@types/express@^5.0.0": "https://registry.npmmirror.com/@types/express/-/express-5.0.3.tgz", "@types/graceful-fs@^4.1.3": "https://registry.npmmirror.com/@types/graceful-fs/-/graceful-fs-4.1.9.tgz", "@types/http-cache-semantics@^4.0.2": "https://registry.npmmirror.com/@types/http-cache-semantics/-/http-cache-semantics-4.0.4.tgz", "@types/http-errors@*": "https://registry.npmmirror.com/@types/http-errors/-/http-errors-2.0.5.tgz", "@types/istanbul-lib-coverage@*": "https://registry.npmmirror.com/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "@types/istanbul-lib-coverage@^2.0.0": "https://registry.npmmirror.com/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "@types/istanbul-lib-coverage@^2.0.1": "https://registry.npmmirror.com/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "@types/istanbul-lib-report@*": "https://registry.npmmirror.com/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz", "@types/istanbul-reports@^3.0.0": "https://registry.npmmirror.com/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz", "@types/jest@^29.5.14": "https://registry.npmmirror.com/@types/jest/-/jest-29.5.14.tgz", "@types/json-schema@*": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json-schema@^7.0.15": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json-schema@^7.0.8": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json-schema@^7.0.9": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/jsonwebtoken@*": "https://registry.npmmirror.com/@types/jsonwebtoken/-/jsonwebtoken-9.0.10.tgz", "@types/jsonwebtoken@9.0.7": "https://registry.npmmirror.com/@types/jsonwebtoken/-/jsonwebtoken-9.0.7.tgz", "@types/methods@^1.1.4": "https://registry.npmmirror.com/@types/methods/-/methods-1.1.4.tgz", "@types/mime@^1": "https://registry.npmmirror.com/@types/mime/-/mime-1.3.5.tgz", "@types/ms@*": "https://registry.npmmirror.com/@types/ms/-/ms-2.1.0.tgz", "@types/node@*": "https://registry.npmmirror.com/@types/node/-/node-24.0.8.tgz", "@types/node@^24.0.1": "https://registry.npmmirror.com/@types/node/-/node-24.0.8.tgz", "@types/passport-jwt@^4.0.1": "https://registry.npmmirror.com/@types/passport-jwt/-/passport-jwt-4.0.1.tgz", "@types/passport-strategy@*": "https://registry.npmmirror.com/@types/passport-strategy/-/passport-strategy-0.2.38.tgz", "@types/passport@*": "https://registry.npmmirror.com/@types/passport/-/passport-1.0.17.tgz", "@types/passport@^0": "https://registry.npmmirror.com/@types/passport/-/passport-0.4.7.tgz", "@types/qs@*": "https://registry.npmmirror.com/@types/qs/-/qs-6.14.0.tgz", "@types/range-parser@*": "https://registry.npmmirror.com/@types/range-parser/-/range-parser-1.2.7.tgz", "@types/send@*": "https://registry.npmmirror.com/@types/send/-/send-0.17.5.tgz", "@types/serve-static@*": "https://registry.npmmirror.com/@types/serve-static/-/serve-static-1.15.8.tgz", "@types/stack-utils@^2.0.0": "https://registry.npmmirror.com/@types/stack-utils/-/stack-utils-2.0.3.tgz", "@types/superagent@^8.1.0": "https://registry.npmmirror.com/@types/superagent/-/superagent-8.1.9.tgz", "@types/supertest@^6.0.2": "https://registry.npmmirror.com/@types/supertest/-/supertest-6.0.3.tgz", "@types/uuid@^10.0.0": "https://registry.npmmirror.com/@types/uuid/-/uuid-10.0.0.tgz", "@types/validator@^13.11.8": "https://registry.npmmirror.com/@types/validator/-/validator-13.15.2.tgz", "@types/webidl-conversions@*": "https://registry.npmmirror.com/@types/webidl-conversions/-/webidl-conversions-7.0.3.tgz", "@types/whatwg-url@^11.0.2": "https://registry.npmmirror.com/@types/whatwg-url/-/whatwg-url-11.0.5.tgz", "@types/yargs-parser@*": "https://registry.npmmirror.com/@types/yargs-parser/-/yargs-parser-21.0.3.tgz", "@types/yargs@^17.0.8": "https://registry.npmmirror.com/@types/yargs/-/yargs-17.0.33.tgz", "@typescript-eslint/eslint-plugin@8.35.1": "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.35.1.tgz", "@typescript-eslint/parser@8.35.1": "https://registry.npmmirror.com/@typescript-eslint/parser/-/parser-8.35.1.tgz", "@typescript-eslint/project-service@8.35.1": "https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.35.1.tgz", "@typescript-eslint/scope-manager@8.35.1": "https://registry.npmmirror.com/@typescript-eslint/scope-manager/-/scope-manager-8.35.1.tgz", "@typescript-eslint/tsconfig-utils@8.35.1": "https://registry.npmmirror.com/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.1.tgz", "@typescript-eslint/tsconfig-utils@^8.35.1": "https://registry.npmmirror.com/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.1.tgz", "@typescript-eslint/type-utils@8.35.1": "https://registry.npmmirror.com/@typescript-eslint/type-utils/-/type-utils-8.35.1.tgz", "@typescript-eslint/types@8.35.1": "https://registry.npmmirror.com/@typescript-eslint/types/-/types-8.35.1.tgz", "@typescript-eslint/types@^8.35.1": "https://registry.npmmirror.com/@typescript-eslint/types/-/types-8.35.1.tgz", "@typescript-eslint/typescript-estree@8.35.1": "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/-/typescript-estree-8.35.1.tgz", "@typescript-eslint/utils@8.35.1": "https://registry.npmmirror.com/@typescript-eslint/utils/-/utils-8.35.1.tgz", "@typescript-eslint/visitor-keys@8.35.1": "https://registry.npmmirror.com/@typescript-eslint/visitor-keys/-/visitor-keys-8.35.1.tgz", "@webassemblyjs/ast@1.14.1": "https://registry.npmmirror.com/@webassemblyjs/ast/-/ast-1.14.1.tgz", "@webassemblyjs/ast@^1.14.1": "https://registry.npmmirror.com/@webassemblyjs/ast/-/ast-1.14.1.tgz", "@webassemblyjs/floating-point-hex-parser@1.13.2": "https://registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.13.2.tgz", "@webassemblyjs/helper-api-error@1.13.2": "https://registry.npmmirror.com/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz", "@webassemblyjs/helper-buffer@1.14.1": "https://registry.npmmirror.com/@webassemblyjs/helper-buffer/-/helper-buffer-1.14.1.tgz", "@webassemblyjs/helper-numbers@1.13.2": "https://registry.npmmirror.com/@webassemblyjs/helper-numbers/-/helper-numbers-1.13.2.tgz", "@webassemblyjs/helper-wasm-bytecode@1.13.2": "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.13.2.tgz", "@webassemblyjs/helper-wasm-section@1.14.1": "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz", "@webassemblyjs/ieee754@1.13.2": "https://registry.npmmirror.com/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz", "@webassemblyjs/leb128@1.13.2": "https://registry.npmmirror.com/@webassemblyjs/leb128/-/leb128-1.13.2.tgz", "@webassemblyjs/utf8@1.13.2": "https://registry.npmmirror.com/@webassemblyjs/utf8/-/utf8-1.13.2.tgz", "@webassemblyjs/wasm-edit@^1.14.1": "https://registry.npmmirror.com/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz", "@webassemblyjs/wasm-gen@1.14.1": "https://registry.npmmirror.com/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz", "@webassemblyjs/wasm-opt@1.14.1": "https://registry.npmmirror.com/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz", "@webassemblyjs/wasm-parser@1.14.1": "https://registry.npmmirror.com/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz", "@webassemblyjs/wasm-parser@^1.14.1": "https://registry.npmmirror.com/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz", "@webassemblyjs/wast-printer@1.14.1": "https://registry.npmmirror.com/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz", "@xhmikosr/archive-type@^7.0.0": "https://registry.npmmirror.com/@xhmikosr/archive-type/-/archive-type-7.0.0.tgz", "@xhmikosr/bin-check@^7.0.3": "https://registry.npmmirror.com/@xhmikosr/bin-check/-/bin-check-7.0.3.tgz", "@xhmikosr/bin-wrapper@^13.0.5": "https://registry.npmmirror.com/@xhmikosr/bin-wrapper/-/bin-wrapper-13.0.5.tgz", "@xhmikosr/decompress-tar@^8.0.1": "https://registry.npmmirror.com/@xhmikosr/decompress-tar/-/decompress-tar-8.0.1.tgz", "@xhmikosr/decompress-tarbz2@^8.0.1": "https://registry.npmmirror.com/@xhmikosr/decompress-tarbz2/-/decompress-tarbz2-8.0.2.tgz", "@xhmikosr/decompress-targz@^8.0.1": "https://registry.npmmirror.com/@xhmikosr/decompress-targz/-/decompress-targz-8.0.1.tgz", "@xhmikosr/decompress-unzip@^7.0.0": "https://registry.npmmirror.com/@xhmikosr/decompress-unzip/-/decompress-unzip-7.0.0.tgz", "@xhmikosr/decompress@^10.0.1": "https://registry.npmmirror.com/@xhmikosr/decompress/-/decompress-10.0.1.tgz", "@xhmikosr/downloader@^15.0.1": "https://registry.npmmirror.com/@xhmikosr/downloader/-/downloader-15.0.1.tgz", "@xhmikosr/os-filter-obj@^3.0.0": "https://registry.npmmirror.com/@xhmikosr/os-filter-obj/-/os-filter-obj-3.0.0.tgz", "@xtuc/ieee754@^1.2.0": "https://registry.npmmirror.com/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "@xtuc/long@4.2.2": "https://registry.npmmirror.com/@xtuc/long/-/long-4.2.2.tgz", "accepts@^2.0.0": "https://registry.npmmirror.com/accepts/-/accepts-2.0.0.tgz", "acorn-jsx@^5.3.2": "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "acorn-walk@^8.1.1": "https://registry.npmmirror.com/acorn-walk/-/acorn-walk-8.3.4.tgz", "acorn@^8.11.0": "https://registry.npmmirror.com/acorn/-/acorn-8.15.0.tgz", "acorn@^8.14.0": "https://registry.npmmirror.com/acorn/-/acorn-8.15.0.tgz", "acorn@^8.15.0": "https://registry.npmmirror.com/acorn/-/acorn-8.15.0.tgz", "acorn@^8.4.1": "https://registry.npmmirror.com/acorn/-/acorn-8.15.0.tgz", "ajv-formats@3.0.1": "https://registry.npmmirror.com/ajv-formats/-/ajv-formats-3.0.1.tgz", "ajv-formats@^2.1.1": "https://registry.npmmirror.com/ajv-formats/-/ajv-formats-2.1.1.tgz", "ajv-keywords@^3.5.2": "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv-keywords@^5.1.0": "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-5.1.0.tgz", "ajv@8.17.1": "https://registry.npmmirror.com/ajv/-/ajv-8.17.1.tgz", "ajv@^6.12.4": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.5": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ajv@^8.0.0": "https://registry.npmmirror.com/ajv/-/ajv-8.17.1.tgz", "ajv@^8.9.0": "https://registry.npmmirror.com/ajv/-/ajv-8.17.1.tgz", "ansi-colors@4.1.3": "https://registry.npmmirror.com/ansi-colors/-/ansi-colors-4.1.3.tgz", "ansi-escapes@^4.2.1": "https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-escapes@^4.3.2": "https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-regex@^5.0.1": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-regex@^6.0.1": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-6.1.0.tgz", "ansi-styles@^4.0.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^4.1.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^5.0.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-5.2.0.tgz", "ansi-styles@^6.1.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-6.2.1.tgz", "ansis@3.17.0": "https://registry.npmmirror.com/ansis/-/ansis-3.17.0.tgz", "anymatch@^3.0.3": "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz", "append-field@^1.0.0": "https://registry.npmmirror.com/append-field/-/append-field-1.0.0.tgz", "arch@^3.0.0": "https://registry.npmmirror.com/arch/-/arch-3.0.0.tgz", "arg@^4.1.0": "https://registry.npmmirror.com/arg/-/arg-4.1.3.tgz", "argparse@^1.0.7": "https://registry.npmmirror.com/argparse/-/argparse-1.0.10.tgz", "argparse@^2.0.1": "https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz", "array-timsort@^1.0.3": "https://registry.npmmirror.com/array-timsort/-/array-timsort-1.0.3.tgz", "asap@^2.0.0": "https://registry.npmmirror.com/asap/-/asap-2.0.6.tgz", "async@^3.2.3": "https://registry.npmmirror.com/async/-/async-3.2.6.tgz", "asynckit@^0.4.0": "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz", "axios@^1.10.0": "https://registry.npmmirror.com/axios/-/axios-1.10.0.tgz", "b4a@^1.6.4": "https://registry.npmmirror.com/b4a/-/b4a-1.6.7.tgz", "babel-jest@^29.7.0": "https://registry.npmmirror.com/babel-jest/-/babel-jest-29.7.0.tgz", "babel-plugin-istanbul@^6.1.1": "https://registry.npmmirror.com/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "babel-plugin-jest-hoist@^29.6.3": "https://registry.npmmirror.com/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz", "babel-preset-current-node-syntax@^1.0.0": "https://registry.npmmirror.com/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz", "babel-preset-jest@^29.6.3": "https://registry.npmmirror.com/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz", "balanced-match@^1.0.0": "https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz", "bare-events@^2.2.0": "https://registry.npmmirror.com/bare-events/-/bare-events-2.5.4.tgz", "base64-js@^1.3.1": "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz", "bcrypt@^6.0.0": "https://registry.npmmirror.com/bcrypt/-/bcrypt-6.0.0.tgz", "bin-version-check@^5.1.0": "https://registry.npmmirror.com/bin-version-check/-/bin-version-check-5.1.0.tgz", "bin-version@^6.0.0": "https://registry.npmmirror.com/bin-version/-/bin-version-6.0.0.tgz", "bl@^4.1.0": "https://registry.npmmirror.com/bl/-/bl-4.1.0.tgz", "body-parser@^2.2.0": "https://registry.npmmirror.com/body-parser/-/body-parser-2.2.0.tgz", "brace-expansion@^1.1.7": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.12.tgz", "brace-expansion@^2.0.1": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.2.tgz", "braces@^3.0.3": "https://registry.npmmirror.com/braces/-/braces-3.0.3.tgz", "browserslist@^4.24.0": "https://registry.npmmirror.com/browserslist/-/browserslist-4.25.1.tgz", "bs-logger@^0.2.6": "https://registry.npmmirror.com/bs-logger/-/bs-logger-0.2.6.tgz", "bser@2.1.1": "https://registry.npmmirror.com/bser/-/bser-2.1.1.tgz", "bson@^6.10.4": "https://registry.npmmirror.com/bson/-/bson-6.10.4.tgz", "buffer-crc32@~0.2.3": "https://registry.npmmirror.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "buffer-equal-constant-time@^1.0.1": "https://registry.npmmirror.com/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "buffer-from@^1.0.0": "https://registry.npmmirror.com/buffer-from/-/buffer-from-1.1.2.tgz", "buffer@^5.2.1": "https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz", "buffer@^5.5.0": "https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz", "busboy@^1.6.0": "https://registry.npmmirror.com/busboy/-/busboy-1.6.0.tgz", "bytes@3.1.2": "https://registry.npmmirror.com/bytes/-/bytes-3.1.2.tgz", "bytes@^3.1.2": "https://registry.npmmirror.com/bytes/-/bytes-3.1.2.tgz", "cacheable-lookup@^7.0.0": "https://registry.npmmirror.com/cacheable-lookup/-/cacheable-lookup-7.0.0.tgz", "cacheable-request@^10.2.8": "https://registry.npmmirror.com/cacheable-request/-/cacheable-request-10.2.14.tgz", "call-bind-apply-helpers@^1.0.1": "https://registry.npmmirror.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bind-apply-helpers@^1.0.2": "https://registry.npmmirror.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bound@^1.0.2": "https://registry.npmmirror.com/call-bound/-/call-bound-1.0.4.tgz", "callsites@^3.0.0": "https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz", "camelcase@^5.3.1": "https://registry.npmmirror.com/camelcase/-/camelcase-5.3.1.tgz", "camelcase@^6.2.0": "https://registry.npmmirror.com/camelcase/-/camelcase-6.3.0.tgz", "caniuse-lite@^1.0.30001726": "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001726.tgz", "chalk@^4.0.0": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "chalk@^4.0.2": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.0": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.2": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "char-regex@^1.0.2": "https://registry.npmmirror.com/char-regex/-/char-regex-1.0.2.tgz", "chardet@^0.7.0": "https://registry.npmmirror.com/chardet/-/chardet-0.7.0.tgz", "chokidar@4.0.3": "https://registry.npmmirror.com/chokidar/-/chokidar-4.0.3.tgz", "chokidar@^4.0.1": "https://registry.npmmirror.com/chokidar/-/chokidar-4.0.3.tgz", "chrome-trace-event@^1.0.2": "https://registry.npmmirror.com/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz", "ci-info@^3.2.0": "https://registry.npmmirror.com/ci-info/-/ci-info-3.9.0.tgz", "cjs-module-lexer@^1.0.0": "https://registry.npmmirror.com/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz", "class-transformer@^0.5.1": "https://registry.npmmirror.com/class-transformer/-/class-transformer-0.5.1.tgz", "class-validator@^0.14.2": "https://registry.npmmirror.com/class-validator/-/class-validator-0.14.2.tgz", "cli-cursor@^3.1.0": "https://registry.npmmirror.com/cli-cursor/-/cli-cursor-3.1.0.tgz", "cli-spinners@^2.5.0": "https://registry.npmmirror.com/cli-spinners/-/cli-spinners-2.9.2.tgz", "cli-table3@0.6.5": "https://registry.npmmirror.com/cli-table3/-/cli-table3-0.6.5.tgz", "cli-width@^4.1.0": "https://registry.npmmirror.com/cli-width/-/cli-width-4.1.0.tgz", "cliui@^8.0.1": "https://registry.npmmirror.com/cliui/-/cliui-8.0.1.tgz", "clone@^1.0.2": "https://registry.npmmirror.com/clone/-/clone-1.0.4.tgz", "co@^4.6.0": "https://registry.npmmirror.com/co/-/co-4.6.0.tgz", "collect-v8-coverage@^1.0.0": "https://registry.npmmirror.com/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz", "color-convert@^2.0.1": "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz", "color-name@~1.1.4": "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz", "combined-stream@^1.0.8": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "commander@4.1.1": "https://registry.npmmirror.com/commander/-/commander-4.1.1.tgz", "commander@^2.20.0": "https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz", "commander@^6.0.0": "https://registry.npmmirror.com/commander/-/commander-6.2.1.tgz", "commander@^8.3.0": "https://registry.npmmirror.com/commander/-/commander-8.3.0.tgz", "comment-json@4.2.5": "https://registry.npmmirror.com/comment-json/-/comment-json-4.2.5.tgz", "component-emitter@^1.3.0": "https://registry.npmmirror.com/component-emitter/-/component-emitter-1.3.1.tgz", "concat-map@0.0.1": "https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz", "concat-stream@^2.0.0": "https://registry.npmmirror.com/concat-stream/-/concat-stream-2.0.0.tgz", "consola@^3.2.3": "https://registry.npmmirror.com/consola/-/consola-3.4.2.tgz", "content-disposition@^0.5.4": "https://registry.npmmirror.com/content-disposition/-/content-disposition-0.5.4.tgz", "content-disposition@^1.0.0": "https://registry.npmmirror.com/content-disposition/-/content-disposition-1.0.0.tgz", "content-type@^1.0.5": "https://registry.npmmirror.com/content-type/-/content-type-1.0.5.tgz", "convert-source-map@^2.0.0": "https://registry.npmmirror.com/convert-source-map/-/convert-source-map-2.0.0.tgz", "cookie-signature@^1.2.1": "https://registry.npmmirror.com/cookie-signature/-/cookie-signature-1.2.2.tgz", "cookie@^0.7.1": "https://registry.npmmirror.com/cookie/-/cookie-0.7.2.tgz", "cookiejar@^2.1.4": "https://registry.npmmirror.com/cookiejar/-/cookiejar-2.1.4.tgz", "core-util-is@^1.0.3": "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.3.tgz", "cors@2.8.5": "https://registry.npmmirror.com/cors/-/cors-2.8.5.tgz", "cosmiconfig@^8.2.0": "https://registry.npmmirror.com/cosmiconfig/-/cosmiconfig-8.3.6.tgz", "create-jest@^29.7.0": "https://registry.npmmirror.com/create-jest/-/create-jest-29.7.0.tgz", "create-require@^1.1.0": "https://registry.npmmirror.com/create-require/-/create-require-1.1.1.tgz", "cross-env@^7.0.3": "https://registry.npmmirror.com/cross-env/-/cross-env-7.0.3.tgz", "cross-spawn@^7.0.1": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.6.tgz", "cross-spawn@^7.0.3": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.6.tgz", "cross-spawn@^7.0.6": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.6.tgz", "date-fns@3.6.0": "https://registry.npmmirror.com/date-fns/-/date-fns-3.6.0.tgz#f20ca4fe94f8b754951b24240676e8618c0206bf", "debug@4.x": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "debug@^4.1.0": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "debug@^4.1.1": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "debug@^4.3.1": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "debug@^4.3.2": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "debug@^4.3.4": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "debug@^4.3.5": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "debug@^4.4.0": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "decompress-response@^6.0.0": "https://registry.npmmirror.com/decompress-response/-/decompress-response-6.0.0.tgz", "dedent@^1.0.0": "https://registry.npmmirror.com/dedent/-/dedent-1.6.0.tgz", "deep-is@^0.1.3": "https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz", "deepmerge@^4.2.2": "https://registry.npmmirror.com/deepmerge/-/deepmerge-4.3.1.tgz", "defaults@^1.0.3": "https://registry.npmmirror.com/defaults/-/defaults-1.0.4.tgz", "defaults@^3.0.0": "https://registry.npmmirror.com/defaults/-/defaults-3.0.0.tgz", "defer-to-connect@^2.0.1": "https://registry.npmmirror.com/defer-to-connect/-/defer-to-connect-2.0.1.tgz", "delayed-stream@~1.0.0": "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz", "depd@2.0.0": "https://registry.npmmirror.com/depd/-/depd-2.0.0.tgz", "depd@^2.0.0": "https://registry.npmmirror.com/depd/-/depd-2.0.0.tgz", "detect-newline@^3.0.0": "https://registry.npmmirror.com/detect-newline/-/detect-newline-3.1.0.tgz", "dezalgo@^1.0.4": "https://registry.npmmirror.com/dezalgo/-/dezalgo-1.0.4.tgz", "diff-sequences@^29.6.3": "https://registry.npmmirror.com/diff-sequences/-/diff-sequences-29.6.3.tgz", "diff@^4.0.1": "https://registry.npmmirror.com/diff/-/diff-4.0.2.tgz", "dotenv-expand@12.0.1": "https://registry.npmmirror.com/dotenv-expand/-/dotenv-expand-12.0.1.tgz", "dotenv@16.4.7": "https://registry.npmmirror.com/dotenv/-/dotenv-16.4.7.tgz", "dotenv@^16.4.5": "https://registry.npmmirror.com/dotenv/-/dotenv-16.6.1.tgz", "dunder-proto@^1.0.1": "https://registry.npmmirror.com/dunder-proto/-/dunder-proto-1.0.1.tgz", "eastasianwidth@^0.2.0": "https://registry.npmmirror.com/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "ecdsa-sig-formatter@1.0.11": "https://registry.npmmirror.com/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "ee-first@1.1.1": "https://registry.npmmirror.com/ee-first/-/ee-first-1.1.1.tgz", "ejs@^3.1.10": "https://registry.npmmirror.com/ejs/-/ejs-3.1.10.tgz", "electron-to-chromium@^1.5.173": "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.5.178.tgz", "emittery@^0.13.1": "https://registry.npmmirror.com/emittery/-/emittery-0.13.1.tgz", "emoji-regex@^8.0.0": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "emoji-regex@^9.2.2": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.2.2.tgz", "encodeurl@^2.0.0": "https://registry.npmmirror.com/encodeurl/-/encodeurl-2.0.0.tgz", "enhanced-resolve@^5.0.0": "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-5.18.2.tgz", "enhanced-resolve@^5.17.1": "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-5.18.2.tgz", "enhanced-resolve@^5.7.0": "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-5.18.2.tgz", "error-ex@^1.3.1": "https://registry.npmmirror.com/error-ex/-/error-ex-1.3.2.tgz", "es-define-property@^1.0.1": "https://registry.npmmirror.com/es-define-property/-/es-define-property-1.0.1.tgz", "es-errors@^1.3.0": "https://registry.npmmirror.com/es-errors/-/es-errors-1.3.0.tgz", "es-module-lexer@^1.2.1": "https://registry.npmmirror.com/es-module-lexer/-/es-module-lexer-1.7.0.tgz", "es-object-atoms@^1.0.0": "https://registry.npmmirror.com/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "es-object-atoms@^1.1.1": "https://registry.npmmirror.com/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "es-set-tostringtag@^2.1.0": "https://registry.npmmirror.com/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "escalade@^3.1.1": "https://registry.npmmirror.com/escalade/-/escalade-3.2.0.tgz", "escalade@^3.2.0": "https://registry.npmmirror.com/escalade/-/escalade-3.2.0.tgz", "escape-html@^1.0.3": "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz", "escape-string-regexp@^2.0.0": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "escape-string-regexp@^4.0.0": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "eslint-config-prettier@^10.0.1": "https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-10.1.5.tgz", "eslint-plugin-prettier@^5.2.2": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.5.1.tgz", "eslint-scope@5.1.1": "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz", "eslint-scope@^8.4.0": "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-8.4.0.tgz", "eslint-visitor-keys@^3.4.3": "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint-visitor-keys@^4.2.1": "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz", "eslint@^9.18.0": "https://registry.npmmirror.com/eslint/-/eslint-9.30.0.tgz", "espree@^10.0.1": "https://registry.npmmirror.com/espree/-/espree-10.4.0.tgz", "espree@^10.4.0": "https://registry.npmmirror.com/espree/-/espree-10.4.0.tgz", "esprima@^4.0.0": "https://registry.npmmirror.com/esprima/-/esprima-4.0.1.tgz", "esprima@^4.0.1": "https://registry.npmmirror.com/esprima/-/esprima-4.0.1.tgz", "esquery@^1.5.0": "https://registry.npmmirror.com/esquery/-/esquery-1.6.0.tgz", "esrecurse@^4.3.0": "https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz", "estraverse@^4.1.1": "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz", "estraverse@^5.1.0": "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz", "estraverse@^5.2.0": "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz", "esutils@^2.0.2": "https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz", "etag@^1.8.1": "https://registry.npmmirror.com/etag/-/etag-1.8.1.tgz", "events@^3.2.0": "https://registry.npmmirror.com/events/-/events-3.3.0.tgz", "execa@^5.0.0": "https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz", "execa@^5.1.1": "https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz", "exit@^0.1.2": "https://registry.npmmirror.com/exit/-/exit-0.1.2.tgz", "expect@^29.0.0": "https://registry.npmmirror.com/expect/-/expect-29.7.0.tgz", "expect@^29.7.0": "https://registry.npmmirror.com/expect/-/expect-29.7.0.tgz", "express@5.1.0": "https://registry.npmmirror.com/express/-/express-5.1.0.tgz", "ext-list@^2.0.0": "https://registry.npmmirror.com/ext-list/-/ext-list-2.2.2.tgz", "ext-name@^5.0.0": "https://registry.npmmirror.com/ext-name/-/ext-name-5.0.0.tgz", "external-editor@^3.1.0": "https://registry.npmmirror.com/external-editor/-/external-editor-3.1.0.tgz", "fast-deep-equal@^3.1.1": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-deep-equal@^3.1.3": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-diff@^1.1.2": "https://registry.npmmirror.com/fast-diff/-/fast-diff-1.3.0.tgz", "fast-fifo@^1.2.0": "https://registry.npmmirror.com/fast-fifo/-/fast-fifo-1.3.2.tgz", "fast-fifo@^1.3.2": "https://registry.npmmirror.com/fast-fifo/-/fast-fifo-1.3.2.tgz", "fast-glob@^3.2.5": "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.3.tgz", "fast-glob@^3.3.2": "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.3.tgz", "fast-json-stable-stringify@2.x": "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-json-stable-stringify@^2.0.0": "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-json-stable-stringify@^2.1.0": "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-levenshtein@^2.0.6": "https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "fast-safe-stringify@2.1.1": "https://registry.npmmirror.com/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz", "fast-safe-stringify@^2.1.1": "https://registry.npmmirror.com/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz", "fast-uri@^3.0.1": "https://registry.npmmirror.com/fast-uri/-/fast-uri-3.0.6.tgz", "fastq@^1.6.0": "https://registry.npmmirror.com/fastq/-/fastq-1.19.1.tgz", "fb-watchman@^2.0.0": "https://registry.npmmirror.com/fb-watchman/-/fb-watchman-2.0.2.tgz", "fflate@^0.8.2": "https://registry.npmmirror.com/fflate/-/fflate-0.8.2.tgz", "file-entry-cache@^8.0.0": "https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-8.0.0.tgz", "file-type@21.0.0": "https://registry.npmmirror.com/file-type/-/file-type-21.0.0.tgz", "file-type@^19.0.0": "https://registry.npmmirror.com/file-type/-/file-type-19.6.0.tgz", "file-type@^19.6.0": "https://registry.npmmirror.com/file-type/-/file-type-19.6.0.tgz", "filelist@^1.0.4": "https://registry.npmmirror.com/filelist/-/filelist-1.0.4.tgz", "filename-reserved-regex@^3.0.0": "https://registry.npmmirror.com/filename-reserved-regex/-/filename-reserved-regex-3.0.0.tgz", "filenamify@^6.0.0": "https://registry.npmmirror.com/filenamify/-/filenamify-6.0.0.tgz", "fill-range@^7.1.1": "https://registry.npmmirror.com/fill-range/-/fill-range-7.1.1.tgz", "finalhandler@^2.1.0": "https://registry.npmmirror.com/finalhandler/-/finalhandler-2.1.0.tgz", "find-up@^4.0.0": "https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz", "find-up@^4.1.0": "https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz", "find-up@^5.0.0": "https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz", "find-versions@^5.0.0": "https://registry.npmmirror.com/find-versions/-/find-versions-5.1.0.tgz", "flat-cache@^4.0.0": "https://registry.npmmirror.com/flat-cache/-/flat-cache-4.0.1.tgz", "flatted@^3.2.9": "https://registry.npmmirror.com/flatted/-/flatted-3.3.3.tgz", "follow-redirects@^1.15.6": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.9.tgz", "foreground-child@^3.1.0": "https://registry.npmmirror.com/foreground-child/-/foreground-child-3.3.1.tgz", "fork-ts-checker-webpack-plugin@9.1.0": "https://registry.npmmirror.com/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-9.1.0.tgz", "form-data-encoder@^2.1.2": "https://registry.npmmirror.com/form-data-encoder/-/form-data-encoder-2.1.4.tgz", "form-data@^4.0.0": "https://registry.npmmirror.com/form-data/-/form-data-4.0.3.tgz", "formidable@^3.5.4": "https://registry.npmmirror.com/formidable/-/formidable-3.5.4.tgz", "forwarded@0.2.0": "https://registry.npmmirror.com/forwarded/-/forwarded-0.2.0.tgz", "fresh@^2.0.0": "https://registry.npmmirror.com/fresh/-/fresh-2.0.0.tgz", "fs-extra@^10.0.0": "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz", "fs-monkey@^1.0.4": "https://registry.npmmirror.com/fs-monkey/-/fs-monkey-1.0.6.tgz", "fs.realpath@^1.0.0": "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz", "fsevents@^2.3.2": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "function-bind@^1.1.2": "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmmirror.com/gensync/-/gensync-1.0.0-beta.2.tgz", "get-caller-file@^2.0.5": "https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz", "get-intrinsic@^1.2.5": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-intrinsic@^1.2.6": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-intrinsic@^1.3.0": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-package-type@^0.1.0": "https://registry.npmmirror.com/get-package-type/-/get-package-type-0.1.0.tgz", "get-proto@^1.0.1": "https://registry.npmmirror.com/get-proto/-/get-proto-1.0.1.tgz", "get-stream@^6.0.0": "https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz", "get-stream@^6.0.1": "https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz", "get-stream@^9.0.1": "https://registry.npmmirror.com/get-stream/-/get-stream-9.0.1.tgz", "glob-parent@^5.1.2": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "glob-parent@^6.0.2": "https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz", "glob-to-regexp@^0.4.1": "https://registry.npmmirror.com/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz", "glob@11.0.1": "https://registry.npmmirror.com/glob/-/glob-11.0.1.tgz", "glob@^7.1.3": "https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz", "glob@^7.1.4": "https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz", "globals@^11.1.0": "https://registry.npmmirror.com/globals/-/globals-11.12.0.tgz", "globals@^14.0.0": "https://registry.npmmirror.com/globals/-/globals-14.0.0.tgz", "globals@^16.0.0": "https://registry.npmmirror.com/globals/-/globals-16.2.0.tgz", "gopd@^1.2.0": "https://registry.npmmirror.com/gopd/-/gopd-1.2.0.tgz", "got@^13.0.0": "https://registry.npmmirror.com/got/-/got-13.0.0.tgz", "graceful-fs@^4.1.2": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.1.6": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.0": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.11": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.4": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.9": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz", "graphemer@^1.4.0": "https://registry.npmmirror.com/graphemer/-/graphemer-1.4.0.tgz", "has-flag@^4.0.0": "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz", "has-own-prop@^2.0.0": "https://registry.npmmirror.com/has-own-prop/-/has-own-prop-2.0.0.tgz", "has-symbols@^1.0.3": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.1.0.tgz", "has-symbols@^1.1.0": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.1.0.tgz", "has-tostringtag@^1.0.2": "https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "hasown@^2.0.2": "https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz", "html-escaper@^2.0.0": "https://registry.npmmirror.com/html-escaper/-/html-escaper-2.0.2.tgz", "http-cache-semantics@^4.1.1": "https://registry.npmmirror.com/http-cache-semantics/-/http-cache-semantics-4.2.0.tgz", "http-errors@2.0.0": "https://registry.npmmirror.com/http-errors/-/http-errors-2.0.0.tgz", "http-errors@^2.0.0": "https://registry.npmmirror.com/http-errors/-/http-errors-2.0.0.tgz", "http2-wrapper@^2.1.10": "https://registry.npmmirror.com/http2-wrapper/-/http2-wrapper-2.2.1.tgz", "human-signals@^2.1.0": "https://registry.npmmirror.com/human-signals/-/human-signals-2.1.0.tgz", "iconv-lite@0.6.3": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz", "iconv-lite@^0.4.24": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz", "iconv-lite@^0.6.3": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz", "ieee754@^1.1.13": "https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz", "ieee754@^1.2.1": "https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz", "ignore@^5.2.0": "https://registry.npmmirror.com/ignore/-/ignore-5.3.2.tgz", "ignore@^7.0.0": "https://registry.npmmirror.com/ignore/-/ignore-7.0.5.tgz", "import-fresh@^3.2.1": "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.1.tgz", "import-fresh@^3.3.0": "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.1.tgz", "import-local@^3.0.2": "https://registry.npmmirror.com/import-local/-/import-local-3.2.0.tgz", "imurmurhash@^0.1.4": "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz", "inflight@^1.0.4": "https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@2.0.4": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.3": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.4": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inspect-with-kind@^1.0.5": "https://registry.npmmirror.com/inspect-with-kind/-/inspect-with-kind-1.0.5.tgz", "ipaddr.js@1.9.1": "https://registry.npmmirror.com/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "is-arrayish@^0.2.1": "https://registry.npmmirror.com/is-arrayish/-/is-arrayish-0.2.1.tgz", "is-core-module@^2.16.0": "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.16.1.tgz", "is-extglob@^2.1.1": "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "is-generator-fn@^2.0.0": "https://registry.npmmirror.com/is-generator-fn/-/is-generator-fn-2.1.0.tgz", "is-glob@^4.0.0": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.1": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.3": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "is-interactive@^1.0.0": "https://registry.npmmirror.com/is-interactive/-/is-interactive-1.0.0.tgz", "is-number@^7.0.0": "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz", "is-plain-obj@^1.0.0": "https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-1.1.0.tgz", "is-plain-obj@^1.1.0": "https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-1.1.0.tgz", "is-promise@^4.0.0": "https://registry.npmmirror.com/is-promise/-/is-promise-4.0.0.tgz", "is-stream@^2.0.0": "https://registry.npmmirror.com/is-stream/-/is-stream-2.0.1.tgz", "is-stream@^2.0.1": "https://registry.npmmirror.com/is-stream/-/is-stream-2.0.1.tgz", "is-stream@^4.0.1": "https://registry.npmmirror.com/is-stream/-/is-stream-4.0.1.tgz", "is-unicode-supported@^0.1.0": "https://registry.npmmirror.com/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "isexe@^2.0.0": "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz", "istanbul-lib-coverage@^3.0.0": "https://registry.npmmirror.com/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz", "istanbul-lib-coverage@^3.2.0": "https://registry.npmmirror.com/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz", "istanbul-lib-instrument@^5.0.4": "https://registry.npmmirror.com/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz", "istanbul-lib-instrument@^6.0.0": "https://registry.npmmirror.com/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz", "istanbul-lib-report@^3.0.0": "https://registry.npmmirror.com/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz", "istanbul-lib-source-maps@^4.0.0": "https://registry.npmmirror.com/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz", "istanbul-reports@^3.1.3": "https://registry.npmmirror.com/istanbul-reports/-/istanbul-reports-3.1.7.tgz", "iterare@1.2.1": "https://registry.npmmirror.com/iterare/-/iterare-1.2.1.tgz", "jackspeak@^4.0.1": "https://registry.npmmirror.com/jackspeak/-/jackspeak-4.1.1.tgz", "jake@^10.8.5": "https://registry.npmmirror.com/jake/-/jake-10.9.2.tgz", "jest-changed-files@^29.7.0": "https://registry.npmmirror.com/jest-changed-files/-/jest-changed-files-29.7.0.tgz", "jest-circus@^29.7.0": "https://registry.npmmirror.com/jest-circus/-/jest-circus-29.7.0.tgz", "jest-cli@^29.7.0": "https://registry.npmmirror.com/jest-cli/-/jest-cli-29.7.0.tgz", "jest-config@^29.7.0": "https://registry.npmmirror.com/jest-config/-/jest-config-29.7.0.tgz", "jest-diff@^29.7.0": "https://registry.npmmirror.com/jest-diff/-/jest-diff-29.7.0.tgz", "jest-docblock@^29.7.0": "https://registry.npmmirror.com/jest-docblock/-/jest-docblock-29.7.0.tgz", "jest-each@^29.7.0": "https://registry.npmmirror.com/jest-each/-/jest-each-29.7.0.tgz", "jest-environment-node@^29.7.0": "https://registry.npmmirror.com/jest-environment-node/-/jest-environment-node-29.7.0.tgz", "jest-get-type@^29.6.3": "https://registry.npmmirror.com/jest-get-type/-/jest-get-type-29.6.3.tgz", "jest-haste-map@^29.7.0": "https://registry.npmmirror.com/jest-haste-map/-/jest-haste-map-29.7.0.tgz", "jest-leak-detector@^29.7.0": "https://registry.npmmirror.com/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz", "jest-matcher-utils@^29.7.0": "https://registry.npmmirror.com/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz", "jest-message-util@^29.7.0": "https://registry.npmmirror.com/jest-message-util/-/jest-message-util-29.7.0.tgz", "jest-mock@^29.7.0": "https://registry.npmmirror.com/jest-mock/-/jest-mock-29.7.0.tgz", "jest-pnp-resolver@^1.2.2": "https://registry.npmmirror.com/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz", "jest-regex-util@^29.6.3": "https://registry.npmmirror.com/jest-regex-util/-/jest-regex-util-29.6.3.tgz", "jest-resolve-dependencies@^29.7.0": "https://registry.npmmirror.com/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz", "jest-resolve@^29.7.0": "https://registry.npmmirror.com/jest-resolve/-/jest-resolve-29.7.0.tgz", "jest-runner@^29.7.0": "https://registry.npmmirror.com/jest-runner/-/jest-runner-29.7.0.tgz", "jest-runtime@^29.7.0": "https://registry.npmmirror.com/jest-runtime/-/jest-runtime-29.7.0.tgz", "jest-snapshot@^29.7.0": "https://registry.npmmirror.com/jest-snapshot/-/jest-snapshot-29.7.0.tgz", "jest-util@^29.7.0": "https://registry.npmmirror.com/jest-util/-/jest-util-29.7.0.tgz", "jest-validate@^29.7.0": "https://registry.npmmirror.com/jest-validate/-/jest-validate-29.7.0.tgz", "jest-watcher@^29.7.0": "https://registry.npmmirror.com/jest-watcher/-/jest-watcher-29.7.0.tgz", "jest-worker@^27.4.5": "https://registry.npmmirror.com/jest-worker/-/jest-worker-27.5.1.tgz", "jest-worker@^29.7.0": "https://registry.npmmirror.com/jest-worker/-/jest-worker-29.7.0.tgz", "jest@^29.7.0": "https://registry.npmmirror.com/jest/-/jest-29.7.0.tgz", "joi@^17.13.3": "https://registry.npmmirror.com/joi/-/joi-17.13.3.tgz", "js-tokens@^4.0.0": "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz", "js-yaml@4.1.0": "https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz", "js-yaml@^3.13.1": "https://registry.npmmirror.com/js-yaml/-/js-yaml-3.14.1.tgz", "js-yaml@^4.1.0": "https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz", "jsesc@^3.0.2": "https://registry.npmmirror.com/jsesc/-/jsesc-3.1.0.tgz", "json-buffer@3.0.1": "https://registry.npmmirror.com/json-buffer/-/json-buffer-3.0.1.tgz", "json-parse-even-better-errors@^2.3.0": "https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "json-parse-even-better-errors@^2.3.1": "https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "json-schema-traverse@^0.4.1": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "json-schema-traverse@^1.0.0": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "json-stable-stringify-without-jsonify@^1.0.1": "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "json5@^2.2.2": "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz", "json5@^2.2.3": "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz", "jsonc-parser@3.3.1": "https://registry.npmmirror.com/jsonc-parser/-/jsonc-parser-3.3.1.tgz", "jsonfile@^6.0.1": "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz", "jsonwebtoken@9.0.2": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz", "jsonwebtoken@^9.0.0": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz", "jwa@^1.4.1": "https://registry.npmmirror.com/jwa/-/jwa-1.4.2.tgz", "jws@^3.2.2": "https://registry.npmmirror.com/jws/-/jws-3.2.2.tgz", "kareem@2.6.3": "https://registry.npmmirror.com/kareem/-/kareem-2.6.3.tgz", "keyv@^4.5.3": "https://registry.npmmirror.com/keyv/-/keyv-4.5.4.tgz", "keyv@^4.5.4": "https://registry.npmmirror.com/keyv/-/keyv-4.5.4.tgz", "kind-of@^6.0.2": "https://registry.npmmirror.com/kind-of/-/kind-of-6.0.3.tgz", "kleur@^3.0.3": "https://registry.npmmirror.com/kleur/-/kleur-3.0.3.tgz", "leven@^3.1.0": "https://registry.npmmirror.com/leven/-/leven-3.1.0.tgz", "levn@^0.4.1": "https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz", "libphonenumber-js@^1.11.1": "https://registry.npmmirror.com/libphonenumber-js/-/libphonenumber-js-1.12.9.tgz", "lines-and-columns@^1.1.6": "https://registry.npmmirror.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "load-esm@1.0.2": "https://registry.npmmirror.com/load-esm/-/load-esm-1.0.2.tgz", "loader-runner@^4.2.0": "https://registry.npmmirror.com/loader-runner/-/loader-runner-4.3.0.tgz", "locate-path@^5.0.0": "https://registry.npmmirror.com/locate-path/-/locate-path-5.0.0.tgz", "locate-path@^6.0.0": "https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz", "lodash.includes@^4.3.0": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-4.3.0.tgz", "lodash.isboolean@^3.0.3": "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz", "lodash.isinteger@^4.0.4": "https://registry.npmmirror.com/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz", "lodash.isnumber@^3.0.3": "https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz", "lodash.isplainobject@^4.0.6": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "lodash.isstring@^4.0.1": "https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "lodash.memoize@^4.1.2": "https://registry.npmmirror.com/lodash.memoize/-/lodash.memoize-4.1.2.tgz", "lodash.merge@^4.6.2": "https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz", "lodash.once@^4.0.0": "https://registry.npmmirror.com/lodash.once/-/lodash.once-4.1.1.tgz", "lodash@4.17.21": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.21": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "log-symbols@^4.1.0": "https://registry.npmmirror.com/log-symbols/-/log-symbols-4.1.0.tgz", "lowercase-keys@^3.0.0": "https://registry.npmmirror.com/lowercase-keys/-/lowercase-keys-3.0.0.tgz", "lru-cache@^11.0.0": "https://registry.npmmirror.com/lru-cache/-/lru-cache-11.1.0.tgz", "lru-cache@^5.1.1": "https://registry.npmmirror.com/lru-cache/-/lru-cache-5.1.1.tgz", "magic-string@0.30.17": "https://registry.npmmirror.com/magic-string/-/magic-string-0.30.17.tgz", "make-dir@^4.0.0": "https://registry.npmmirror.com/make-dir/-/make-dir-4.0.0.tgz", "make-error@^1.1.1": "https://registry.npmmirror.com/make-error/-/make-error-1.3.6.tgz", "make-error@^1.3.6": "https://registry.npmmirror.com/make-error/-/make-error-1.3.6.tgz", "makeerror@1.0.12": "https://registry.npmmirror.com/makeerror/-/makeerror-1.0.12.tgz", "math-intrinsics@^1.1.0": "https://registry.npmmirror.com/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "media-typer@0.3.0": "https://registry.npmmirror.com/media-typer/-/media-typer-0.3.0.tgz", "media-typer@^1.1.0": "https://registry.npmmirror.com/media-typer/-/media-typer-1.1.0.tgz", "memfs@^3.4.1": "https://registry.npmmirror.com/memfs/-/memfs-3.6.0.tgz", "memory-pager@^1.0.2": "https://registry.npmmirror.com/memory-pager/-/memory-pager-1.5.0.tgz", "merge-descriptors@^2.0.0": "https://registry.npmmirror.com/merge-descriptors/-/merge-descriptors-2.0.0.tgz", "merge-stream@^2.0.0": "https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz", "merge2@^1.3.0": "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz", "methods@^1.1.2": "https://registry.npmmirror.com/methods/-/methods-1.1.2.tgz", "micromatch@^4.0.0": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.8.tgz", "micromatch@^4.0.4": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.8.tgz", "micromatch@^4.0.8": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.8.tgz", "mime-db@1.52.0": "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz", "mime-db@^1.28.0": "https://registry.npmmirror.com/mime-db/-/mime-db-1.54.0.tgz", "mime-db@^1.54.0": "https://registry.npmmirror.com/mime-db/-/mime-db-1.54.0.tgz", "mime-types@^2.1.12": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "mime-types@^2.1.27": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "mime-types@^3.0.0": "https://registry.npmmirror.com/mime-types/-/mime-types-3.0.1.tgz", "mime-types@^3.0.1": "https://registry.npmmirror.com/mime-types/-/mime-types-3.0.1.tgz", "mime-types@~2.1.24": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "mime@2.6.0": "https://registry.npmmirror.com/mime/-/mime-2.6.0.tgz", "mimic-fn@^2.1.0": "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz", "mimic-response@^3.1.0": "https://registry.npmmirror.com/mimic-response/-/mimic-response-3.1.0.tgz", "mimic-response@^4.0.0": "https://registry.npmmirror.com/mimic-response/-/mimic-response-4.0.0.tgz", "minimatch@^10.0.0": "https://registry.npmmirror.com/minimatch/-/minimatch-10.0.3.tgz", "minimatch@^3.0.4": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.1": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.2": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^5.0.1": "https://registry.npmmirror.com/minimatch/-/minimatch-5.1.6.tgz", "minimatch@^9.0.3": "https://registry.npmmirror.com/minimatch/-/minimatch-9.0.5.tgz", "minimatch@^9.0.4": "https://registry.npmmirror.com/minimatch/-/minimatch-9.0.5.tgz", "minimist@^1.2.6": "https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz", "minipass@^7.1.2": "https://registry.npmmirror.com/minipass/-/minipass-7.1.2.tgz", "mkdirp@^0.5.6": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz", "mongodb-connection-string-url@^3.0.0": "https://registry.npmmirror.com/mongodb-connection-string-url/-/mongodb-connection-string-url-3.0.2.tgz", "mongodb@~6.17.0": "https://registry.npmmirror.com/mongodb/-/mongodb-6.17.0.tgz", "mongoose@^8.15.1": "https://registry.npmmirror.com/mongoose/-/mongoose-8.16.1.tgz", "mpath@0.9.0": "https://registry.npmmirror.com/mpath/-/mpath-0.9.0.tgz", "mquery@5.0.0": "https://registry.npmmirror.com/mquery/-/mquery-5.0.0.tgz", "ms@2.1.3": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "ms@^2.1.1": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "ms@^2.1.3": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "multer@2.0.1": "https://registry.npmmirror.com/multer/-/multer-2.0.1.tgz", "mute-stream@^2.0.0": "https://registry.npmmirror.com/mute-stream/-/mute-stream-2.0.0.tgz", "natural-compare@^1.4.0": "https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz", "negotiator@^1.0.0": "https://registry.npmmirror.com/negotiator/-/negotiator-1.0.0.tgz", "neo-async@^2.6.2": "https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz", "node-abort-controller@^3.0.1": "https://registry.npmmirror.com/node-abort-controller/-/node-abort-controller-3.1.1.tgz", "node-addon-api@^8.3.0": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.4.0.tgz", "node-emoji@1.11.0": "https://registry.npmmirror.com/node-emoji/-/node-emoji-1.11.0.tgz", "node-gyp-build@^4.8.4": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.8.4.tgz", "node-int64@^0.4.0": "https://registry.npmmirror.com/node-int64/-/node-int64-0.4.0.tgz", "node-releases@^2.0.19": "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.19.tgz", "normalize-path@^3.0.0": "https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-url@^8.0.0": "https://registry.npmmirror.com/normalize-url/-/normalize-url-8.0.2.tgz", "npm-run-path@^4.0.1": "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-4.0.1.tgz", "object-assign@^4": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "object-assign@^4.1.1": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "object-inspect@^1.13.3": "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.13.4.tgz", "on-finished@^2.4.1": "https://registry.npmmirror.com/on-finished/-/on-finished-2.4.1.tgz", "once@^1.3.0": "https://registry.npmmirror.com/once/-/once-1.4.0.tgz", "once@^1.4.0": "https://registry.npmmirror.com/once/-/once-1.4.0.tgz", "onetime@^5.1.0": "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz", "onetime@^5.1.2": "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz", "optionator@^0.9.3": "https://registry.npmmirror.com/optionator/-/optionator-0.9.4.tgz", "ora@5.4.1": "https://registry.npmmirror.com/ora/-/ora-5.4.1.tgz", "os-tmpdir@~1.0.2": "https://registry.npmmirror.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "p-cancelable@^3.0.0": "https://registry.npmmirror.com/p-cancelable/-/p-cancelable-3.0.0.tgz", "p-limit@^2.2.0": "https://registry.npmmirror.com/p-limit/-/p-limit-2.3.0.tgz", "p-limit@^3.0.2": "https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz", "p-limit@^3.1.0": "https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz", "p-locate@^4.1.0": "https://registry.npmmirror.com/p-locate/-/p-locate-4.1.0.tgz", "p-locate@^5.0.0": "https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz", "p-try@^2.0.0": "https://registry.npmmirror.com/p-try/-/p-try-2.2.0.tgz", "package-json-from-dist@^1.0.0": "https://registry.npmmirror.com/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", "parent-module@^1.0.0": "https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz", "parse-json@^5.2.0": "https://registry.npmmirror.com/parse-json/-/parse-json-5.2.0.tgz", "parseurl@^1.3.3": "https://registry.npmmirror.com/parseurl/-/parseurl-1.3.3.tgz", "passport-jwt@^4.0.1": "https://registry.npmmirror.com/passport-jwt/-/passport-jwt-4.0.1.tgz", "passport-strategy@1.x.x": "https://registry.npmmirror.com/passport-strategy/-/passport-strategy-1.0.0.tgz", "passport-strategy@^1.0.0": "https://registry.npmmirror.com/passport-strategy/-/passport-strategy-1.0.0.tgz", "passport@^0.7.0": "https://registry.npmmirror.com/passport/-/passport-0.7.0.tgz", "path-exists@^4.0.0": "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz", "path-is-absolute@^1.0.0": "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "path-key@^3.0.0": "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz", "path-key@^3.1.0": "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz", "path-parse@^1.0.7": "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz", "path-scurry@^2.0.0": "https://registry.npmmirror.com/path-scurry/-/path-scurry-2.0.0.tgz", "path-to-regexp@8.2.0": "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-8.2.0.tgz", "path-to-regexp@^8.0.0": "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-8.2.0.tgz", "path-type@^4.0.0": "https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz", "pause@0.0.1": "https://registry.npmmirror.com/pause/-/pause-0.0.1.tgz", "peek-readable@^5.3.1": "https://registry.npmmirror.com/peek-readable/-/peek-readable-5.4.2.tgz", "pend@~1.2.0": "https://registry.npmmirror.com/pend/-/pend-1.2.0.tgz", "picocolors@^1.1.1": "https://registry.npmmirror.com/picocolors/-/picocolors-1.1.1.tgz", "picomatch@4.0.2": "https://registry.npmmirror.com/picomatch/-/picomatch-4.0.2.tgz", "picomatch@^2.0.4": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.3": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.3.1": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "pirates@^4.0.4": "https://registry.npmmirror.com/pirates/-/pirates-4.0.7.tgz", "piscina@^4.3.1": "https://registry.npmmirror.com/piscina/-/piscina-4.9.2.tgz", "pkg-dir@^4.2.0": "https://registry.npmmirror.com/pkg-dir/-/pkg-dir-4.2.0.tgz", "pluralize@8.0.0": "https://registry.npmmirror.com/pluralize/-/pluralize-8.0.0.tgz", "prelude-ls@^1.2.1": "https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz", "prettier-linter-helpers@^1.0.0": "https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz", "prettier@^3.4.2": "https://registry.npmmirror.com/prettier/-/prettier-3.6.2.tgz", "pretty-format@^29.0.0": "https://registry.npmmirror.com/pretty-format/-/pretty-format-29.7.0.tgz", "pretty-format@^29.7.0": "https://registry.npmmirror.com/pretty-format/-/pretty-format-29.7.0.tgz", "prompts@^2.0.1": "https://registry.npmmirror.com/prompts/-/prompts-2.4.2.tgz", "proxy-addr@^2.0.7": "https://registry.npmmirror.com/proxy-addr/-/proxy-addr-2.0.7.tgz", "proxy-from-env@^1.1.0": "https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "punycode@^2.1.0": "https://registry.npmmirror.com/punycode/-/punycode-2.3.1.tgz", "punycode@^2.3.1": "https://registry.npmmirror.com/punycode/-/punycode-2.3.1.tgz", "pure-rand@^6.0.0": "https://registry.npmmirror.com/pure-rand/-/pure-rand-6.1.0.tgz", "qs@^6.11.0": "https://registry.npmmirror.com/qs/-/qs-6.14.0.tgz", "qs@^6.14.0": "https://registry.npmmirror.com/qs/-/qs-6.14.0.tgz", "queue-microtask@^1.2.2": "https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz", "quick-lru@^5.1.1": "https://registry.npmmirror.com/quick-lru/-/quick-lru-5.1.1.tgz", "randombytes@^2.1.0": "https://registry.npmmirror.com/randombytes/-/randombytes-2.1.0.tgz", "range-parser@^1.2.1": "https://registry.npmmirror.com/range-parser/-/range-parser-1.2.1.tgz", "raw-body@^3.0.0": "https://registry.npmmirror.com/raw-body/-/raw-body-3.0.0.tgz", "react-is@^18.0.0": "https://registry.npmmirror.com/react-is/-/react-is-18.3.1.tgz", "readable-stream@^3.0.2": "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz", "readable-stream@^3.4.0": "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz", "readdirp@^4.0.1": "https://registry.npmmirror.com/readdirp/-/readdirp-4.1.2.tgz", "reflect-metadata@^0.2.2": "https://registry.npmmirror.com/reflect-metadata/-/reflect-metadata-0.2.2.tgz", "repeat-string@^1.6.1": "https://registry.npmmirror.com/repeat-string/-/repeat-string-1.6.1.tgz", "require-directory@^2.1.1": "https://registry.npmmirror.com/require-directory/-/require-directory-2.1.1.tgz", "require-from-string@^2.0.2": "https://registry.npmmirror.com/require-from-string/-/require-from-string-2.0.2.tgz", "resolve-alpn@^1.2.0": "https://registry.npmmirror.com/resolve-alpn/-/resolve-alpn-1.2.1.tgz", "resolve-cwd@^3.0.0": "https://registry.npmmirror.com/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "resolve-from@^4.0.0": "https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz", "resolve-from@^5.0.0": "https://registry.npmmirror.com/resolve-from/-/resolve-from-5.0.0.tgz", "resolve.exports@^2.0.0": "https://registry.npmmirror.com/resolve.exports/-/resolve.exports-2.0.3.tgz", "resolve@^1.20.0": "https://registry.npmmirror.com/resolve/-/resolve-1.22.10.tgz", "responselike@^3.0.0": "https://registry.npmmirror.com/responselike/-/responselike-3.0.0.tgz", "restore-cursor@^3.1.0": "https://registry.npmmirror.com/restore-cursor/-/restore-cursor-3.1.0.tgz", "reusify@^1.0.4": "https://registry.npmmirror.com/reusify/-/reusify-1.1.0.tgz", "router@^2.2.0": "https://registry.npmmirror.com/router/-/router-2.2.0.tgz", "run-parallel@^1.1.9": "https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz", "rxjs@7.8.1": "https://registry.npmmirror.com/rxjs/-/rxjs-7.8.1.tgz", "rxjs@^7.8.1": "https://registry.npmmirror.com/rxjs/-/rxjs-7.8.2.tgz", "safe-buffer@5.2.1": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.0.1": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.0": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@~5.2.0": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "safer-buffer@>= 2.1.2 < 3": "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz", "safer-buffer@>= 2.1.2 < 3.0.0": "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz", "schema-utils@^3.1.1": "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.3.0.tgz", "schema-utils@^4.3.0": "https://registry.npmmirror.com/schema-utils/-/schema-utils-4.3.2.tgz", "seek-bzip@^2.0.0": "https://registry.npmmirror.com/seek-bzip/-/seek-bzip-2.0.0.tgz", "semver-regex@^4.0.5": "https://registry.npmmirror.com/semver-regex/-/semver-regex-4.0.5.tgz", "semver-truncate@^3.0.0": "https://registry.npmmirror.com/semver-truncate/-/semver-truncate-3.0.0.tgz", "semver@^6.3.0": "https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz", "semver@^6.3.1": "https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz", "semver@^7.3.4": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "semver@^7.3.5": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "semver@^7.3.8": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "semver@^7.5.3": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "semver@^7.5.4": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "semver@^7.6.0": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "semver@^7.7.2": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "send@^1.1.0": "https://registry.npmmirror.com/send/-/send-1.2.0.tgz", "send@^1.2.0": "https://registry.npmmirror.com/send/-/send-1.2.0.tgz", "serialize-javascript@^6.0.2": "https://registry.npmmirror.com/serialize-javascript/-/serialize-javascript-6.0.2.tgz", "serve-static@^2.2.0": "https://registry.npmmirror.com/serve-static/-/serve-static-2.2.0.tgz", "setprototypeof@1.2.0": "https://registry.npmmirror.com/setprototypeof/-/setprototypeof-1.2.0.tgz", "shebang-command@^2.0.0": "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz", "shebang-regex@^3.0.0": "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz", "side-channel-list@^1.0.0": "https://registry.npmmirror.com/side-channel-list/-/side-channel-list-1.0.0.tgz", "side-channel-map@^1.0.1": "https://registry.npmmirror.com/side-channel-map/-/side-channel-map-1.0.1.tgz", "side-channel-weakmap@^1.0.2": "https://registry.npmmirror.com/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "side-channel@^1.1.0": "https://registry.npmmirror.com/side-channel/-/side-channel-1.1.0.tgz", "sift@17.1.3": "https://registry.npmmirror.com/sift/-/sift-17.1.3.tgz", "signal-exit@^3.0.2": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.3": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.7": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^4.0.1": "https://registry.npmmirror.com/signal-exit/-/signal-exit-4.1.0.tgz", "signal-exit@^4.1.0": "https://registry.npmmirror.com/signal-exit/-/signal-exit-4.1.0.tgz", "sisteransi@^1.0.5": "https://registry.npmmirror.com/sisteransi/-/sisteransi-1.0.5.tgz", "slash@3.0.0": "https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz", "slash@^3.0.0": "https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz", "sort-keys-length@^1.0.0": "https://registry.npmmirror.com/sort-keys-length/-/sort-keys-length-1.0.1.tgz", "sort-keys@^1.0.0": "https://registry.npmmirror.com/sort-keys/-/sort-keys-1.1.2.tgz", "source-map-support@0.5.13": "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.13.tgz", "source-map-support@^0.5.21": "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.21.tgz", "source-map-support@~0.5.20": "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.21.tgz", "source-map@0.7.4": "https://registry.npmmirror.com/source-map/-/source-map-0.7.4.tgz", "source-map@^0.6.0": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "source-map@^0.6.1": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "source-map@^0.7.3": "https://registry.npmmirror.com/source-map/-/source-map-0.7.4.tgz", "source-map@^0.7.4": "https://registry.npmmirror.com/source-map/-/source-map-0.7.4.tgz", "sparse-bitfield@^3.0.3": "https://registry.npmmirror.com/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz", "sprintf-js@~1.0.2": "https://registry.npmmirror.com/sprintf-js/-/sprintf-js-1.0.3.tgz", "stack-utils@^2.0.3": "https://registry.npmmirror.com/stack-utils/-/stack-utils-2.0.6.tgz", "statuses@2.0.1": "https://registry.npmmirror.com/statuses/-/statuses-2.0.1.tgz", "statuses@^2.0.1": "https://registry.npmmirror.com/statuses/-/statuses-2.0.2.tgz", "streamsearch@^1.1.0": "https://registry.npmmirror.com/streamsearch/-/streamsearch-1.1.0.tgz", "streamx@^2.15.0": "https://registry.npmmirror.com/streamx/-/streamx-2.22.1.tgz", "string-length@^4.0.1": "https://registry.npmmirror.com/string-length/-/string-length-4.0.2.tgz", "string-width-cjs@npm:string-width@^4.2.0": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string-width@^4.1.0": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.0": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.3": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string-width@^5.0.1": "https://registry.npmmirror.com/string-width/-/string-width-5.1.2.tgz", "string-width@^5.1.2": "https://registry.npmmirror.com/string-width/-/string-width-5.1.2.tgz", "string_decoder@^1.1.1": "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.3.0.tgz", "strip-ansi-cjs@npm:strip-ansi@^6.0.1": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.0": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.1": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^7.0.1": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-7.1.0.tgz", "strip-bom@^3.0.0": "https://registry.npmmirror.com/strip-bom/-/strip-bom-3.0.0.tgz", "strip-bom@^4.0.0": "https://registry.npmmirror.com/strip-bom/-/strip-bom-4.0.0.tgz", "strip-dirs@^3.0.0": "https://registry.npmmirror.com/strip-dirs/-/strip-dirs-3.0.0.tgz", "strip-final-newline@^2.0.0": "https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "strip-json-comments@^3.1.1": "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "strtok3@^10.2.2": "https://registry.npmmirror.com/strtok3/-/strtok3-10.3.1.tgz", "strtok3@^9.0.1": "https://registry.npmmirror.com/strtok3/-/strtok3-9.1.1.tgz", "superagent@^10.2.1": "https://registry.npmmirror.com/superagent/-/superagent-10.2.1.tgz", "supertest@^7.0.0": "https://registry.npmmirror.com/supertest/-/supertest-7.1.1.tgz", "supports-color@^7.1.0": "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz", "supports-color@^8.0.0": "https://registry.npmmirror.com/supports-color/-/supports-color-8.1.1.tgz", "supports-preserve-symlinks-flag@^1.0.0": "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "swagger-ui-dist@5.21.0": "https://registry.npmmirror.com/swagger-ui-dist/-/swagger-ui-dist-5.21.0.tgz", "swagger-ui-dist@>=5.0.0": "https://registry.npmmirror.com/swagger-ui-dist/-/swagger-ui-dist-5.25.3.tgz", "swagger-ui-express@^5.0.1": "https://registry.npmmirror.com/swagger-ui-express/-/swagger-ui-express-5.0.1.tgz", "symbol-observable@4.0.0": "https://registry.npmmirror.com/symbol-observable/-/symbol-observable-4.0.0.tgz", "synckit@^0.11.7": "https://registry.npmmirror.com/synckit/-/synckit-0.11.8.tgz", "tapable@^2.1.1": "https://registry.npmmirror.com/tapable/-/tapable-2.2.2.tgz", "tapable@^2.2.0": "https://registry.npmmirror.com/tapable/-/tapable-2.2.2.tgz", "tapable@^2.2.1": "https://registry.npmmirror.com/tapable/-/tapable-2.2.2.tgz", "tar-stream@^3.1.7": "https://registry.npmmirror.com/tar-stream/-/tar-stream-3.1.7.tgz", "terser-webpack-plugin@^5.3.11": "https://registry.npmmirror.com/terser-webpack-plugin/-/terser-webpack-plugin-5.3.14.tgz", "terser@^5.31.1": "https://registry.npmmirror.com/terser/-/terser-5.43.1.tgz", "test-exclude@^6.0.0": "https://registry.npmmirror.com/test-exclude/-/test-exclude-6.0.0.tgz", "text-decoder@^1.1.0": "https://registry.npmmirror.com/text-decoder/-/text-decoder-1.2.3.tgz", "through@^2.3.8": "https://registry.npmmirror.com/through/-/through-2.3.8.tgz", "tmp@^0.0.33": "https://registry.npmmirror.com/tmp/-/tmp-0.0.33.tgz", "tmpl@1.0.5": "https://registry.npmmirror.com/tmpl/-/tmpl-1.0.5.tgz", "to-regex-range@^5.0.1": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz", "toidentifier@1.0.1": "https://registry.npmmirror.com/toidentifier/-/toidentifier-1.0.1.tgz", "token-types@^6.0.0": "https://registry.npmmirror.com/token-types/-/token-types-6.0.3.tgz", "tr46@^5.1.0": "https://registry.npmmirror.com/tr46/-/tr46-5.1.1.tgz", "tree-kill@1.2.2": "https://registry.npmmirror.com/tree-kill/-/tree-kill-1.2.2.tgz", "ts-api-utils@^2.1.0": "https://registry.npmmirror.com/ts-api-utils/-/ts-api-utils-2.1.0.tgz", "ts-jest@^29.2.5": "https://registry.npmmirror.com/ts-jest/-/ts-jest-29.4.0.tgz", "ts-loader@^9.5.2": "https://registry.npmmirror.com/ts-loader/-/ts-loader-9.5.2.tgz", "ts-node@^10.9.2": "https://registry.npmmirror.com/ts-node/-/ts-node-10.9.2.tgz", "tsconfig-paths-webpack-plugin@4.2.0": "https://registry.npmmirror.com/tsconfig-paths-webpack-plugin/-/tsconfig-paths-webpack-plugin-4.2.0.tgz", "tsconfig-paths@4.2.0": "https://registry.npmmirror.com/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz", "tsconfig-paths@^4.1.2": "https://registry.npmmirror.com/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz", "tsconfig-paths@^4.2.0": "https://registry.npmmirror.com/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz", "tslib@2.8.1": "https://registry.npmmirror.com/tslib/-/tslib-2.8.1.tgz", "tslib@^2.1.0": "https://registry.npmmirror.com/tslib/-/tslib-2.8.1.tgz", "type-check@^0.4.0": "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz", "type-check@~0.4.0": "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz", "type-detect@4.0.8": "https://registry.npmmirror.com/type-detect/-/type-detect-4.0.8.tgz", "type-fest@^0.21.3": "https://registry.npmmirror.com/type-fest/-/type-fest-0.21.3.tgz", "type-fest@^4.41.0": "https://registry.npmmirror.com/type-fest/-/type-fest-4.41.0.tgz", "type-is@^1.6.18": "https://registry.npmmirror.com/type-is/-/type-is-1.6.18.tgz", "type-is@^2.0.0": "https://registry.npmmirror.com/type-is/-/type-is-2.0.1.tgz", "type-is@^2.0.1": "https://registry.npmmirror.com/type-is/-/type-is-2.0.1.tgz", "typedarray@^0.0.6": "https://registry.npmmirror.com/typedarray/-/typedarray-0.0.6.tgz", "typescript-eslint@^8.20.0": "https://registry.npmmirror.com/typescript-eslint/-/typescript-eslint-8.35.1.tgz", "typescript@5.8.3": "https://registry.npmmirror.com/typescript/-/typescript-5.8.3.tgz", "typescript@^5.7.3": "https://registry.npmmirror.com/typescript/-/typescript-5.8.3.tgz", "uid@2.0.2": "https://registry.npmmirror.com/uid/-/uid-2.0.2.tgz", "uint8array-extras@^1.3.0": "https://registry.npmmirror.com/uint8array-extras/-/uint8array-extras-1.4.0.tgz", "uint8array-extras@^1.4.0": "https://registry.npmmirror.com/uint8array-extras/-/uint8array-extras-1.4.0.tgz", "unbzip2-stream@^1.4.3": "https://registry.npmmirror.com/unbzip2-stream/-/unbzip2-stream-1.4.3.tgz", "undici-types@~7.8.0": "https://registry.npmmirror.com/undici-types/-/undici-types-7.8.0.tgz", "universalify@^2.0.0": "https://registry.npmmirror.com/universalify/-/universalify-2.0.1.tgz", "unpipe@1.0.0": "https://registry.npmmirror.com/unpipe/-/unpipe-1.0.0.tgz", "update-browserslist-db@^1.1.3": "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "uri-js@^4.2.2": "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz", "util-deprecate@^1.0.1": "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz", "utils-merge@^1.0.1": "https://registry.npmmirror.com/utils-merge/-/utils-merge-1.0.1.tgz", "uuid@^11.1.0": "https://registry.npmmirror.com/uuid/-/uuid-11.1.0.tgz", "v8-compile-cache-lib@^3.0.1": "https://registry.npmmirror.com/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz", "v8-to-istanbul@^9.0.1": "https://registry.npmmirror.com/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz", "validator@^13.9.0": "https://registry.npmmirror.com/validator/-/validator-13.15.15.tgz", "vary@^1": "https://registry.npmmirror.com/vary/-/vary-1.1.2.tgz", "vary@^1.1.2": "https://registry.npmmirror.com/vary/-/vary-1.1.2.tgz", "walker@^1.0.8": "https://registry.npmmirror.com/walker/-/walker-1.0.8.tgz", "watchpack@^2.4.1": "https://registry.npmmirror.com/watchpack/-/watchpack-2.4.4.tgz", "wcwidth@^1.0.1": "https://registry.npmmirror.com/wcwidth/-/wcwidth-1.0.1.tgz", "webidl-conversions@^7.0.0": "https://registry.npmmirror.com/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "webpack-node-externals@3.0.0": "https://registry.npmmirror.com/webpack-node-externals/-/webpack-node-externals-3.0.0.tgz", "webpack-sources@^3.2.3": "https://registry.npmmirror.com/webpack-sources/-/webpack-sources-3.3.3.tgz", "webpack@5.99.6": "https://registry.npmmirror.com/webpack/-/webpack-5.99.6.tgz", "whatwg-url@^14.1.0 || ^13.0.0": "https://registry.npmmirror.com/whatwg-url/-/whatwg-url-14.2.0.tgz", "which@^2.0.1": "https://registry.npmmirror.com/which/-/which-2.0.2.tgz", "word-wrap@^1.2.5": "https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.5.tgz", "wrap-ansi-cjs@npm:wrap-ansi@^7.0.0": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^6.2.0": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "wrap-ansi@^7.0.0": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^8.1.0": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "wrappy@1": "https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz", "write-file-atomic@^4.0.2": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "xtend@^4.0.2": "https://registry.npmmirror.com/xtend/-/xtend-4.0.2.tgz", "y18n@^5.0.5": "https://registry.npmmirror.com/y18n/-/y18n-5.0.8.tgz", "yallist@^3.0.2": "https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz", "yargs-parser@21.1.1": "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-21.1.1.tgz", "yargs-parser@^21.1.1": "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-21.1.1.tgz", "yargs@^17.3.1": "https://registry.npmmirror.com/yargs/-/yargs-17.7.2.tgz", "yauzl@^3.1.2": "https://registry.npmmirror.com/yauzl/-/yauzl-3.2.0.tgz", "yn@3.1.1": "https://registry.npmmirror.com/yn/-/yn-3.1.1.tgz", "yocto-queue@^0.1.0": "https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz", "yoctocolors-cjs@^2.1.2": "https://registry.npmmirror.com/yoctocolors-cjs/-/yoctocolors-cjs-2.1.2.tgz"}, "files": [], "artifacts": {}}