import * as Jo<PERSON> from 'joi';
export interface DatabaseConfig {
    uri: string;
    name: string;
}
export interface ServerConfig {
    port: number;
    environment: string;
}
export interface JwtConfig {
    secret: string;
    expiresIn: string;
}
export interface WeixinConfig {
    appId: string;
    appSecret: string;
    mchId?: string;
    apiKey?: string;
    serialNo?: string;
    notifyUrl?: string;
    privateKeyPath?: string;
}
export interface AppConfig {
    database: DatabaseConfig;
    server: ServerConfig;
    jwt: JwtConfig;
    weixin: WeixinConfig;
    debug: boolean;
}
export declare const configValidationSchema: Joi.ObjectSchema<any>;
declare const _default: () => AppConfig;
export default _default;
