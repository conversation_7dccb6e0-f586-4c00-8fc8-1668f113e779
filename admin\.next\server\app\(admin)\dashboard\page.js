(()=>{var e={};e.id=284,e.ids=[284],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4401:(e,t,r)=>{Promise.resolve().then(r.bind(r,72631))},4691:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=r(7565).A},7565:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(43210),o=r(69662),s=r.n(o),i=r(71802),l=r(52604),a=r(76285),c=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function u(e){return"number"==typeof e?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}let d=["xs","sm","md","lg","xl","xxl"],p=n.forwardRef((e,t)=>{let{getPrefixCls:r,direction:o}=n.useContext(i.QO),{gutter:p,wrap:m}=n.useContext(l.A),{prefixCls:f,span:g,order:x,offset:h,push:v,pull:y,className:b,children:$,flex:j,style:A}=e,k=c(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),O=r("col",f),[C,w,S]=(0,a.xV)(O),E={},P={};d.forEach(t=>{let r={},n=e[t];"number"==typeof n?r.span=n:"object"==typeof n&&(r=n||{}),delete k[t],P=Object.assign(Object.assign({},P),{[`${O}-${t}-${r.span}`]:void 0!==r.span,[`${O}-${t}-order-${r.order}`]:r.order||0===r.order,[`${O}-${t}-offset-${r.offset}`]:r.offset||0===r.offset,[`${O}-${t}-push-${r.push}`]:r.push||0===r.push,[`${O}-${t}-pull-${r.pull}`]:r.pull||0===r.pull,[`${O}-rtl`]:"rtl"===o}),r.flex&&(P[`${O}-${t}-flex`]=!0,E[`--${O}-${t}-flex`]=u(r.flex))});let N=s()(O,{[`${O}-${g}`]:void 0!==g,[`${O}-order-${x}`]:x,[`${O}-offset-${h}`]:h,[`${O}-push-${v}`]:v,[`${O}-pull-${y}`]:y},b,P,w,S),I={};if(p&&p[0]>0){let e=p[0]/2;I.paddingLeft=e,I.paddingRight=e}return j&&(I.flex=u(j),!1!==m||I.minWidth||(I.minWidth=0)),C(n.createElement("div",Object.assign({},k,{style:Object.assign(Object.assign(Object.assign({},I),A),E),className:N,ref:t}),$))})},8662:(e,t,r)=>{"use strict";r.d(t,{A:()=>k});var n=r(43210),o=r(96201),s=r(53428),i=r(56883),l=r(69662),a=r.n(l),c=r(44666),u=r(71802),d=r(37510);let p=e=>{let t,{value:r,formatter:o,precision:s,decimalSeparator:i,groupSeparator:l="",prefixCls:a}=e;if("function"==typeof o)t=o(r);else{let e=String(r),o=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(o&&"-"!==e){let e=o[1],r=o[2]||"0",c=o[4]||"";r=r.replace(/\B(?=(\d{3})+(?!\d))/g,l),"number"==typeof s&&(c=c.padEnd(s,"0").slice(0,s>0?s:0)),c&&(c=`${i}${c}`),t=[n.createElement("span",{key:"int",className:`${a}-content-value-int`},e,r),c&&n.createElement("span",{key:"decimal",className:`${a}-content-value-decimal`},c)]}else t=e}return n.createElement("span",{className:`${a}-content-value`},t)};var m=r(32476),f=r(13581),g=r(60254);let x=e=>{let{componentCls:t,marginXXS:r,padding:n,colorTextDescription:o,titleFontSize:s,colorTextHeading:i,contentFontSize:l,fontFamily:a}=e;return{[t]:Object.assign(Object.assign({},(0,m.dF)(e)),{[`${t}-title`]:{marginBottom:r,color:o,fontSize:s},[`${t}-skeleton`]:{paddingTop:n},[`${t}-content`]:{color:i,fontSize:l,fontFamily:a,[`${t}-content-value`]:{display:"inline-block",direction:"ltr"},[`${t}-content-prefix, ${t}-content-suffix`]:{display:"inline-block"},[`${t}-content-prefix`]:{marginInlineEnd:r},[`${t}-content-suffix`]:{marginInlineStart:r}}})}},h=(0,f.OF)("Statistic",e=>[x((0,g.oX)(e,{}))],e=>{let{fontSizeHeading3:t,fontSize:r}=e;return{titleFontSize:r,contentFontSize:t}});var v=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let y=n.forwardRef((e,t)=>{let{prefixCls:r,className:o,rootClassName:s,style:i,valueStyle:l,value:m=0,title:f,valueRender:g,prefix:x,suffix:y,loading:b=!1,formatter:$,precision:j,decimalSeparator:A=".",groupSeparator:k=",",onMouseEnter:O,onMouseLeave:C}=e,w=v(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:S,direction:E,className:P,style:N}=(0,u.TP)("statistic"),I=S("statistic",r),[z,R,D]=h(I),M=n.createElement(p,{decimalSeparator:A,groupSeparator:k,prefixCls:I,formatter:$,precision:j,value:m}),L=a()(I,{[`${I}-rtl`]:"rtl"===E},P,o,s,R,D),q=n.useRef(null);n.useImperativeHandle(t,()=>({nativeElement:q.current}));let W=(0,c.A)(w,{aria:!0,data:!0});return z(n.createElement("div",Object.assign({},W,{ref:q,className:L,style:Object.assign(Object.assign({},N),i),onMouseEnter:O,onMouseLeave:C}),f&&n.createElement("div",{className:`${I}-title`},f),n.createElement(d.A,{paragraph:!1,loading:b,className:`${I}-skeleton`},n.createElement("div",{style:l,className:`${I}-content`},x&&n.createElement("span",{className:`${I}-content-prefix`},x),g?g(M):M,y&&n.createElement("span",{className:`${I}-content-suffix`},y)))))}),b=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var $=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let j=e=>{let{value:t,format:r="HH:mm:ss",onChange:l,onFinish:a,type:c}=e,u=$(e,["value","format","onChange","onFinish","type"]),d="countdown"===c,[p,m]=n.useState(null),f=(0,o._q)(()=>{let e=Date.now(),r=new Date(t).getTime();return m({}),null==l||l(d?r-e:e-r),!d||!(r<e)||(null==a||a(),!1)});return n.useEffect(()=>{let e,t=()=>{e=(0,s.A)(()=>{f()&&t()})};return t(),()=>s.A.cancel(e)},[t,d]),n.useEffect(()=>{m({})},[]),n.createElement(y,Object.assign({},u,{value:t,valueRender:e=>(0,i.Ob)(e,{title:void 0}),formatter:(e,t)=>p?function(e,t,r){let{format:n=""}=t,o=new Date(e).getTime(),s=Date.now();return function(e,t){let r=e,n=/\[[^\]]*]/g,o=(t.match(n)||[]).map(e=>e.slice(1,-1)),s=t.replace(n,"[]"),i=b.reduce((e,[t,n])=>{if(e.includes(t)){let o=Math.floor(r/n);return r-=o*n,e.replace(RegExp(`${t}+`,"g"),e=>{let t=e.length;return o.toString().padStart(t,"0")})}return e},s),l=0;return i.replace(n,()=>{let e=o[l];return l+=1,e})}(r?Math.max(o-s,0):Math.max(s-o,0),n)}(e,Object.assign(Object.assign({},t),{format:r}),d):"-"}))},A=n.memo(e=>n.createElement(j,Object.assign({},e,{type:"countdown"})));y.Timer=j,y.Countdown=A;let k=y},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11101:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(80828),o=r(43210);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.7 630.2 359 721.7 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.7 758.3 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM184 352V232h64v207.6a91.99 91.99 0 01-64-87.6zm520 128c0 49.1-19.1 95.4-53.9 130.1-34.8 34.8-81 53.9-130.1 53.9h-16c-49.1 0-95.4-19.1-130.1-53.9-34.8-34.8-53.9-81-53.9-130.1V184h384v296zm136-128c0 41-26.9 75.8-64 87.6V232h64v120z"}}]},name:"trophy",theme:"outlined"};var i=r(21898);let l=o.forwardRef(function(e,t){return o.createElement(i.A,(0,n.A)({},e,{ref:t,icon:s}))})},12412:e=>{"use strict";e.exports=require("assert")},14129:(e,t,r)=>{Promise.resolve().then(r.bind(r,84790))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20775:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var n=r(43210),o=r(69662),s=r.n(o),i=r(57266),l=r(71802),a=r(54908),c=r(52604),u=r(76285),d=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function p(e,t){let[r,o]=n.useState("string"==typeof e?e:""),s=()=>{if("string"==typeof e&&o(e),"object"==typeof e)for(let r=0;r<i.ye.length;r++){let n=i.ye[r];if(!t||!t[n])continue;let s=e[n];if(void 0!==s)return void o(s)}};return n.useEffect(()=>{s()},[JSON.stringify(e),t]),r}let m=n.forwardRef((e,t)=>{let{prefixCls:r,justify:o,align:m,className:f,style:g,children:x,gutter:h=0,wrap:v}=e,y=d(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:b,direction:$}=n.useContext(l.QO),j=(0,a.A)(!0,null),A=p(m,j),k=p(o,j),O=b("row",r),[C,w,S]=(0,u.L3)(O),E=function(e,t){let r=[void 0,void 0],n=Array.isArray(e)?e:[e,void 0],o=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return n.forEach((e,t)=>{if("object"==typeof e&&null!==e)for(let n=0;n<i.ye.length;n++){let s=i.ye[n];if(o[s]&&void 0!==e[s]){r[t]=e[s];break}}else r[t]=e}),r}(h,j),P=s()(O,{[`${O}-no-wrap`]:!1===v,[`${O}-${k}`]:k,[`${O}-${A}`]:A,[`${O}-rtl`]:"rtl"===$},f,w,S),N={},I=null!=E[0]&&E[0]>0?-(E[0]/2):void 0;I&&(N.marginLeft=I,N.marginRight=I);let[z,R]=E;N.rowGap=R;let D=n.useMemo(()=>({gutter:[z,R],wrap:v}),[z,R,v]);return C(n.createElement(c.A.Provider,{value:D},n.createElement("div",Object.assign({},y,{className:P,style:Object.assign(Object.assign({},N),g),ref:t}),x)))})},21820:e=>{"use strict";e.exports=require("os")},26099:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var n=r(65239),o=r(48088),s=r(88170),i=r.n(s),l=r(30893),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);r.d(t,a);let c={children:["",{children:["(admin)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84790)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\dashboard\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(admin)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52604:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(43210).createContext)({})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72631:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eO});var n=r(60687),o=r(43210),s=r(99053);r(35899);var i=r(96625),l=r(4691),a=r(11585),c=r(8662),u=r(73117),d=r(91039),p=r(69146),m=r(41514),f=r(15693),g=r(69662),x=r.n(g),h=r(11056),v=r(71802),y=r(80828),b=r(219),$=r(78135),j={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},A=function(){var e=(0,o.useRef)([]),t=(0,o.useRef)(null);return(0,o.useEffect)(function(){var r=Date.now(),n=!1;e.current.forEach(function(e){if(e){n=!0;var o=e.style;o.transitionDuration=".3s, .3s, .3s, .06s",t.current&&r-t.current<100&&(o.transitionDuration="0s, 0s")}}),n&&(t.current=Date.now())}),e.current},k=r(83192),O=r(82853),C=r(31829),w=0,S=(0,C.A)();let E=function(e){var t=o.useState(),r=(0,O.A)(t,2),n=r[0],s=r[1];return o.useEffect(function(){var e;s("rc_progress_".concat((S?(e=w,w+=1):e="TEST_OR_SSR",e)))},[]),e||n};var P=function(e){var t=e.bg,r=e.children;return o.createElement("div",{style:{width:"100%",height:"100%",background:t}},r)};function N(e,t){return Object.keys(e).map(function(r){var n=parseFloat(r),o="".concat(Math.floor(n*t),"%");return"".concat(e[r]," ").concat(o)})}var I=o.forwardRef(function(e,t){var r=e.prefixCls,n=e.color,s=e.gradientId,i=e.radius,l=e.style,a=e.ptg,c=e.strokeLinecap,u=e.strokeWidth,d=e.size,p=e.gapDegree,m=n&&"object"===(0,k.A)(n),f=d/2,g=o.createElement("circle",{className:"".concat(r,"-circle-path"),r:i,cx:f,cy:f,stroke:m?"#FFF":void 0,strokeLinecap:c,strokeWidth:u,opacity:+(0!==a),style:l,ref:t});if(!m)return g;var x="".concat(s,"-conic"),h=N(n,(360-p)/360),v=N(n,1),y="conic-gradient(from ".concat(p?"".concat(180+p/2,"deg"):"0deg",", ").concat(h.join(", "),")"),b="linear-gradient(to ".concat(p?"bottom":"top",", ").concat(v.join(", "),")");return o.createElement(o.Fragment,null,o.createElement("mask",{id:x},g),o.createElement("foreignObject",{x:0,y:0,width:d,height:d,mask:"url(#".concat(x,")")},o.createElement(P,{bg:b},o.createElement(P,{bg:y}))))}),z=function(e,t,r,n,o,s,i,l,a,c){var u=arguments.length>10&&void 0!==arguments[10]?arguments[10]:0,d=(100-n)/100*t;return"round"===a&&100!==n&&(d+=c/2)>=t&&(d=t-.01),{stroke:"string"==typeof l?l:void 0,strokeDasharray:"".concat(t,"px ").concat(e),strokeDashoffset:d+u,transform:"rotate(".concat(o+r/100*360*((360-s)/360)+(0===s?0:({bottom:0,top:180,left:90,right:-90})[i]),"deg)"),transformOrigin:"".concat(50,"px ").concat(50,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},R=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function D(e){var t=null!=e?e:[];return Array.isArray(t)?t:[t]}let M=function(e){var t,r,n,s,i=(0,b.A)((0,b.A)({},j),e),l=i.id,a=i.prefixCls,c=i.steps,u=i.strokeWidth,d=i.trailWidth,p=i.gapDegree,m=void 0===p?0:p,f=i.gapPosition,g=i.trailColor,h=i.strokeLinecap,v=i.style,O=i.className,C=i.strokeColor,w=i.percent,S=(0,$.A)(i,R),P=E(l),N="".concat(P,"-gradient"),M=50-u/2,L=2*Math.PI*M,q=m>0?90+m/2:-90,W=(360-m)/360*L,_="object"===(0,k.A)(c)?c:{count:c,gap:2},F=_.count,T=_.gap,B=D(w),X=D(C),G=X.find(function(e){return e&&"object"===(0,k.A)(e)}),H=G&&"object"===(0,k.A)(G)?"butt":h,V=z(L,W,0,100,q,m,f,g,H,u),U=A();return o.createElement("svg",(0,y.A)({className:x()("".concat(a,"-circle"),O),viewBox:"0 0 ".concat(100," ").concat(100),style:v,id:l,role:"presentation"},S),!F&&o.createElement("circle",{className:"".concat(a,"-circle-trail"),r:M,cx:50,cy:50,stroke:g,strokeLinecap:H,strokeWidth:d||u,style:V}),F?(t=Math.round(F*(B[0]/100)),r=100/F,n=0,Array(F).fill(null).map(function(e,s){var i=s<=t-1?X[0]:g,l=i&&"object"===(0,k.A)(i)?"url(#".concat(N,")"):void 0,c=z(L,W,n,r,q,m,f,i,"butt",u,T);return n+=(W-c.strokeDashoffset+T)*100/W,o.createElement("circle",{key:s,className:"".concat(a,"-circle-path"),r:M,cx:50,cy:50,stroke:l,strokeWidth:u,opacity:1,style:c,ref:function(e){U[s]=e}})})):(s=0,B.map(function(e,t){var r=X[t]||X[X.length-1],n=z(L,W,s,e,q,m,f,r,H,u);return s+=e,o.createElement(I,{key:t,color:r,ptg:e,radius:M,prefixCls:a,gradientId:N,style:n,strokeLinecap:H,strokeWidth:u,gapDegree:m,ref:function(e){U[t]=e},size:100})}).reverse()))};var L=r(33519),q=r(20619);function W(e){return!e||e<0?0:e>100?100:e}function _({success:e,successPercent:t}){let r=t;return e&&"progress"in e&&(r=e.progress),e&&"percent"in e&&(r=e.percent),r}let F=({percent:e,success:t,successPercent:r})=>{let n=W(_({success:t,successPercent:r}));return[n,W(W(e)-n)]},T=({success:e={},strokeColor:t})=>{let{strokeColor:r}=e;return[r||q.uy.green,t||null]},B=(e,t,r)=>{var n,o,s,i;let l=-1,a=-1;if("step"===t){let t=r.steps,n=r.strokeWidth;"string"==typeof e||void 0===e?(l="small"===e?2:14,a=null!=n?n:8):"number"==typeof e?[l,a]=[e,e]:[l=14,a=8]=Array.isArray(e)?e:[e.width,e.height],l*=t}else if("line"===t){let t=null==r?void 0:r.strokeWidth;"string"==typeof e||void 0===e?a=t||("small"===e?6:8):"number"==typeof e?[l,a]=[e,e]:[l=-1,a=8]=Array.isArray(e)?e:[e.width,e.height]}else("circle"===t||"dashboard"===t)&&("string"==typeof e||void 0===e?[l,a]="small"===e?[60,60]:[120,120]:"number"==typeof e?[l,a]=[e,e]:Array.isArray(e)&&(l=null!=(o=null!=(n=e[0])?n:e[1])?o:120,a=null!=(i=null!=(s=e[0])?s:e[1])?i:120));return[l,a]},X=e=>3/e*100,G=e=>{let{prefixCls:t,trailColor:r=null,strokeLinecap:n="round",gapPosition:s,gapDegree:i,width:l=120,type:a,children:c,success:u,size:d=l,steps:p}=e,[m,f]=B(d,"circle"),{strokeWidth:g}=e;void 0===g&&(g=Math.max(X(m),6));let h=o.useMemo(()=>i||0===i?i:"dashboard"===a?75:void 0,[i,a]),v=F(e),y="[object Object]"===Object.prototype.toString.call(e.strokeColor),b=T({success:u,strokeColor:e.strokeColor}),$=x()(`${t}-inner`,{[`${t}-circle-gradient`]:y}),j=o.createElement(M,{steps:p,percent:p?v[1]:v,strokeWidth:g,trailWidth:g,strokeColor:p?b[1]:b,strokeLinecap:n,trailColor:r,prefixCls:t,gapDegree:h,gapPosition:s||"dashboard"===a&&"bottom"||void 0}),A=m<=20,k=o.createElement("div",{className:$,style:{width:m,height:f,fontSize:.15*m+6}},j,!A&&c);return A?o.createElement(L.A,{title:c},k):k};var H=r(42411),V=r(32476),U=r(13581),Q=r(60254);let Y="--progress-line-stroke-color",J="--progress-percent",K=e=>{let t=e?"100%":"-100%";return new H.Mo(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},Z=e=>{let{componentCls:t,iconCls:r}=e;return{[t]:Object.assign(Object.assign({},(0,V.dF)(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${Y})`]},height:"100%",width:`calc(1 / var(${J}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[r]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${(0,H.zA)(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:K(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:K(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},ee=e=>{let{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[r]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},et=e=>{let{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},er=e=>{let{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${r}`]:{fontSize:e.fontSizeSM}}}},en=(0,U.OF)("Progress",e=>{let t=e.calc(e.marginXXS).div(2).equal(),r=(0,Q.oX)(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[Z(r),ee(r),et(r),er(r)]},e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:`${e.fontSize/e.fontSizeSM}em`}));var eo=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let es=e=>{let t=[];return Object.keys(e).forEach(r=>{let n=parseFloat(r.replace(/%/g,""));Number.isNaN(n)||t.push({key:n,value:e[r]})}),(t=t.sort((e,t)=>e.key-t.key)).map(({key:e,value:t})=>`${t} ${e}%`).join(", ")},ei=(e,t)=>{let{from:r=q.uy.blue,to:n=q.uy.blue,direction:o="rtl"===t?"to left":"to right"}=e,s=eo(e,["from","to","direction"]);if(0!==Object.keys(s).length){let e=es(s),t=`linear-gradient(${o}, ${e})`;return{background:t,[Y]:t}}let i=`linear-gradient(${o}, ${r}, ${n})`;return{background:i,[Y]:i}},el=e=>{let{prefixCls:t,direction:r,percent:n,size:s,strokeWidth:i,strokeColor:l,strokeLinecap:a="round",children:c,trailColor:u=null,percentPosition:d,success:p}=e,{align:m,type:f}=d,g=l&&"string"!=typeof l?ei(l,r):{[Y]:l,background:l},h="square"===a||"butt"===a?0:void 0,[v,y]=B(null!=s?s:[-1,i||("small"===s?6:8)],"line",{strokeWidth:i}),b=Object.assign(Object.assign({width:`${W(n)}%`,height:y,borderRadius:h},g),{[J]:W(n)/100}),$=_(e),j={width:`${W($)}%`,height:y,borderRadius:h,backgroundColor:null==p?void 0:p.strokeColor},A=o.createElement("div",{className:`${t}-inner`,style:{backgroundColor:u||void 0,borderRadius:h}},o.createElement("div",{className:x()(`${t}-bg`,`${t}-bg-${f}`),style:b},"inner"===f&&c),void 0!==$&&o.createElement("div",{className:`${t}-success-bg`,style:j})),k="outer"===f&&"start"===m,O="outer"===f&&"end"===m;return"outer"===f&&"center"===m?o.createElement("div",{className:`${t}-layout-bottom`},A,c):o.createElement("div",{className:`${t}-outer`,style:{width:v<0?"100%":v}},k&&c,A,O&&c)},ea=e=>{let{size:t,steps:r,rounding:n=Math.round,percent:s=0,strokeWidth:i=8,strokeColor:l,trailColor:a=null,prefixCls:c,children:u}=e,d=n(s/100*r),[p,m]=B(null!=t?t:["small"===t?2:14,i],"step",{steps:r,strokeWidth:i}),f=p/r,g=Array.from({length:r});for(let e=0;e<r;e++){let t=Array.isArray(l)?l[e]:l;g[e]=o.createElement("div",{key:e,className:x()(`${c}-steps-item`,{[`${c}-steps-item-active`]:e<=d-1}),style:{backgroundColor:e<=d-1?t:a,width:f,height:m}})}return o.createElement("div",{className:`${c}-steps-outer`},g,u)};var ec=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let eu=["normal","exception","active","success"],ed=o.forwardRef((e,t)=>{let r,{prefixCls:n,className:s,rootClassName:i,steps:l,strokeColor:a,percent:c=0,size:g="default",showInfo:y=!0,type:b="line",status:$,format:j,style:A,percentPosition:k={}}=e,O=ec(e,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:C="end",type:w="outer"}=k,S=Array.isArray(a)?a[0]:a,E="string"==typeof a||Array.isArray(a)?a:void 0,P=o.useMemo(()=>{if(S){let e="string"==typeof S?S:Object.values(S)[0];return new u.Y(e).isLight()}return!1},[a]),N=o.useMemo(()=>{var t,r;let n=_(e);return parseInt(void 0!==n?null==(t=null!=n?n:0)?void 0:t.toString():null==(r=null!=c?c:0)?void 0:r.toString(),10)},[c,e.success,e.successPercent]),I=o.useMemo(()=>!eu.includes($)&&N>=100?"success":$||"normal",[$,N]),{getPrefixCls:z,direction:R,progress:D}=o.useContext(v.QO),M=z("progress",n),[L,q,F]=en(M),T="line"===b,X=T&&!l,H=o.useMemo(()=>{let t;if(!y)return null;let r=_(e),n=j||(e=>`${e}%`),s=T&&P&&"inner"===w;return"inner"===w||j||"exception"!==I&&"success"!==I?t=n(W(c),W(r)):"exception"===I?t=T?o.createElement(m.A,null):o.createElement(f.A,null):"success"===I&&(t=T?o.createElement(d.A,null):o.createElement(p.A,null)),o.createElement("span",{className:x()(`${M}-text`,{[`${M}-text-bright`]:s,[`${M}-text-${C}`]:X,[`${M}-text-${w}`]:X}),title:"string"==typeof t?t:void 0},t)},[y,c,N,I,b,M,j]);"line"===b?r=l?o.createElement(ea,Object.assign({},e,{strokeColor:E,prefixCls:M,steps:"object"==typeof l?l.count:l}),H):o.createElement(el,Object.assign({},e,{strokeColor:S,prefixCls:M,direction:R,percentPosition:{align:C,type:w}}),H):("circle"===b||"dashboard"===b)&&(r=o.createElement(G,Object.assign({},e,{strokeColor:S,prefixCls:M,progressStatus:I}),H));let V=x()(M,`${M}-status-${I}`,{[`${M}-${"dashboard"===b&&"circle"||b}`]:"line"!==b,[`${M}-inline-circle`]:"circle"===b&&B(g,"circle")[0]<=20,[`${M}-line`]:X,[`${M}-line-align-${C}`]:X,[`${M}-line-position-${w}`]:X,[`${M}-steps`]:l,[`${M}-show-info`]:y,[`${M}-${g}`]:"string"==typeof g,[`${M}-rtl`]:"rtl"===R},null==D?void 0:D.className,s,i,q,F);return L(o.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},null==D?void 0:D.style),A),className:V,role:"progressbar","aria-valuenow":N,"aria-valuemin":0,"aria-valuemax":100},(0,h.A)(O,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),r))});var ep=r(21411),em=r(71103),ef=r(94858),eg=r(28859),ex=r(53788),eh=r(11101),ev=r(47453),ey=r(53082),eb=r(79505),e$=r(73237),ej=r(16189);r(23870);let{Title:eA,Paragraph:ek}=s.A;function eO(){let e=(0,ej.useRouter)(),[t,r]=(0,o.useState)(!0),[s,u]=(0,o.useState)({totalUsers:0,totalPhrases:0,totalLevels:0,maxLevels:1e3,remainingLevels:1e3,activeUsers:0,totalGames:0,totalCompletions:0,vipUsers:0,vipRate:0,totalOrders:0,successOrders:0,totalRevenue:0}),d=s.totalLevels/s.maxLevels*100,p=s.totalGames>0?s.totalCompletions/s.totalGames*100:0;return(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{style:{marginBottom:24},children:[(0,n.jsx)(eA,{level:2,children:"主控面板"}),(0,n.jsx)(ek,{children:"欢迎使用消消乐游戏管理后台！这里展示了系统的核心统计信息和快捷操作入口。"})]}),(0,n.jsxs)(i.A,{gutter:[16,16],style:{marginBottom:24},children:[(0,n.jsx)(l.A,{xs:24,sm:12,lg:6,children:(0,n.jsx)(a.A,{children:(0,n.jsx)(c.A,{title:"总用户数",value:s.totalUsers,prefix:(0,n.jsx)(em.A,{}),loading:t})})}),(0,n.jsx)(l.A,{xs:24,sm:12,lg:6,children:(0,n.jsx)(a.A,{children:(0,n.jsx)(c.A,{title:"VIP用户",value:s.vipUsers,prefix:(0,n.jsx)(ef.A,{}),suffix:`/ ${s.totalUsers}`,valueStyle:{color:"#faad14"},loading:t})})}),(0,n.jsx)(l.A,{xs:24,sm:12,lg:6,children:(0,n.jsx)(a.A,{children:(0,n.jsx)(c.A,{title:"词组总数",value:s.totalPhrases,prefix:(0,n.jsx)(eg.A,{}),loading:t})})}),(0,n.jsx)(l.A,{xs:24,sm:12,lg:6,children:(0,n.jsx)(a.A,{children:(0,n.jsx)(c.A,{title:"关卡总数",value:s.totalLevels,prefix:(0,n.jsx)(ex.A,{}),suffix:`/ ${s.maxLevels}`,loading:t})})})]}),(0,n.jsxs)(i.A,{gutter:[16,16],style:{marginBottom:24},children:[(0,n.jsx)(l.A,{xs:24,sm:12,lg:6,children:(0,n.jsx)(a.A,{children:(0,n.jsx)(c.A,{title:"VIP转化率",value:s.vipRate,precision:1,suffix:"%",valueStyle:{color:s.vipRate>=10?"#3f8600":"#cf1322"},loading:t})})}),(0,n.jsx)(l.A,{xs:24,sm:12,lg:6,children:(0,n.jsx)(a.A,{children:(0,n.jsx)(c.A,{title:"活跃用户",value:s.activeUsers,prefix:(0,n.jsx)(eh.A,{}),suffix:"(7天)",loading:t})})}),(0,n.jsx)(l.A,{xs:24,sm:12,lg:6,children:(0,n.jsx)(a.A,{children:(0,n.jsx)(c.A,{title:"总收入",value:s.totalRevenue,precision:2,prefix:"\xa5",valueStyle:{color:"#52c41a"},loading:t})})}),(0,n.jsx)(l.A,{xs:24,sm:12,lg:6,children:(0,n.jsx)(a.A,{children:(0,n.jsx)(c.A,{title:"成功订单",value:s.successOrders,prefix:(0,n.jsx)(ev.A,{}),suffix:`/ ${s.totalOrders}`,loading:t})})})]}),(0,n.jsxs)(i.A,{gutter:[16,16],style:{marginBottom:24},children:[(0,n.jsx)(l.A,{xs:24,lg:12,children:(0,n.jsxs)(a.A,{title:"关卡创建进度",loading:t,children:[(0,n.jsx)("div",{style:{marginBottom:16},children:(0,n.jsx)(ed,{percent:d,status:d>=90?"exception":"active",format:()=>`${s.totalLevels}/${s.maxLevels}`})}),(0,n.jsxs)(ek,{children:["已创建 ",s.totalLevels," 个关卡，还可以创建 ",s.remainingLevels," 个关卡"]})]})}),(0,n.jsx)(l.A,{xs:24,lg:12,children:(0,n.jsxs)(a.A,{title:"游戏统计",loading:t,children:[(0,n.jsxs)(i.A,{gutter:16,children:[(0,n.jsx)(l.A,{span:12,children:(0,n.jsx)(c.A,{title:"总游戏次数",value:s.totalGames,precision:0})}),(0,n.jsx)(l.A,{span:12,children:(0,n.jsx)(c.A,{title:"总通关次数",value:s.totalCompletions,precision:0})})]}),(0,n.jsx)("div",{style:{marginTop:16},children:(0,n.jsx)(c.A,{title:"整体通关率",value:p,precision:2,suffix:"%",valueStyle:{color:p>=50?"#3f8600":"#cf1322"}})})]})})]}),(0,n.jsx)(a.A,{title:"快捷操作",children:(0,n.jsxs)(i.A,{gutter:[16,16],children:[(0,n.jsx)(l.A,{xs:24,sm:12,md:8,lg:6,children:(0,n.jsx)(ep.Ay,{type:"primary",block:!0,icon:(0,n.jsx)(ey.A,{}),onClick:()=>e.push("/levels/create"),disabled:0===s.remainingLevels,children:"创建关卡"})}),(0,n.jsx)(l.A,{xs:24,sm:12,md:8,lg:6,children:(0,n.jsx)(ep.Ay,{block:!0,icon:(0,n.jsx)(ey.A,{}),onClick:()=>e.push("/phrases"),children:"管理词组"})}),(0,n.jsx)(l.A,{xs:24,sm:12,md:8,lg:6,children:(0,n.jsx)(ep.Ay,{block:!0,icon:(0,n.jsx)(eb.A,{}),onClick:()=>e.push("/users"),children:"查看用户"})}),(0,n.jsx)(l.A,{xs:24,sm:12,md:8,lg:6,children:(0,n.jsx)(ep.Ay,{block:!0,icon:(0,n.jsx)(ex.A,{}),onClick:()=>e.push("/levels"),children:"关卡列表"})}),(0,n.jsx)(l.A,{xs:24,sm:12,md:8,lg:6,children:(0,n.jsx)(ep.Ay,{block:!0,icon:(0,n.jsx)(e$.A,{}),onClick:()=>e.push("/vip-packages"),children:"VIP套餐管理"})}),(0,n.jsx)(l.A,{xs:24,sm:12,md:8,lg:6,children:(0,n.jsx)(ep.Ay,{block:!0,icon:(0,n.jsx)(ef.A,{}),onClick:()=>e.push("/vip-users"),children:"VIP用户管理"})}),(0,n.jsx)(l.A,{xs:24,sm:12,md:8,lg:6,children:(0,n.jsx)(ep.Ay,{block:!0,icon:(0,n.jsx)(ev.A,{}),onClick:()=>e.push("/payment-orders"),children:"支付订单"})})]})})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79505:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(80828),o=r(43210);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"eye",theme:"outlined"};var i=r(21898);let l=o.forwardRef(function(e,t){return o.createElement(i.A,(0,n.A)({},e,{ref:t,icon:s}))})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84790:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\dashboard\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96625:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=r(20775).A}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,433,658,816,204,451],()=>r(26099));module.exports=n})();