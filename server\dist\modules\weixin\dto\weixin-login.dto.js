"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinSessionCheckDto = exports.WeixinPhoneBindDto = exports.WeixinLoginResponseDto = exports.WeixinLoginDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class WeixinLoginDto {
    code;
    phone;
    nickname;
    avatarUrl;
}
exports.WeixinLoginDto = WeixinLoginDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信小程序登录凭证code', example: '081Kq4Ga1MSox41ufaGa1elzqd4Kq4Gn' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: 'code不能为空' }),
    __metadata("design:type", String)
], WeixinLoginDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户手机号（可选）', example: '13800138000', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WeixinLoginDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户昵称（可选）', example: '微信用户', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WeixinLoginDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户头像URL（可选）', example: 'https://thirdwx.qlogo.cn/mmopen/xxx', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WeixinLoginDto.prototype, "avatarUrl", void 0);
class WeixinLoginResponseDto {
    status;
    message;
    openid;
    sessionKey;
    userInfo;
    unionid;
}
exports.WeixinLoginResponseDto = WeixinLoginResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '登录状态', example: 'success' }),
    __metadata("design:type", String)
], WeixinLoginResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应消息', example: '登录成功' }),
    __metadata("design:type", String)
], WeixinLoginResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户openid', example: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE' }),
    __metadata("design:type", String)
], WeixinLoginResponseDto.prototype, "openid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '会话密钥（仅用于服务端，不返回给客户端）', example: 'session_key_xxx' }),
    __metadata("design:type", String)
], WeixinLoginResponseDto.prototype, "sessionKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户信息（登录成功时返回）', required: false }),
    __metadata("design:type", Object)
], WeixinLoginResponseDto.prototype, "userInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'unionid（如果小程序绑定了开放平台）', required: false }),
    __metadata("design:type", String)
], WeixinLoginResponseDto.prototype, "unionid", void 0);
class WeixinPhoneBindDto {
    openid;
    phone;
    nickname;
    avatarUrl;
}
exports.WeixinPhoneBindDto = WeixinPhoneBindDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信用户openid', example: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: 'openid不能为空' }),
    __metadata("design:type", String)
], WeixinPhoneBindDto.prototype, "openid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户手机号', example: '13800138000' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '手机号不能为空' }),
    __metadata("design:type", String)
], WeixinPhoneBindDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户昵称（可选）', example: '微信用户', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WeixinPhoneBindDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户头像URL（可选）', example: 'https://thirdwx.qlogo.cn/mmopen/xxx', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WeixinPhoneBindDto.prototype, "avatarUrl", void 0);
class WeixinSessionCheckDto {
    openid;
    sessionKey;
}
exports.WeixinSessionCheckDto = WeixinSessionCheckDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信用户openid', example: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: 'openid不能为空' }),
    __metadata("design:type", String)
], WeixinSessionCheckDto.prototype, "openid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '会话密钥', example: 'session_key_xxx' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: 'sessionKey不能为空' }),
    __metadata("design:type", String)
], WeixinSessionCheckDto.prototype, "sessionKey", void 0);
//# sourceMappingURL=weixin-login.dto.js.map