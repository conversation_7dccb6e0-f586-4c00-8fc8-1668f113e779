(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{10202:e=>{e.exports={container:"LoginPage_container__uSVwT"}},29799:(e,t,r)=>{"use strict";r.d(t,{F:()=>c});var s=r(23464),a=r(19868);let n={BASE_URL:"http://127.0.0.1:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}},o=s.A.create({baseURL:n.BASE_URL,timeout:n.TIMEOUT,headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),o.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){let{status:t,data:r}=e.response;switch(t){case 401:a.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:a.Ay.error("没有权限访问该资源");break;case 404:a.Ay.error("请求的资源不存在");break;case 500:a.Ay.error("服务器内部错误");break;default:a.Ay.error((null==r?void 0:r.message)||"请求失败")}}else e.request?a.Ay.error("网络连接失败，请检查网络"):a.Ay.error("请求配置错误");return Promise.reject(e)});let c={get:(e,t)=>o.get(e,t),post:(e,t,r)=>o.post(e,t,r),put:(e,t,r)=>o.put(e,t,r),patch:(e,t,r)=>o.patch(e,t,r),delete:(e,t)=>o.delete(e,t)}},35695:(e,t,r)=>{"use strict";var s=r(18999);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useServerInsertedHTML")&&r.d(t,{useServerInsertedHTML:function(){return s.useServerInsertedHTML}})},42993:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var s=r(95155),a=r(12115),n=r(19868),o=r(505),c=r(97605),i=r(44670),l=r(56020),u=r(30662),d=r(50274),m=r(79630);let h={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"};var g=r(62764),p=a.forwardRef(function(e,t){return a.createElement(g.A,(0,m.A)({},e,{ref:t,icon:h}))}),A=r(35695),v=r(59959),f=r(10202),y=r.n(f);let _=()=>{let e=(0,A.useRouter)(),[t,r]=(0,a.useState)(!1),m=async t=>{r(!0);try{let r=await v.y.login(t);r.accessToken?(n.Ay.success("登录成功！"),v.y.setToken(r.accessToken),e.push("/dashboard")):n.Ay.error(r.message||"登录失败，请检查用户名或密码！")}catch(e){console.error("登录失败:",e)}finally{r(!1)}};return(0,s.jsx)("div",{className:y().container,children:(0,s.jsx)(o.A,{title:(0,s.jsx)(c.A.Title,{level:3,style:{textAlign:"center",marginBottom:0},children:"后台登录"}),style:{width:400},children:(0,s.jsxs)(i.A,{name:"admin_login",initialValues:{remember:!0},onFinish:m,children:[(0,s.jsx)(i.A.Item,{name:"username",rules:[{required:!0,message:"请输入用户名!"}],children:(0,s.jsx)(l.A,{prefix:(0,s.jsx)(d.A,{}),placeholder:"用户名 (例如: admin)"})}),(0,s.jsx)(i.A.Item,{name:"password",rules:[{required:!0,message:"请输入密码!"}],children:(0,s.jsx)(l.A.Password,{prefix:(0,s.jsx)(p,{}),placeholder:"密码 (例如: password123)"})}),(0,s.jsx)(i.A.Item,{children:(0,s.jsx)(u.Ay,{type:"primary",htmlType:"submit",style:{width:"100%"},loading:t,children:"登录"})})]})})})}},50274:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(79630),a=r(12115);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var o=r(62764);let c=a.forwardRef(function(e,t){return a.createElement(o.A,(0,s.A)({},e,{ref:t,icon:n}))})},59959:(e,t,r)=>{"use strict";r.d(t,{y:()=>a});var s=r(29799);let a={login:async e=>(await s.F.post("/api/v1/auth/login",e)).data,logout:()=>{localStorage.removeItem("admin_token"),window.location.href="/login"},getToken:()=>localStorage.getItem("admin_token"),setToken:e=>{localStorage.setItem("admin_token",e)},isLoggedIn:()=>!!localStorage.getItem("admin_token")};a.login},82038:(e,t,r)=>{Promise.resolve().then(r.bind(r,42993))}},e=>{var t=t=>e(e.s=t);e.O(0,[7301,8340,547,7469,7497,44,7605,4670,8441,1684,7358],()=>t(82038)),_N_E=e.O()}]);