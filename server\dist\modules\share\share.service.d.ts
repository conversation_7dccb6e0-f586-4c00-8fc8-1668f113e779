import { Model } from 'mongoose';
import { CreateShareConfigDto } from './dto/create-share-config.dto';
import { UpdateShareConfigDto } from './dto/update-share-config.dto';
import { ShareConfigResponseDto } from './dto/share-config-response.dto';
import { ShareConfigDocument } from './entities/share-config.entity';
export declare class ShareService {
    private shareConfigModel;
    private readonly logger;
    constructor(shareConfigModel: Model<ShareConfigDocument>);
    private generateUniqueShareConfigId;
    create(createShareConfigDto: CreateShareConfigDto): Promise<ShareConfigResponseDto>;
    findAll(): Promise<ShareConfigResponseDto[]>;
    findActive(): Promise<ShareConfigResponseDto[]>;
    findOne(id: string): Promise<ShareConfigResponseDto>;
    findByType(type: string): Promise<ShareConfigResponseDto | null>;
    getDefaultConfig(): Promise<ShareConfigResponseDto | null>;
    update(id: string, updateShareConfigDto: UpdateShareConfigDto): Promise<ShareConfigResponseDto>;
    remove(id: string): Promise<void>;
    toggleActive(id: string): Promise<ShareConfigResponseDto>;
    private getShareConfigEntity;
    private mapToResponseDto;
    initializeDefaultConfig(): Promise<void>;
}
