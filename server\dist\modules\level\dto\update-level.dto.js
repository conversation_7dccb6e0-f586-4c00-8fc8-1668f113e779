"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateLevelDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_level_dto_1 = require("./create-level.dto");
const class_validator_1 = require("class-validator");
class UpdateLevelDto extends (0, swagger_1.PartialType)(create_level_dto_1.CreateLevelDto) {
    thesaurusIds;
    phraseIds;
}
exports.UpdateLevelDto = UpdateLevelDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新关联的词库ID列表', type: [String], example: ['uuid3-thesaurus-zzz'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('all', { each: true, message: '每个词库ID都必须是有效的UUID格式' }),
    __metadata("design:type", Array)
], UpdateLevelDto.prototype, "thesaurusIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新直接选择的词组ID列表', type: [String], example: ['uuid1-phrase-xxx', 'uuid2-phrase-yyy'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('all', { each: true, message: '每个词组ID都必须是有效的UUID格式' }),
    __metadata("design:type", Array)
], UpdateLevelDto.prototype, "phraseIds", void 0);
//# sourceMappingURL=update-level.dto.js.map