import { Document } from 'mongoose';
export type PhraseDocument = Phrase & Document;
export declare class Phrase {
    id: string;
    text: string;
    meaning: string;
    exampleSentence?: string;
    tags?: string[];
    createdAt: Date;
    updatedAt: Date;
}
export declare const PhraseSchema: import("mongoose").Schema<Phrase, import("mongoose").Model<Phrase, any, any, any, Document<unknown, any, Phrase, any> & Phrase & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Phrase, Document<unknown, {}, import("mongoose").FlatRecord<Phrase>, {}> & import("mongoose").FlatRecord<Phrase> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
export type PhraseEntity = Phrase;
