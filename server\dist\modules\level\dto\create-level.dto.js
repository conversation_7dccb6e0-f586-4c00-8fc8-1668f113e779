"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateLevelDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateLevelDto {
    name;
    difficulty;
    description;
    thesaurusIds;
    phraseIds;
}
exports.CreateLevelDto = CreateLevelDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '关卡名称不能为空' }),
    __metadata("design:type", String)
], CreateLevelDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsNumber)({}, { message: '难度必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '难度最小为1' }),
    (0, class_validator_1.Max)(5, { message: '难度最大为5' }),
    (0, class_validator_1.IsNotEmpty)({ message: '关卡难度不能为空' }),
    __metadata("design:type", Number)
], CreateLevelDto.prototype, "difficulty", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateLevelDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关联的词库ID列表', type: [String], example: ['uuid1-thesaurus-xxx', 'uuid2-thesaurus-yyy'], required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('all', { each: true, message: '每个词库ID都必须是有效的UUID格式' }),
    __metadata("design:type", Array)
], CreateLevelDto.prototype, "thesaurusIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '直接选择的词组ID列表', type: [String], example: ['uuid1-phrase-xxx', 'uuid2-phrase-yyy'], required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('all', { each: true, message: '每个词组ID都必须是有效的UUID格式' }),
    __metadata("design:type", Array)
], CreateLevelDto.prototype, "phraseIds", void 0);
//# sourceMappingURL=create-level.dto.js.map