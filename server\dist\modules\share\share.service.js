"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ShareService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShareService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const share_config_entity_1 = require("./entities/share-config.entity");
const date_formatter_1 = require("../../common/utils/date-formatter");
let ShareService = ShareService_1 = class ShareService {
    shareConfigModel;
    logger = new common_1.Logger(ShareService_1.name);
    constructor(shareConfigModel) {
        this.shareConfigModel = shareConfigModel;
    }
    async generateUniqueShareConfigId() {
        let id;
        let attempts = 0;
        const maxAttempts = 10;
        do {
            attempts++;
            const timestamp = Date.now().toString().slice(-6);
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            id = `share-config-${timestamp}-${random}`;
            const existingConfig = await this.shareConfigModel.findOne({ id }).exec();
            if (!existingConfig) {
                break;
            }
            if (attempts >= maxAttempts) {
                throw new common_1.BadRequestException('无法生成唯一的分享配置ID，请稍后重试');
            }
        } while (true);
        return id;
    }
    async create(createShareConfigDto) {
        const type = createShareConfigDto.type || 'default';
        if (type === 'default') {
            const existingDefault = await this.shareConfigModel.findOne({ type: 'default', isActive: true }).exec();
            if (existingDefault) {
                throw new common_1.BadRequestException('默认分享配置已存在，请先禁用现有配置');
            }
        }
        const newShareConfig = new this.shareConfigModel({
            id: await this.generateUniqueShareConfigId(),
            name: createShareConfigDto.name,
            title: createShareConfigDto.title,
            path: createShareConfigDto.path,
            imageUrl: createShareConfigDto.imageUrl,
            description: createShareConfigDto.description,
            type: type,
            isActive: createShareConfigDto.isActive ?? true,
            sortOrder: createShareConfigDto.sortOrder ?? 1,
        });
        const savedConfig = await newShareConfig.save();
        this.logger.log(`✅ 创建分享配置成功: ${savedConfig.id} - ${savedConfig.name}`);
        return this.mapToResponseDto(savedConfig);
    }
    async findAll() {
        const configs = await this.shareConfigModel
            .find()
            .sort({ sortOrder: 1, createdAt: -1 })
            .exec();
        return configs.map(config => this.mapToResponseDto(config));
    }
    async findActive() {
        const configs = await this.shareConfigModel
            .find({ isActive: true })
            .sort({ sortOrder: 1, createdAt: -1 })
            .exec();
        return configs.map(config => this.mapToResponseDto(config));
    }
    async findOne(id) {
        const config = await this.getShareConfigEntity(id);
        return this.mapToResponseDto(config);
    }
    async findByType(type) {
        const config = await this.shareConfigModel
            .findOne({ type, isActive: true })
            .exec();
        return config ? this.mapToResponseDto(config) : null;
    }
    async getDefaultConfig() {
        return this.findByType('default');
    }
    async update(id, updateShareConfigDto) {
        const existingConfig = await this.getShareConfigEntity(id);
        if (updateShareConfigDto.type === 'default' && existingConfig.type !== 'default') {
            const existingDefault = await this.shareConfigModel.findOne({
                type: 'default',
                isActive: true,
                id: { $ne: id }
            }).exec();
            if (existingDefault) {
                throw new common_1.BadRequestException('默认分享配置已存在，请先禁用现有配置');
            }
        }
        Object.assign(existingConfig, updateShareConfigDto);
        const updatedConfig = await existingConfig.save();
        this.logger.log(`📝 更新分享配置: ${updatedConfig.id} - ${updatedConfig.name}`);
        return this.mapToResponseDto(updatedConfig);
    }
    async remove(id) {
        const config = await this.getShareConfigEntity(id);
        if (config.type === 'default') {
            throw new common_1.BadRequestException('不能删除默认分享配置，请先禁用');
        }
        await this.shareConfigModel.findOneAndDelete({ id }).exec();
        this.logger.log(`🗑️ 删除分享配置: ${id} - ${config.name}`);
    }
    async toggleActive(id) {
        const config = await this.getShareConfigEntity(id);
        config.isActive = !config.isActive;
        const updatedConfig = await config.save();
        this.logger.log(`🔄 切换分享配置状态: ${updatedConfig.id} - ${updatedConfig.isActive ? '启用' : '禁用'}`);
        return this.mapToResponseDto(updatedConfig);
    }
    async getShareConfigEntity(id) {
        const config = await this.shareConfigModel.findOne({ id }).exec();
        if (!config) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的分享配置`);
        }
        return config;
    }
    mapToResponseDto(config) {
        return {
            id: config.id,
            name: config.name,
            title: config.title,
            path: config.path,
            imageUrl: config.imageUrl,
            description: config.description,
            type: config.type,
            isActive: config.isActive,
            sortOrder: config.sortOrder,
            createdAt: (0, date_formatter_1.formatDate)(config.createdAt),
            updatedAt: (0, date_formatter_1.formatDate)(config.updatedAt),
        };
    }
    async initializeDefaultConfig() {
        const existingDefault = await this.shareConfigModel.findOne({ type: 'default' }).exec();
        if (!existingDefault) {
            const defaultConfig = new this.shareConfigModel({
                id: await this.generateUniqueShareConfigId(),
                name: '默认分享配置',
                title: '一起来挑战词汇游戏！',
                path: '/pages/index/index',
                description: '挑战你的词汇量，看看你能通过多少关！',
                type: 'default',
                isActive: true,
                sortOrder: 1,
            });
            await defaultConfig.save();
            this.logger.log('🎯 初始化默认分享配置完成');
        }
    }
};
exports.ShareService = ShareService;
exports.ShareService = ShareService = ShareService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(share_config_entity_1.ShareConfig.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], ShareService);
//# sourceMappingURL=share.service.js.map