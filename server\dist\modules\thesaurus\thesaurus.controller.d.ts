import { ThesaurusService } from './thesaurus.service';
import { CreateThesaurusDto } from './dto/create-thesaurus.dto';
import { UpdateThesaurusDto } from './dto/update-thesaurus.dto';
import { ThesaurusResponseDto } from './dto/thesaurus-response.dto';
import { AddPhraseToThesaurusDto } from './dto/add-phrase-to-thesaurus.dto';
export declare class ThesaurusController {
    private readonly thesaurusService;
    constructor(thesaurusService: ThesaurusService);
    create(createThesaurusDto: CreateThesaurusDto): Promise<ThesaurusResponseDto>;
    findAll(): Promise<ThesaurusResponseDto[]>;
    findOne(id: string): Promise<ThesaurusResponseDto>;
    update(id: string, updateThesaurusDto: UpdateThesaurusDto): Promise<ThesaurusResponseDto>;
    remove(id: string): Promise<void>;
    addPhraseToThesaurus(thesaurusId: string, addPhraseDto: AddPhraseToThesaurusDto): Promise<ThesaurusResponseDto>;
    removePhraseFromThesaurus(thesaurusId: string, phraseId: string): Promise<ThesaurusResponseDto>;
}
