"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const settings_service_1 = require("./settings.service");
const update_settings_dto_1 = require("./dto/update-settings.dto");
const settings_response_dto_1 = require("./dto/settings-response.dto");
let SettingsController = class SettingsController {
    settingsService;
    constructor(settingsService) {
        this.settingsService = settingsService;
    }
    findAll() {
        return this.settingsService.findAll();
    }
    findOne(id) {
        return this.settingsService.findOne(id);
    }
    findByKey(key) {
        return this.settingsService.findByKey(key);
    }
    update(id, updateSettingsDto) {
        return this.settingsService.update(id, updateSettingsDto);
    }
    updateByKey(key, body) {
        return this.settingsService.updateByKey(key, body.value);
    }
    initializeDefaults() {
        return this.settingsService.initializeDefaultSettings();
    }
};
exports.SettingsController = SettingsController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有设置列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取设置列表', type: [settings_response_dto_1.SettingsResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], SettingsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取设置详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '设置ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取设置详情', type: settings_response_dto_1.SettingsResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '设置不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], SettingsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('key/:key'),
    (0, swagger_1.ApiOperation)({ summary: '根据键名获取设置' }),
    (0, swagger_1.ApiParam)({ name: 'key', description: '设置键名' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取设置', type: settings_response_dto_1.SettingsResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '设置不存在' }),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], SettingsController.prototype, "findByKey", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新设置' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '设置ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '设置更新成功', type: settings_response_dto_1.SettingsResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '设置不存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_settings_dto_1.UpdateSettingsDto]),
    __metadata("design:returntype", void 0)
], SettingsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)('key/:key'),
    (0, swagger_1.ApiOperation)({ summary: '根据键名更新设置值' }),
    (0, swagger_1.ApiParam)({ name: 'key', description: '设置键名' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '设置更新成功', type: settings_response_dto_1.SettingsResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '设置不存在' }),
    __param(0, (0, common_1.Param)('key')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], SettingsController.prototype, "updateByKey", null);
__decorate([
    (0, common_1.Post)('initialize'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '初始化默认设置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '默认设置初始化成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], SettingsController.prototype, "initializeDefaults", null);
exports.SettingsController = SettingsController = __decorate([
    (0, swagger_1.ApiTags)('settings'),
    (0, common_1.Controller)('settings'),
    __metadata("design:paramtypes", [settings_service_1.SettingsService])
], SettingsController);
//# sourceMappingURL=settings.controller.js.map