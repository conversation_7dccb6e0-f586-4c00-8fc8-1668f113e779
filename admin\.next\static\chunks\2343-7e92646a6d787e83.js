"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2343],{9800:(e,t,n)=>{n.d(t,{M:()=>o});let o=n(12115).createContext({siderHook:{addSider:()=>null,removeSider:()=>null}})},13066:(e,t,n)=>{n.d(t,{P:()=>S,A:()=>O});var o=n(12115),c=n(79630);let a={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"};var r=n(62764),l=o.forwardRef(function(e,t){return o.createElement(r.A,(0,c.A)({},e,{ref:t,icon:a}))}),i=n(56480),d=n(46752),s=n(29300),u=n.n(s),m=n(17980),p=n(76592),g=n(15982),b=n(9800),v=n(85573),h=n(69793),f=n(45431);let I=e=>{let{componentCls:t,siderBg:n,motionDurationMid:o,motionDurationSlow:c,antCls:a,triggerHeight:r,triggerColor:l,triggerBg:i,headerHeight:d,zeroTriggerWidth:s,zeroTriggerHeight:u,borderRadiusLG:m,lightSiderBg:p,lightTriggerColor:g,lightTriggerBg:b,bodyBg:h}=e;return{[t]:{position:"relative",minWidth:0,background:n,transition:"all ".concat(o,", background 0s"),"&-has-trigger":{paddingBottom:r},"&-right":{order:1},["".concat(t,"-children")]:{height:"100%",marginTop:-.1,paddingTop:.1,["".concat(a,"-menu").concat(a,"-menu-inline-collapsed")]:{width:"auto"}},["&-zero-width ".concat(t,"-children")]:{overflow:"hidden"},["".concat(t,"-trigger")]:{position:"fixed",bottom:0,zIndex:1,height:r,color:l,lineHeight:(0,v.zA)(r),textAlign:"center",background:i,cursor:"pointer",transition:"all ".concat(o)},["".concat(t,"-zero-width-trigger")]:{position:"absolute",top:d,insetInlineEnd:e.calc(s).mul(-1).equal(),zIndex:1,width:s,height:u,color:l,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:n,borderRadius:"0 ".concat((0,v.zA)(m)," ").concat((0,v.zA)(m)," 0"),cursor:"pointer",transition:"background ".concat(c," ease"),"&::after":{position:"absolute",inset:0,background:"transparent",transition:"all ".concat(c),content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(s).mul(-1).equal(),borderRadius:"".concat((0,v.zA)(m)," 0 0 ").concat((0,v.zA)(m))}},"&-light":{background:p,["".concat(t,"-trigger")]:{color:g,background:b},["".concat(t,"-zero-width-trigger")]:{color:g,background:b,border:"1px solid ".concat(h),borderInlineStart:0}}}}},x=(0,f.OF)(["Layout","Sider"],e=>[I(e)],h.cH,{deprecatedTokens:h.lB});var y=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,o=Object.getOwnPropertySymbols(e);c<o.length;c++)0>t.indexOf(o[c])&&Object.prototype.propertyIsEnumerable.call(e,o[c])&&(n[o[c]]=e[o[c]]);return n};let w={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},C=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),S=o.createContext({}),B=(()=>{let e=0;return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e+=1,"".concat(t).concat(e)}})(),O=o.forwardRef((e,t)=>{let{prefixCls:n,className:c,trigger:a,children:r,defaultCollapsed:s=!1,theme:v="dark",style:h={},collapsible:f=!1,reverseArrow:I=!1,width:O=200,collapsedWidth:A=80,zeroWidthTriggerStyle:z,breakpoint:k,onCollapse:j,onBreakpoint:E}=e,H=y(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:T}=(0,o.useContext)(b.M),[N,R]=(0,o.useState)("collapsed"in e?e.collapsed:s),[P,M]=(0,o.useState)(!1);(0,o.useEffect)(()=>{"collapsed"in e&&R(e.collapsed)},[e.collapsed]);let D=(t,n)=>{"collapsed"in e||R(t),null==j||j(t,n)},{getPrefixCls:L,direction:W}=(0,o.useContext)(g.QO),X=L("layout-sider",n),[q,_,Y]=x(X),F=(0,o.useRef)(null);F.current=e=>{M(e.matches),null==E||E(e.matches),N!==e.matches&&D(e.matches,"responsive")},(0,o.useEffect)(()=>{let e;function t(e){var t;return null==(t=F.current)?void 0:t.call(F,e)}return void 0!==(null==window?void 0:window.matchMedia)&&k&&k in w&&(e=window.matchMedia("screen and (max-width: ".concat(w[k],")")),(0,p.e)(e,t),t(e)),()=>{(0,p.p)(e,t)}},[k]),(0,o.useEffect)(()=>{let e=B("ant-sider-");return T.addSider(e),()=>T.removeSider(e)},[]);let G=()=>{D(!N,"clickTrigger")},V=(0,m.A)(H,["collapsed"]),K=N?A:O,Q=C(K)?"".concat(K,"px"):String(K),U=0===parseFloat(String(A||0))?o.createElement("span",{onClick:G,className:u()("".concat(X,"-zero-width-trigger"),"".concat(X,"-zero-width-trigger-").concat(I?"right":"left")),style:z},a||o.createElement(l,null)):null,J="rtl"===W==!I,Z={expanded:J?o.createElement(d.A,null):o.createElement(i.A,null),collapsed:J?o.createElement(i.A,null):o.createElement(d.A,null)}[N?"collapsed":"expanded"],$=null!==a?U||o.createElement("div",{className:"".concat(X,"-trigger"),onClick:G,style:{width:Q}},a||Z):null,ee=Object.assign(Object.assign({},h),{flex:"0 0 ".concat(Q),maxWidth:Q,minWidth:Q,width:Q}),et=u()(X,"".concat(X,"-").concat(v),{["".concat(X,"-collapsed")]:!!N,["".concat(X,"-has-trigger")]:f&&null!==a&&!U,["".concat(X,"-below")]:!!P,["".concat(X,"-zero-width")]:0===parseFloat(Q)},c,_,Y),en=o.useMemo(()=>({siderCollapsed:N}),[N]);return q(o.createElement(S.Provider,{value:en},o.createElement("aside",Object.assign({className:et},V,{style:ee,ref:t}),o.createElement("div",{className:"".concat(X,"-children")},r),f||P&&U?$:null)))})},32653:(e,t,n)=>{n.d(t,{A:()=>i,h:()=>d});var o=n(12115),c=n(74686),a=n(9184),r=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,o=Object.getOwnPropertySymbols(e);c<o.length;c++)0>t.indexOf(o[c])&&Object.prototype.propertyIsEnumerable.call(e,o[c])&&(n[o[c]]=e[o[c]]);return n};let l=o.createContext(null),i=o.forwardRef((e,t)=>{let{children:n}=e,i=r(e,["children"]),d=o.useContext(l),s=o.useMemo(()=>Object.assign(Object.assign({},d),i),[d,i.prefixCls,i.mode,i.selectable,i.rootClassName]),u=(0,c.H3)(n),m=(0,c.xK)(t,u?(0,c.A9)(n):null);return o.createElement(l.Provider,{value:s},o.createElement(a.A,{space:!0},u?o.cloneElement(n,{ref:m}):n))}),d=l},46752:(e,t,n)=>{n.d(t,{A:()=>l});var o=n(79630),c=n(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"};var r=n(62764);let l=c.forwardRef(function(e,t){return c.createElement(r.A,(0,o.A)({},e,{ref:t,icon:a}))})},56480:(e,t,n)=>{n.d(t,{A:()=>l});var o=n(79630),c=n(12115);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};var r=n(62764);let l=c.forwardRef(function(e,t){return c.createElement(r.A,(0,o.A)({},e,{ref:t,icon:a}))})},69793:(e,t,n)=>{n.d(t,{Ay:()=>i,cH:()=>r,lB:()=>l});var o=n(85573),c=n(45431);let a=e=>{let{antCls:t,componentCls:n,colorText:c,footerBg:a,headerHeight:r,headerPadding:l,headerColor:i,footerPadding:d,fontSize:s,bodyBg:u,headerBg:m}=e;return{[n]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:u,"&, *":{boxSizing:"border-box"},["&".concat(n,"-has-sider")]:{flexDirection:"row",["> ".concat(n,", > ").concat(n,"-content")]:{width:0}},["".concat(n,"-header, &").concat(n,"-footer")]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},["".concat(n,"-header")]:{height:r,padding:l,color:i,lineHeight:(0,o.zA)(r),background:m,["".concat(t,"-menu")]:{lineHeight:"inherit"}},["".concat(n,"-footer")]:{padding:d,color:c,fontSize:s,background:a},["".concat(n,"-content")]:{flex:"auto",color:c,minHeight:0}}},r=e=>{let{colorBgLayout:t,controlHeight:n,controlHeightLG:o,colorText:c,controlHeightSM:a,marginXXS:r,colorTextLightSolid:l,colorBgContainer:i}=e,d=1.25*o;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:2*n,headerPadding:"0 ".concat(d,"px"),headerColor:c,footerPadding:"".concat(a,"px ").concat(d,"px"),footerBg:t,siderBg:"#001529",triggerHeight:o+2*r,triggerBg:"#002140",triggerColor:l,zeroTriggerWidth:o,zeroTriggerHeight:o,lightSiderBg:i,lightTriggerBg:i,lightTriggerColor:c}},l=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],i=(0,c.OF)("Layout",e=>[a(e)],r,{deprecatedTokens:l})},82343:(e,t,n)=>{n.d(t,{A:()=>Y});var o=n(12115),c=n(56480),a=n(46752),r=n(29300),l=n.n(r),i=n(10177),d=n(18885),s=n(48804),u=n(17980),m=n(9130);let p=e=>"object"!=typeof e&&"function"!=typeof e||null===e;var g=n(52824),b=n(31776),v=n(80163),h=n(26791),f=n(6833),I=n(15982),x=n(68151),y=n(83803),w=n(32653),C=n(85954),S=n(85573),B=n(18184),O=n(53272),A=n(52770),z=n(47212),k=n(35464),j=n(45902),E=n(45431),H=n(61388);let T=e=>{let{componentCls:t,menuCls:n,colorError:o,colorTextLightSolid:c}=e,a="".concat(n,"-item");return{["".concat(t,", ").concat(t,"-menu-submenu")]:{["".concat(n," ").concat(a)]:{["&".concat(a,"-danger:not(").concat(a,"-disabled)")]:{color:o,"&:hover":{color:c,backgroundColor:o}}}}}},N=e=>{let{componentCls:t,menuCls:n,zIndexPopup:o,dropdownArrowDistance:c,sizePopupArrow:a,antCls:r,iconCls:l,motionDurationMid:i,paddingBlock:d,fontSize:s,dropdownEdgeChildPadding:u,colorTextDisabled:m,fontSizeIcon:p,controlPaddingHorizontal:g,colorBgElevated:b}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:o,display:"block","&::before":{position:"absolute",insetBlock:e.calc(a).div(2).sub(c).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},["&-trigger".concat(r,"-btn")]:{["& > ".concat(l,"-down, & > ").concat(r,"-btn-icon > ").concat(l,"-down")]:{fontSize:p}},["".concat(t,"-wrap")]:{position:"relative",["".concat(r,"-btn > ").concat(l,"-down")]:{fontSize:p},["".concat(l,"-down::before")]:{transition:"transform ".concat(i)}},["".concat(t,"-wrap-open")]:{["".concat(l,"-down::before")]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},["&".concat(r,"-slide-down-enter").concat(r,"-slide-down-enter-active").concat(t,"-placement-bottomLeft,\n          &").concat(r,"-slide-down-appear").concat(r,"-slide-down-appear-active").concat(t,"-placement-bottomLeft,\n          &").concat(r,"-slide-down-enter").concat(r,"-slide-down-enter-active").concat(t,"-placement-bottom,\n          &").concat(r,"-slide-down-appear").concat(r,"-slide-down-appear-active").concat(t,"-placement-bottom,\n          &").concat(r,"-slide-down-enter").concat(r,"-slide-down-enter-active").concat(t,"-placement-bottomRight,\n          &").concat(r,"-slide-down-appear").concat(r,"-slide-down-appear-active").concat(t,"-placement-bottomRight")]:{animationName:O.ox},["&".concat(r,"-slide-up-enter").concat(r,"-slide-up-enter-active").concat(t,"-placement-topLeft,\n          &").concat(r,"-slide-up-appear").concat(r,"-slide-up-appear-active").concat(t,"-placement-topLeft,\n          &").concat(r,"-slide-up-enter").concat(r,"-slide-up-enter-active").concat(t,"-placement-top,\n          &").concat(r,"-slide-up-appear").concat(r,"-slide-up-appear-active").concat(t,"-placement-top,\n          &").concat(r,"-slide-up-enter").concat(r,"-slide-up-enter-active").concat(t,"-placement-topRight,\n          &").concat(r,"-slide-up-appear").concat(r,"-slide-up-appear-active").concat(t,"-placement-topRight")]:{animationName:O.nP},["&".concat(r,"-slide-down-leave").concat(r,"-slide-down-leave-active").concat(t,"-placement-bottomLeft,\n          &").concat(r,"-slide-down-leave").concat(r,"-slide-down-leave-active").concat(t,"-placement-bottom,\n          &").concat(r,"-slide-down-leave").concat(r,"-slide-down-leave-active").concat(t,"-placement-bottomRight")]:{animationName:O.vR},["&".concat(r,"-slide-up-leave").concat(r,"-slide-up-leave-active").concat(t,"-placement-topLeft,\n          &").concat(r,"-slide-up-leave").concat(r,"-slide-up-leave-active").concat(t,"-placement-top,\n          &").concat(r,"-slide-up-leave").concat(r,"-slide-up-leave-active").concat(t,"-placement-topRight")]:{animationName:O.YU}}},(0,k.Ay)(e,b,{arrowPlacement:{top:!0,bottom:!0}}),{["".concat(t," ").concat(n)]:{position:"relative",margin:0},["".concat(n,"-submenu-popup")]:{position:"absolute",zIndex:o,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},["".concat(t,", ").concat(t,"-menu-submenu")]:Object.assign(Object.assign({},(0,B.dF)(e)),{[n]:Object.assign(Object.assign({padding:u,listStyleType:"none",backgroundColor:b,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},(0,B.K8)(e)),{"&:empty":{padding:0,boxShadow:"none"},["".concat(n,"-item-group-title")]:{padding:"".concat((0,S.zA)(d)," ").concat((0,S.zA)(g)),color:e.colorTextDescription,transition:"all ".concat(i)},["".concat(n,"-item")]:{position:"relative",display:"flex",alignItems:"center"},["".concat(n,"-item-icon")]:{minWidth:s,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},["".concat(n,"-title-content")]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:"all ".concat(i),"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},["".concat(n,"-item-extra")]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},["".concat(n,"-item, ").concat(n,"-submenu-title")]:Object.assign(Object.assign({display:"flex",margin:0,padding:"".concat((0,S.zA)(d)," ").concat((0,S.zA)(g)),color:e.colorText,fontWeight:"normal",fontSize:s,lineHeight:e.lineHeight,cursor:"pointer",transition:"all ".concat(i),borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},(0,B.K8)(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:m,cursor:"not-allowed","&:hover":{color:m,backgroundColor:b,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:"".concat((0,S.zA)(e.marginXXS)," 0"),overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},["".concat(t,"-menu-submenu-expand-icon")]:{position:"absolute",insetInlineEnd:e.paddingXS,["".concat(t,"-menu-submenu-arrow-icon")]:{marginInlineEnd:"0 !important",color:e.colorIcon,fontSize:p,fontStyle:"normal"}}}),["".concat(n,"-item-group-list")]:{margin:"0 ".concat((0,S.zA)(e.marginXS)),padding:0,listStyle:"none"},["".concat(n,"-submenu-title")]:{paddingInlineEnd:e.calc(g).add(e.fontSizeSM).equal()},["".concat(n,"-submenu-vertical")]:{position:"relative"},["".concat(n,"-submenu").concat(n,"-submenu-disabled ").concat(t,"-menu-submenu-title")]:{["&, ".concat(t,"-menu-submenu-arrow-icon")]:{color:m,backgroundColor:b,cursor:"not-allowed"}},["".concat(n,"-submenu-selected ").concat(t,"-menu-submenu-title")]:{color:e.colorPrimary}})})},[(0,O._j)(e,"slide-up"),(0,O._j)(e,"slide-down"),(0,A.Mh)(e,"move-up"),(0,A.Mh)(e,"move-down"),(0,z.aB)(e,"zoom-big")]]},R=(0,E.OF)("Dropdown",e=>{let{marginXXS:t,sizePopupArrow:n,paddingXXS:o,componentCls:c}=e,a=(0,H.oX)(e,{menuCls:"".concat(c,"-menu"),dropdownArrowDistance:e.calc(n).div(2).add(t).equal(),dropdownEdgeChildPadding:o});return[N(a),T(a)]},e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},(0,k.Ke)({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),(0,j.n)(e)),{resetStyle:!1}),P=e=>{var t;let{menu:n,arrow:r,prefixCls:b,children:S,trigger:B,disabled:O,dropdownRender:A,popupRender:z,getPopupContainer:k,overlayClassName:j,rootClassName:E,overlayStyle:H,open:T,onOpenChange:N,visible:P,onVisibleChange:M,mouseEnterDelay:D=.15,mouseLeaveDelay:L=.1,autoAdjustOverflow:W=!0,placement:X="",overlay:q,transitionName:_,destroyOnHidden:Y,destroyPopupOnHide:F}=e,{getPopupContainer:G,getPrefixCls:V,direction:K,dropdown:Q}=o.useContext(I.QO),U=z||A;(0,h.rJ)("Dropdown");let J=o.useMemo(()=>{let e=V();return void 0!==_?_:X.includes("top")?"".concat(e,"-slide-down"):"".concat(e,"-slide-up")},[V,X,_]),Z=o.useMemo(()=>X?X.includes("Center")?X.slice(0,X.indexOf("Center")):X:"rtl"===K?"bottomRight":"bottomLeft",[X,K]),$=V("dropdown",b),ee=(0,x.A)($),[et,en,eo]=R($,ee),[,ec]=(0,C.Ay)(),ea=o.Children.only(p(S)?o.createElement("span",null,S):S),er=(0,v.Ob)(ea,{className:l()("".concat($,"-trigger"),{["".concat($,"-rtl")]:"rtl"===K},ea.props.className),disabled:null!=(t=ea.props.disabled)?t:O}),el=O?[]:B,ei=!!(null==el?void 0:el.includes("contextMenu")),[ed,es]=(0,s.A)(!1,{value:null!=T?T:P}),eu=(0,d.A)(e=>{null==N||N(e,{source:"trigger"}),null==M||M(e),es(e)}),em=l()(j,E,en,eo,ee,null==Q?void 0:Q.className,{["".concat($,"-rtl")]:"rtl"===K}),ep=(0,g.A)({arrowPointAtCenter:"object"==typeof r&&r.pointAtCenter,autoAdjustOverflow:W,offset:ec.marginXXS,arrowWidth:r?ec.sizePopupArrow:0,borderRadius:ec.borderRadius}),eg=o.useCallback(()=>{null!=n&&n.selectable&&null!=n&&n.multiple||(null==N||N(!1,{source:"menu"}),es(!1))},[null==n?void 0:n.selectable,null==n?void 0:n.multiple]),[eb,ev]=(0,m.YK)("Dropdown",null==H?void 0:H.zIndex),eh=o.createElement(i.A,Object.assign({alignPoint:ei},(0,u.A)(e,["rootClassName"]),{mouseEnterDelay:D,mouseLeaveDelay:L,visible:ed,builtinPlacements:ep,arrow:!!r,overlayClassName:em,prefixCls:$,getPopupContainer:k||G,transitionName:J,trigger:el,overlay:()=>{let e;return e=(null==n?void 0:n.items)?o.createElement(y.A,Object.assign({},n)):"function"==typeof q?q():q,U&&(e=U(e)),e=o.Children.only("string"==typeof e?o.createElement("span",null,e):e),o.createElement(w.A,{prefixCls:"".concat($,"-menu"),rootClassName:l()(eo,ee),expandIcon:o.createElement("span",{className:"".concat($,"-menu-submenu-arrow")},"rtl"===K?o.createElement(c.A,{className:"".concat($,"-menu-submenu-arrow-icon")}):o.createElement(a.A,{className:"".concat($,"-menu-submenu-arrow-icon")})),mode:"vertical",selectable:!1,onClick:eg,validator:e=>{let{mode:t}=e}},e)},placement:Z,onVisibleChange:eu,overlayStyle:Object.assign(Object.assign(Object.assign({},null==Q?void 0:Q.style),H),{zIndex:eb}),autoDestroy:null!=Y?Y:F}),er);return eb&&(eh=o.createElement(f.A.Provider,{value:ev},eh)),et(eh)},M=(0,b.A)(P,"align",void 0,"dropdown",e=>e);P._InternalPanelDoNotUseOrYouWillBeFired=e=>o.createElement(M,Object.assign({},e),o.createElement("span",null));var D=n(83607),L=n(30662),W=n(12320),X=n(18574),q=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,o=Object.getOwnPropertySymbols(e);c<o.length;c++)0>t.indexOf(o[c])&&Object.prototype.propertyIsEnumerable.call(e,o[c])&&(n[o[c]]=e[o[c]]);return n};let _=e=>{let{getPopupContainer:t,getPrefixCls:n,direction:c}=o.useContext(I.QO),{prefixCls:a,type:r="default",danger:i,disabled:d,loading:s,onClick:u,htmlType:m,children:p,className:g,menu:b,arrow:v,autoFocus:h,overlay:f,trigger:x,align:y,open:w,onOpenChange:C,placement:S,getPopupContainer:B,href:O,icon:A=o.createElement(D.A,null),title:z,buttonsRender:k=e=>e,mouseEnterDelay:j,mouseLeaveDelay:E,overlayClassName:H,overlayStyle:T,destroyOnHidden:N,destroyPopupOnHide:R,dropdownRender:M,popupRender:_}=e,Y=q(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyOnHidden","destroyPopupOnHide","dropdownRender","popupRender"]),F=n("dropdown",a),G={menu:b,arrow:v,autoFocus:h,align:y,disabled:d,trigger:d?[]:x,onOpenChange:C,getPopupContainer:B||t,mouseEnterDelay:j,mouseLeaveDelay:E,overlayClassName:H,overlayStyle:T,destroyOnHidden:N,popupRender:_||M},{compactSize:V,compactItemClassnames:K}=(0,X.RQ)(F,c),Q=l()("".concat(F,"-button"),K,g);"destroyPopupOnHide"in e&&(G.destroyPopupOnHide=R),"overlay"in e&&(G.overlay=f),"open"in e&&(G.open=w),"placement"in e?G.placement=S:G.placement="rtl"===c?"bottomLeft":"bottomRight";let[U,J]=k([o.createElement(L.Ay,{type:r,danger:i,disabled:d,loading:s,onClick:u,htmlType:m,href:O,title:z},p),o.createElement(L.Ay,{type:r,danger:i,icon:A})]);return o.createElement(W.A.Compact,Object.assign({className:Q,size:V,block:!0},Y),U,o.createElement(P,Object.assign({},G),J))};_.__ANT_BUTTON=!0,P.Button=_;let Y=P},83803:(e,t,n)=>{n.d(t,{A:()=>V});var o=n(12115),c=n(91187),a=n(13066),r=n(83607),l=n(29300),i=n.n(l),d=n(18885),s=n(17980),u=n(93666),m=n(80163),p=n(15982),g=n(68151);let b=(0,o.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var v=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,o=Object.getOwnPropertySymbols(e);c<o.length;c++)0>t.indexOf(o[c])&&Object.prototype.propertyIsEnumerable.call(e,o[c])&&(n[o[c]]=e[o[c]]);return n};let h=e=>{let{prefixCls:t,className:n,dashed:a}=e,r=v(e,["prefixCls","className","dashed"]),{getPrefixCls:l}=o.useContext(p.QO),d=l("menu",t),s=i()({["".concat(d,"-item-divider-dashed")]:!!a},n);return o.createElement(c.cG,Object.assign({className:s},r))};var f=n(63715),I=n(26922);let x=e=>{var t;let{className:n,children:r,icon:l,title:d,danger:u,extra:p}=e,{prefixCls:g,firstLevel:v,direction:h,disableMenuItemTitleTooltip:x,inlineCollapsed:y}=o.useContext(b),{siderCollapsed:w}=o.useContext(a.P),C=d;void 0===d?C=v?r:"":!1===d&&(C="");let S={title:C};w||y||(S.title=null,S.open=!1);let B=(0,f.A)(r).length,O=o.createElement(c.q7,Object.assign({},(0,s.A)(e,["title","icon","danger"]),{className:i()({["".concat(g,"-item-danger")]:u,["".concat(g,"-item-only-child")]:(l?B+1:B)===1},n),title:"string"==typeof d?d:void 0}),(0,m.Ob)(l,{className:i()(o.isValidElement(l)?null==(t=l.props)?void 0:t.className:void 0,"".concat(g,"-item-icon"))}),(e=>{let t=null==r?void 0:r[0],n=o.createElement("span",{className:i()("".concat(g,"-title-content"),{["".concat(g,"-title-content-with-extra")]:!!p||0===p})},r);return(!l||o.isValidElement(r)&&"span"===r.type)&&r&&e&&v&&"string"==typeof t?o.createElement("div",{className:"".concat(g,"-inline-collapsed-noicon")},t.charAt(0)):n})(y));return x||(O=o.createElement(I.A,Object.assign({},S,{placement:"rtl"===h?"left":"right",classNames:{root:"".concat(g,"-inline-collapsed-tooltip")}}),O)),O};var y=n(32653),w=n(85573),C=n(34162),S=n(18184),B=n(35376),O=n(53272),A=n(47212),z=n(45431),k=n(61388);let j=e=>{let{componentCls:t,motionDurationSlow:n,horizontalLineHeight:o,colorSplit:c,lineWidth:a,lineType:r,itemPaddingInline:l}=e;return{["".concat(t,"-horizontal")]:{lineHeight:o,border:0,borderBottom:"".concat((0,w.zA)(a)," ").concat(r," ").concat(c),boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},["".concat(t,"-item, ").concat(t,"-submenu")]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:l},["> ".concat(t,"-item:hover,\n        > ").concat(t,"-item-active,\n        > ").concat(t,"-submenu ").concat(t,"-submenu-title:hover")]:{backgroundColor:"transparent"},["".concat(t,"-item, ").concat(t,"-submenu-title")]:{transition:["border-color ".concat(n),"background ".concat(n)].join(",")},["".concat(t,"-submenu-arrow")]:{display:"none"}}}},E=e=>{let{componentCls:t,menuArrowOffset:n,calc:o}=e;return{["".concat(t,"-rtl")]:{direction:"rtl"},["".concat(t,"-submenu-rtl")]:{transformOrigin:"100% 0"},["".concat(t,"-rtl").concat(t,"-vertical,\n    ").concat(t,"-submenu-rtl ").concat(t,"-vertical")]:{["".concat(t,"-submenu-arrow")]:{"&::before":{transform:"rotate(-45deg) translateY(".concat((0,w.zA)(o(n).mul(-1).equal()),")")},"&::after":{transform:"rotate(45deg) translateY(".concat((0,w.zA)(n),")")}}}}},H=e=>Object.assign({},(0,S.jk)(e)),T=(e,t)=>{let{componentCls:n,itemColor:o,itemSelectedColor:c,subMenuItemSelectedColor:a,groupTitleColor:r,itemBg:l,subMenuItemBg:i,itemSelectedBg:d,activeBarHeight:s,activeBarWidth:u,activeBarBorderWidth:m,motionDurationSlow:p,motionEaseInOut:g,motionEaseOut:b,itemPaddingInline:v,motionDurationMid:h,itemHoverColor:f,lineType:I,colorSplit:x,itemDisabledColor:y,dangerItemColor:C,dangerItemHoverColor:S,dangerItemSelectedColor:B,dangerItemActiveBg:O,dangerItemSelectedBg:A,popupBg:z,itemHoverBg:k,itemActiveBg:j,menuSubMenuBg:E,horizontalItemSelectedColor:T,horizontalItemSelectedBg:N,horizontalItemBorderRadius:R,horizontalItemHoverBg:P}=e;return{["".concat(n,"-").concat(t,", ").concat(n,"-").concat(t," > ").concat(n)]:{color:o,background:l,["&".concat(n,"-root:focus-visible")]:Object.assign({},H(e)),["".concat(n,"-item")]:{"&-group-title, &-extra":{color:r}},["".concat(n,"-submenu-selected > ").concat(n,"-submenu-title")]:{color:a},["".concat(n,"-item, ").concat(n,"-submenu-title")]:{color:o,["&:not(".concat(n,"-item-disabled):focus-visible")]:Object.assign({},H(e))},["".concat(n,"-item-disabled, ").concat(n,"-submenu-disabled")]:{color:"".concat(y," !important")},["".concat(n,"-item:not(").concat(n,"-item-selected):not(").concat(n,"-submenu-selected)")]:{["&:hover, > ".concat(n,"-submenu-title:hover")]:{color:f}},["&:not(".concat(n,"-horizontal)")]:{["".concat(n,"-item:not(").concat(n,"-item-selected)")]:{"&:hover":{backgroundColor:k},"&:active":{backgroundColor:j}},["".concat(n,"-submenu-title")]:{"&:hover":{backgroundColor:k},"&:active":{backgroundColor:j}}},["".concat(n,"-item-danger")]:{color:C,["&".concat(n,"-item:hover")]:{["&:not(".concat(n,"-item-selected):not(").concat(n,"-submenu-selected)")]:{color:S}},["&".concat(n,"-item:active")]:{background:O}},["".concat(n,"-item a")]:{"&, &:hover":{color:"inherit"}},["".concat(n,"-item-selected")]:{color:c,["&".concat(n,"-item-danger")]:{color:B},"a, a:hover":{color:"inherit"}},["& ".concat(n,"-item-selected")]:{backgroundColor:d,["&".concat(n,"-item-danger")]:{backgroundColor:A}},["&".concat(n,"-submenu > ").concat(n)]:{backgroundColor:E},["&".concat(n,"-popup > ").concat(n)]:{backgroundColor:z},["&".concat(n,"-submenu-popup > ").concat(n)]:{backgroundColor:z},["&".concat(n,"-horizontal")]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{["> ".concat(n,"-item, > ").concat(n,"-submenu")]:{top:m,marginTop:e.calc(m).mul(-1).equal(),marginBottom:0,borderRadius:R,"&::after":{position:"absolute",insetInline:v,bottom:0,borderBottom:"".concat((0,w.zA)(s)," solid transparent"),transition:"border-color ".concat(p," ").concat(g),content:'""'},"&:hover, &-active, &-open":{background:P,"&::after":{borderBottomWidth:s,borderBottomColor:T}},"&-selected":{color:T,backgroundColor:N,"&:hover":{backgroundColor:N},"&::after":{borderBottomWidth:s,borderBottomColor:T}}}}),["&".concat(n,"-root")]:{["&".concat(n,"-inline, &").concat(n,"-vertical")]:{borderInlineEnd:"".concat((0,w.zA)(m)," ").concat(I," ").concat(x)}},["&".concat(n,"-inline")]:{["".concat(n,"-sub").concat(n,"-inline")]:{background:i},["".concat(n,"-item")]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:"".concat((0,w.zA)(u)," solid ").concat(c),transform:"scaleY(0.0001)",opacity:0,transition:["transform ".concat(h," ").concat(b),"opacity ".concat(h," ").concat(b)].join(","),content:'""'},["&".concat(n,"-item-danger")]:{"&::after":{borderInlineEndColor:B}}},["".concat(n,"-selected, ").concat(n,"-item-selected")]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:["transform ".concat(h," ").concat(g),"opacity ".concat(h," ").concat(g)].join(",")}}}}}},N=e=>{let{componentCls:t,itemHeight:n,itemMarginInline:o,padding:c,menuArrowSize:a,marginXS:r,itemMarginBlock:l,itemWidth:i,itemPaddingInline:d}=e,s=e.calc(a).add(c).add(r).equal();return{["".concat(t,"-item")]:{position:"relative",overflow:"hidden"},["".concat(t,"-item, ").concat(t,"-submenu-title")]:{height:n,lineHeight:(0,w.zA)(n),paddingInline:d,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:l,width:i},["> ".concat(t,"-item,\n            > ").concat(t,"-submenu > ").concat(t,"-submenu-title")]:{height:n,lineHeight:(0,w.zA)(n)},["".concat(t,"-item-group-list ").concat(t,"-submenu-title,\n            ").concat(t,"-submenu-title")]:{paddingInlineEnd:s}}},R=e=>{let{componentCls:t,iconCls:n,itemHeight:o,colorTextLightSolid:c,dropdownWidth:a,controlHeightLG:r,motionEaseOut:l,paddingXL:i,itemMarginInline:d,fontSizeLG:s,motionDurationFast:u,motionDurationSlow:m,paddingXS:p,boxShadowSecondary:g,collapsedWidth:b,collapsedIconSize:v}=e,h={height:o,lineHeight:(0,w.zA)(o),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({["&".concat(t,"-root")]:{boxShadow:"none"}},N(e))},["".concat(t,"-submenu-popup")]:{["".concat(t,"-vertical")]:Object.assign(Object.assign({},N(e)),{boxShadow:g})}},{["".concat(t,"-submenu-popup ").concat(t,"-vertical").concat(t,"-sub")]:{minWidth:a,maxHeight:"calc(100vh - ".concat((0,w.zA)(e.calc(r).mul(2.5).equal()),")"),padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{["".concat(t,"-inline")]:{width:"100%",["&".concat(t,"-root")]:{["".concat(t,"-item, ").concat(t,"-submenu-title")]:{display:"flex",alignItems:"center",transition:["border-color ".concat(m),"background ".concat(m),"padding ".concat(u," ").concat(l)].join(","),["> ".concat(t,"-title-content")]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},["".concat(t,"-sub").concat(t,"-inline")]:{padding:0,border:0,borderRadius:0,boxShadow:"none",["& > ".concat(t,"-submenu > ").concat(t,"-submenu-title")]:h,["& ".concat(t,"-item-group-title")]:{paddingInlineStart:i}},["".concat(t,"-item")]:h}},{["".concat(t,"-inline-collapsed")]:{width:b,["&".concat(t,"-root")]:{["".concat(t,"-item, ").concat(t,"-submenu ").concat(t,"-submenu-title")]:{["> ".concat(t,"-inline-collapsed-noicon")]:{fontSize:s,textAlign:"center"}}},["> ".concat(t,"-item,\n          > ").concat(t,"-item-group > ").concat(t,"-item-group-list > ").concat(t,"-item,\n          > ").concat(t,"-item-group > ").concat(t,"-item-group-list > ").concat(t,"-submenu > ").concat(t,"-submenu-title,\n          > ").concat(t,"-submenu > ").concat(t,"-submenu-title")]:{insetInlineStart:0,paddingInline:"calc(50% - ".concat((0,w.zA)(e.calc(v).div(2).equal())," - ").concat((0,w.zA)(d),")"),textOverflow:"clip",["\n            ".concat(t,"-submenu-arrow,\n            ").concat(t,"-submenu-expand-icon\n          ")]:{opacity:0},["".concat(t,"-item-icon, ").concat(n)]:{margin:0,fontSize:v,lineHeight:(0,w.zA)(o),"+ span":{display:"inline-block",opacity:0}}},["".concat(t,"-item-icon, ").concat(n)]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",["".concat(t,"-item-icon, ").concat(n)]:{display:"none"},"a, a:hover":{color:c}},["".concat(t,"-item-group-title")]:Object.assign(Object.assign({},S.L9),{paddingInline:p})}}]},P=e=>{let{componentCls:t,motionDurationSlow:n,motionDurationMid:o,motionEaseInOut:c,motionEaseOut:a,iconCls:r,iconSize:l,iconMarginInlineEnd:i}=e;return{["".concat(t,"-item, ").concat(t,"-submenu-title")]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:["border-color ".concat(n),"background ".concat(n),"padding calc(".concat(n," + 0.1s) ").concat(c)].join(","),["".concat(t,"-item-icon, ").concat(r)]:{minWidth:l,fontSize:l,transition:["font-size ".concat(o," ").concat(a),"margin ".concat(n," ").concat(c),"color ".concat(n)].join(","),"+ span":{marginInlineStart:i,opacity:1,transition:["opacity ".concat(n," ").concat(c),"margin ".concat(n),"color ".concat(n)].join(",")}},["".concat(t,"-item-icon")]:Object.assign({},(0,S.Nk)()),["&".concat(t,"-item-only-child")]:{["> ".concat(r,", > ").concat(t,"-item-icon")]:{marginInlineEnd:0}}},["".concat(t,"-item-disabled, ").concat(t,"-submenu-disabled")]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},["> ".concat(t,"-submenu-title")]:{color:"inherit !important",cursor:"not-allowed"}}}},M=e=>{let{componentCls:t,motionDurationSlow:n,motionEaseInOut:o,borderRadius:c,menuArrowSize:a,menuArrowOffset:r}=e;return{["".concat(t,"-submenu")]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:a,color:"currentcolor",transform:"translateY(-50%)",transition:"transform ".concat(n," ").concat(o,", opacity ").concat(n)},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(a).mul(.6).equal(),height:e.calc(a).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:c,transition:["background ".concat(n," ").concat(o),"transform ".concat(n," ").concat(o),"top ".concat(n," ").concat(o),"color ".concat(n," ").concat(o)].join(","),content:'""'},"&::before":{transform:"rotate(45deg) translateY(".concat((0,w.zA)(e.calc(r).mul(-1).equal()),")")},"&::after":{transform:"rotate(-45deg) translateY(".concat((0,w.zA)(r),")")}}}}},D=e=>{let{antCls:t,componentCls:n,fontSize:o,motionDurationSlow:c,motionDurationMid:a,motionEaseInOut:r,paddingXS:l,padding:i,colorSplit:d,lineWidth:s,zIndexPopup:u,borderRadiusLG:m,subMenuItemBorderRadius:p,menuArrowSize:g,menuArrowOffset:b,lineType:v,groupTitleLineHeight:h,groupTitleFontSize:f}=e;return[{"":{[n]:Object.assign(Object.assign({},(0,S.t6)()),{"&-hidden":{display:"none"}})},["".concat(n,"-submenu-hidden")]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,S.dF)(e)),(0,S.t6)()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:"width ".concat(c," cubic-bezier(0.2, 0, 0, 1) 0s"),"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",["".concat(n,"-item")]:{flex:"none"}},["".concat(n,"-item, ").concat(n,"-submenu, ").concat(n,"-submenu-title")]:{borderRadius:e.itemBorderRadius},["".concat(n,"-item-group-title")]:{padding:"".concat((0,w.zA)(l)," ").concat((0,w.zA)(i)),fontSize:f,lineHeight:h,transition:"all ".concat(c)},["&-horizontal ".concat(n,"-submenu")]:{transition:["border-color ".concat(c," ").concat(r),"background ".concat(c," ").concat(r)].join(",")},["".concat(n,"-submenu, ").concat(n,"-submenu-inline")]:{transition:["border-color ".concat(c," ").concat(r),"background ".concat(c," ").concat(r),"padding ".concat(a," ").concat(r)].join(",")},["".concat(n,"-submenu ").concat(n,"-sub")]:{cursor:"initial",transition:["background ".concat(c," ").concat(r),"padding ".concat(c," ").concat(r)].join(",")},["".concat(n,"-title-content")]:{transition:"color ".concat(c),"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},["> ".concat(t,"-typography-ellipsis-single-line")]:{display:"inline",verticalAlign:"unset"},["".concat(n,"-item-extra")]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},["".concat(n,"-item a")]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},["".concat(n,"-item-divider")]:{overflow:"hidden",lineHeight:0,borderColor:d,borderStyle:v,borderWidth:0,borderTopWidth:s,marginBlock:s,padding:0,"&-dashed":{borderStyle:"dashed"}}}),P(e)),{["".concat(n,"-item-group")]:{["".concat(n,"-item-group-list")]:{margin:0,padding:0,["".concat(n,"-item, ").concat(n,"-submenu-title")]:{paddingInline:"".concat((0,w.zA)(e.calc(o).mul(2).equal())," ").concat((0,w.zA)(i))}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:u,borderRadius:m,boxShadow:"none",transformOrigin:"0 0",["&".concat(n,"-submenu")]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},["> ".concat(n)]:Object.assign(Object.assign(Object.assign({borderRadius:m},P(e)),M(e)),{["".concat(n,"-item, ").concat(n,"-submenu > ").concat(n,"-submenu-title")]:{borderRadius:p},["".concat(n,"-submenu-title::after")]:{transition:"transform ".concat(c," ").concat(r)}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),M(e)),{["&-inline-collapsed ".concat(n,"-submenu-arrow,\n        &-inline ").concat(n,"-submenu-arrow")]:{"&::before":{transform:"rotate(-45deg) translateX(".concat((0,w.zA)(b),")")},"&::after":{transform:"rotate(45deg) translateX(".concat((0,w.zA)(e.calc(b).mul(-1).equal()),")")}},["".concat(n,"-submenu-open").concat(n,"-submenu-inline > ").concat(n,"-submenu-title > ").concat(n,"-submenu-arrow")]:{transform:"translateY(".concat((0,w.zA)(e.calc(g).mul(.2).mul(-1).equal()),")"),"&::after":{transform:"rotate(-45deg) translateX(".concat((0,w.zA)(e.calc(b).mul(-1).equal()),")")},"&::before":{transform:"rotate(45deg) translateX(".concat((0,w.zA)(b),")")}}})},{["".concat(t,"-layout-header")]:{[n]:{lineHeight:"inherit"}}}]},L=e=>{var t,n,o;let{colorPrimary:c,colorError:a,colorTextDisabled:r,colorErrorBg:l,colorText:i,colorTextDescription:d,colorBgContainer:s,colorFillAlter:u,colorFillContent:m,lineWidth:p,lineWidthBold:g,controlItemBgActive:b,colorBgTextHover:v,controlHeightLG:h,lineHeight:f,colorBgElevated:I,marginXXS:x,padding:y,fontSize:w,controlHeightSM:S,fontSizeLG:B,colorTextLightSolid:O,colorErrorHover:A}=e,z=null!=(t=e.activeBarWidth)?t:0,k=null!=(n=e.activeBarBorderWidth)?n:p,j=null!=(o=e.itemMarginInline)?o:e.marginXXS,E=new C.Y(O).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:i,itemColor:i,colorItemTextHover:i,itemHoverColor:i,colorItemTextHoverHorizontal:c,horizontalItemHoverColor:c,colorGroupTitle:d,groupTitleColor:d,colorItemTextSelected:c,itemSelectedColor:c,subMenuItemSelectedColor:c,colorItemTextSelectedHorizontal:c,horizontalItemSelectedColor:c,colorItemBg:s,itemBg:s,colorItemBgHover:v,itemHoverBg:v,colorItemBgActive:m,itemActiveBg:b,colorSubItemBg:u,subMenuItemBg:u,colorItemBgSelected:b,itemSelectedBg:b,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:z,colorActiveBarHeight:g,activeBarHeight:g,colorActiveBarBorderSize:p,activeBarBorderWidth:k,colorItemTextDisabled:r,itemDisabledColor:r,colorDangerItemText:a,dangerItemColor:a,colorDangerItemTextHover:a,dangerItemHoverColor:a,colorDangerItemTextSelected:a,dangerItemSelectedColor:a,colorDangerItemBgActive:l,dangerItemActiveBg:l,colorDangerItemBgSelected:l,dangerItemSelectedBg:l,itemMarginInline:j,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:h,groupTitleLineHeight:f,collapsedWidth:2*h,popupBg:I,itemMarginBlock:x,itemPaddingInline:y,horizontalLineHeight:"".concat(1.15*h,"px"),iconSize:w,iconMarginInlineEnd:S-w,collapsedIconSize:B,groupTitleFontSize:w,darkItemDisabledColor:new C.Y(O).setA(.25).toRgbString(),darkItemColor:E,darkDangerItemColor:a,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:O,darkItemSelectedBg:c,darkDangerItemSelectedBg:a,darkItemHoverBg:"transparent",darkGroupTitleColor:E,darkItemHoverColor:O,darkDangerItemHoverColor:A,darkDangerItemSelectedColor:O,darkDangerItemActiveBg:a,itemWidth:z?"calc(100% + ".concat(k,"px)"):"calc(100% - ".concat(2*j,"px)")}};var W=n(9130);let X=e=>{var t;let n,{popupClassName:a,icon:r,title:l,theme:d}=e,u=o.useContext(b),{prefixCls:p,inlineCollapsed:g,theme:v}=u,h=(0,c.Wj)();if(r){let e=o.isValidElement(l)&&"span"===l.type;n=o.createElement(o.Fragment,null,(0,m.Ob)(r,{className:i()(o.isValidElement(r)?null==(t=r.props)?void 0:t.className:void 0,"".concat(p,"-item-icon"))}),e?l:o.createElement("span",{className:"".concat(p,"-title-content")},l))}else n=g&&!h.length&&l&&"string"==typeof l?o.createElement("div",{className:"".concat(p,"-inline-collapsed-noicon")},l.charAt(0)):o.createElement("span",{className:"".concat(p,"-title-content")},l);let f=o.useMemo(()=>Object.assign(Object.assign({},u),{firstLevel:!1}),[u]),[I]=(0,W.YK)("Menu");return o.createElement(b.Provider,{value:f},o.createElement(c.g8,Object.assign({},(0,s.A)(e,["icon"]),{title:n,popupClassName:i()(p,a,"".concat(p,"-").concat(d||v)),popupStyle:Object.assign({zIndex:I},e.popupStyle)})))};var q=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,o=Object.getOwnPropertySymbols(e);c<o.length;c++)0>t.indexOf(o[c])&&Object.prototype.propertyIsEnumerable.call(e,o[c])&&(n[o[c]]=e[o[c]]);return n};function _(e){return null===e||!1===e}let Y={item:x,submenu:X,divider:h},F=(0,o.forwardRef)((e,t)=>{var n;let a=o.useContext(y.h),l=a||{},{getPrefixCls:v,getPopupContainer:h,direction:f,menu:I}=o.useContext(p.QO),x=v(),{prefixCls:w,className:C,style:S,theme:H="light",expandIcon:N,_internalDisableMenuItemTitleTooltip:P,inlineCollapsed:M,siderCollapsed:W,rootClassName:X,mode:F,selectable:G,onClick:V,overflowedIndicatorPopupClassName:K}=e,Q=q(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),U=(0,s.A)(Q,["collapsedWidth"]);null==(n=l.validator)||n.call(l,{mode:F});let J=(0,d.A)(function(){for(var e,t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];null==V||V.apply(void 0,n),null==(e=l.onClick)||e.call(l)}),Z=l.mode||F,$=null!=G?G:l.selectable,ee=null!=M?M:W,et={horizontal:{motionName:"".concat(x,"-slide-up")},inline:(0,u.A)(x),other:{motionName:"".concat(x,"-zoom-big")}},en=v("menu",w||l.prefixCls),eo=(0,g.A)(en),[ec,ea,er]=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return(0,z.OF)("Menu",e=>{let{colorBgElevated:t,controlHeightLG:n,fontSize:o,darkItemColor:c,darkDangerItemColor:a,darkItemBg:r,darkSubMenuItemBg:l,darkItemSelectedColor:i,darkItemSelectedBg:d,darkDangerItemSelectedBg:s,darkItemHoverBg:u,darkGroupTitleColor:m,darkItemHoverColor:p,darkItemDisabledColor:g,darkDangerItemHoverColor:b,darkDangerItemSelectedColor:v,darkDangerItemActiveBg:h,popupBg:f,darkPopupBg:I}=e,x=e.calc(o).div(7).mul(5).equal(),y=(0,k.oX)(e,{menuArrowSize:x,menuHorizontalHeight:e.calc(n).mul(1.15).equal(),menuArrowOffset:e.calc(x).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:f}),w=(0,k.oX)(y,{itemColor:c,itemHoverColor:p,groupTitleColor:m,itemSelectedColor:i,subMenuItemSelectedColor:i,itemBg:r,popupBg:I,subMenuItemBg:l,itemActiveBg:"transparent",itemSelectedBg:d,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:u,itemDisabledColor:g,dangerItemColor:a,dangerItemHoverColor:b,dangerItemSelectedColor:v,dangerItemActiveBg:h,dangerItemSelectedBg:s,menuSubMenuBg:l,horizontalItemSelectedColor:i,horizontalItemSelectedBg:d});return[D(y),j(y),R(y),T(y,"light"),T(w,"dark"),E(y),(0,B.A)(y),(0,O._j)(y,"slide-up"),(0,O._j)(y,"slide-down"),(0,A.aB)(y,"zoom-big")]},L,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t)}(en,eo,!a),el=i()("".concat(en,"-").concat(H),null==I?void 0:I.className,C),ei=o.useMemo(()=>{var e,t;if("function"==typeof N||_(N))return N||null;if("function"==typeof l.expandIcon||_(l.expandIcon))return l.expandIcon||null;if("function"==typeof(null==I?void 0:I.expandIcon)||_(null==I?void 0:I.expandIcon))return(null==I?void 0:I.expandIcon)||null;let n=null!=(e=null!=N?N:null==l?void 0:l.expandIcon)?e:null==I?void 0:I.expandIcon;return(0,m.Ob)(n,{className:i()("".concat(en,"-submenu-expand-icon"),o.isValidElement(n)?null==(t=n.props)?void 0:t.className:void 0)})},[N,null==l?void 0:l.expandIcon,null==I?void 0:I.expandIcon,en]),ed=o.useMemo(()=>({prefixCls:en,inlineCollapsed:ee||!1,direction:f,firstLevel:!0,theme:H,mode:Z,disableMenuItemTitleTooltip:P}),[en,ee,f,P,H]);return ec(o.createElement(y.h.Provider,{value:null},o.createElement(b.Provider,{value:ed},o.createElement(c.Ay,Object.assign({getPopupContainer:h,overflowedIndicator:o.createElement(r.A,null),overflowedIndicatorPopupClassName:i()(en,"".concat(en,"-").concat(H),K),mode:Z,selectable:$,onClick:J},U,{inlineCollapsed:ee,style:Object.assign(Object.assign({},null==I?void 0:I.style),S),className:el,prefixCls:en,direction:f,defaultMotions:et,expandIcon:ei,ref:t,rootClassName:i()(X,ea,l.rootClassName,er,eo),_internalComponents:Y})))))}),G=(0,o.forwardRef)((e,t)=>{let n=(0,o.useRef)(null),c=o.useContext(a.P);return(0,o.useImperativeHandle)(t,()=>({menu:n.current,focus:e=>{var t;null==(t=n.current)||t.focus(e)}})),o.createElement(F,Object.assign({ref:n},e,c))});G.Item=x,G.SubMenu=X,G.Divider=h,G.ItemGroup=c.te;let V=G}}]);