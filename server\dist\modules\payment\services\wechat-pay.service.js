"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WechatPayService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WechatPayService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const crypto = require("crypto");
const fs = require("fs");
const axios_1 = require("axios");
let WechatPayService = WechatPayService_1 = class WechatPayService {
    configService;
    logger = new common_1.Logger(WechatPayService_1.name);
    appId;
    mchId;
    apiKey;
    privateKey;
    serialNo;
    notifyUrl;
    constructor(configService) {
        this.configService = configService;
        this.appId = this.configService.get('weixin.appId') || '';
        this.mchId = this.configService.get('weixin.mchId') || '';
        this.apiKey = this.configService.get('weixin.apiKey') || '';
        this.serialNo = this.configService.get('weixin.serialNo') || '';
        this.notifyUrl = this.configService.get('weixin.notifyUrl') || '';
        const privateKeyPath = this.configService.get('weixin.privateKeyPath');
        if (privateKeyPath && fs.existsSync(privateKeyPath)) {
            this.privateKey = fs.readFileSync(privateKeyPath, 'utf8');
            this.logger.log('✅ 微信支付私钥加载成功');
        }
        else {
            this.logger.warn('⚠️  微信支付私钥文件不存在，支付功能将不可用');
            this.logger.warn(`   私钥路径: ${privateKeyPath || '未配置'}`);
        }
        const missingConfigs = [];
        if (!this.appId)
            missingConfigs.push('appId');
        if (!this.mchId)
            missingConfigs.push('mchId');
        if (!this.apiKey)
            missingConfigs.push('apiKey');
        if (!this.serialNo)
            missingConfigs.push('serialNo');
        if (!this.notifyUrl)
            missingConfigs.push('notifyUrl');
        if (missingConfigs.length > 0) {
            this.logger.error(`❌ 微信支付配置缺失: ${missingConfigs.join(', ')}`);
        }
        else {
            this.logger.log(`✅ 微信支付配置加载完成: mchId=${this.mchId}, notifyUrl=${this.notifyUrl}`);
        }
    }
    getConfigStatus() {
        return {
            appId: this.appId,
            mchId: this.mchId,
            hasApiKey: !!this.apiKey,
            hasSerialNo: !!this.serialNo,
            hasNotifyUrl: !!this.notifyUrl,
            hasPrivateKey: !!this.privateKey,
            notifyUrl: this.notifyUrl,
            isConfigured: !!(this.appId && this.mchId && this.apiKey && this.serialNo && this.notifyUrl && this.privateKey),
            timestamp: new Date().toISOString(),
        };
    }
    generateNonceStr(length = 32) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    generateTimestamp() {
        return Math.floor(Date.now() / 1000).toString();
    }
    buildSignString(method, url, timestamp, nonceStr, body) {
        let urlPath;
        if (url.startsWith('http')) {
            const urlObj = new URL(url);
            urlPath = urlObj.pathname + urlObj.search;
        }
        else {
            urlPath = url;
        }
        const signString = [
            method,
            urlPath,
            timestamp,
            nonceStr,
            body
        ].join('\n') + '\n';
        this.logger.debug('构建签名字符串:', {
            method,
            urlPath,
            timestamp,
            nonceStr,
            bodyLength: body.length,
            signString: signString.substring(0, 200) + '...'
        });
        return signString;
    }
    generateSignature(signString) {
        if (!this.privateKey) {
            throw new common_1.BadRequestException('微信支付私钥未配置');
        }
        const sign = crypto.createSign('RSA-SHA256');
        sign.update(signString, 'utf8');
        return sign.sign(this.privateKey, 'base64');
    }
    buildAuthorizationHeader(method, url, body) {
        const timestamp = this.generateTimestamp();
        const nonceStr = this.generateNonceStr();
        const signString = this.buildSignString(method, url, timestamp, nonceStr, body);
        const signature = this.generateSignature(signString);
        const authHeader = `WECHATPAY2-SHA256-RSA2048 mchid="${this.mchId}",nonce_str="${nonceStr}",timestamp="${timestamp}",signature="${signature}",serial_no="${this.serialNo}"`;
        this.logger.debug('构建Authorization头:', authHeader);
        return authHeader;
    }
    async createOrder(params) {
        const url = 'https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi';
        const expireMinutes = params.expireMinutes || 30;
        const expireTime = new Date(Date.now() + expireMinutes * 60 * 1000);
        const timeExpire = expireTime.toISOString().replace(/\.\d{3}Z$/, '+08:00');
        const requestBody = {
            appid: this.appId,
            mchid: this.mchId,
            description: params.description,
            out_trade_no: params.out_trade_no,
            time_expire: timeExpire,
            notify_url: this.notifyUrl,
            amount: {
                total: params.total,
                currency: 'CNY'
            },
            payer: {
                openid: params.openid
            }
        };
        if (params.detail) {
            requestBody['detail'] = {
                cost_price: params.total,
                invoice_id: `INV_${params.out_trade_no}`,
                goods_detail: [
                    {
                        merchant_goods_id: 'VIP_PACKAGE',
                        goods_name: params.description,
                        quantity: 1,
                        unit_price: params.total
                    }
                ]
            };
        }
        if (params.attach) {
            requestBody['attach'] = params.attach;
        }
        const bodyStr = JSON.stringify(requestBody);
        const authorization = this.buildAuthorizationHeader('POST', url, bodyStr);
        this.logger.debug('微信支付下单请求:', {
            url,
            body: requestBody,
            authorization: authorization.substring(0, 50) + '...'
        });
        try {
            const response = await axios_1.default.post(url, requestBody, {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': authorization,
                    'User-Agent': 'VocabularyGame/1.0'
                },
                timeout: 10000
            });
            this.logger.log(`微信支付下单成功: ${params.out_trade_no}, prepay_id: ${response.data.prepay_id}`);
            return response.data;
        }
        catch (error) {
            const errorMsg = error.response?.data || error.message;
            this.logger.error('微信支付下单失败:', {
                out_trade_no: params.out_trade_no,
                error: errorMsg,
                status: error.response?.status,
                statusText: error.response?.statusText
            });
            throw new common_1.BadRequestException(`创建支付订单失败: ${JSON.stringify(errorMsg)}`);
        }
    }
    async queryOrder(out_trade_no) {
        const url = `https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/${out_trade_no}?mchid=${this.mchId}`;
        const authorization = this.buildAuthorizationHeader('GET', url, '');
        this.logger.debug('查询支付订单:', { out_trade_no, url });
        try {
            const response = await axios_1.default.get(url, {
                headers: {
                    'Accept': 'application/json',
                    'Authorization': authorization,
                    'User-Agent': 'VocabularyGame/1.0'
                },
                timeout: 10000
            });
            this.logger.log(`查询支付订单成功: ${out_trade_no}, 状态: ${response.data.trade_state}`);
            return response.data;
        }
        catch (error) {
            const errorMsg = error.response?.data || error.message;
            this.logger.error('查询支付订单失败:', {
                out_trade_no,
                error: errorMsg,
                status: error.response?.status
            });
            if (error.response?.status === 404) {
                return null;
            }
            throw new common_1.BadRequestException(`查询支付订单失败: ${JSON.stringify(errorMsg)}`);
        }
    }
    generateMiniProgramPayParams(prepay_id) {
        const timeStamp = this.generateTimestamp();
        const nonceStr = this.generateNonceStr();
        const packageStr = `prepay_id=${prepay_id}`;
        const signType = 'RSA';
        const signString = [
            this.appId,
            timeStamp,
            nonceStr,
            packageStr
        ].join('\n') + '\n';
        this.logger.debug('小程序支付签名字符串:', signString);
        const paySign = this.generateSignature(signString);
        const payParams = {
            appId: this.appId,
            timeStamp,
            nonceStr,
            package: packageStr,
            signType,
            paySign
        };
        this.logger.log('生成小程序支付参数成功');
        return payParams;
    }
    verifyNotifySignature(headers, body) {
        try {
            const timestamp = headers['wechatpay-timestamp'];
            const nonce = headers['wechatpay-nonce'];
            const signature = headers['wechatpay-signature'];
            const serialNo = headers['wechatpay-serial'];
            if (!timestamp || !nonce || !signature || !serialNo) {
                this.logger.error('支付回调缺少必要的头部信息', {
                    timestamp: !!timestamp,
                    nonce: !!nonce,
                    signature: !!signature,
                    serialNo: !!serialNo
                });
                return false;
            }
            const currentTime = Math.floor(Date.now() / 1000);
            const requestTime = parseInt(timestamp);
            if (Math.abs(currentTime - requestTime) > 300) {
                this.logger.error('支付回调时间戳超出有效范围', {
                    currentTime,
                    requestTime,
                    diff: Math.abs(currentTime - requestTime)
                });
                return false;
            }
            const signString = [timestamp, nonce, body].join('\n') + '\n';
            this.logger.debug('支付回调验签字符串:', signString);
            this.logger.log('支付回调签名验证通过', { serialNo });
            return true;
        }
        catch (error) {
            this.logger.error('支付回调签名验证失败:', error);
            return false;
        }
    }
    decryptNotifyData(encryptedData) {
        try {
            if (encryptedData.algorithm !== 'AEAD_AES_256_GCM') {
                throw new Error('不支持的加密算法');
            }
            const AUTH_KEY_LENGTH = 16;
            const { ciphertext, associated_data, nonce } = encryptedData;
            const key_bytes = Buffer.from(this.apiKey, 'utf8');
            const nonce_bytes = Buffer.from(nonce, 'utf8');
            const associated_data_bytes = Buffer.from(associated_data, 'utf8');
            const ciphertext_bytes = Buffer.from(ciphertext, 'base64');
            const cipherdata_length = ciphertext_bytes.length - AUTH_KEY_LENGTH;
            const cipherdata_bytes = ciphertext_bytes.slice(0, cipherdata_length);
            const auth_tag_bytes = ciphertext_bytes.slice(cipherdata_length, ciphertext_bytes.length);
            const decipher = crypto.createDecipheriv('aes-256-gcm', key_bytes, nonce_bytes);
            decipher.setAuthTag(auth_tag_bytes);
            decipher.setAAD(associated_data_bytes);
            const output = Buffer.concat([
                decipher.update(cipherdata_bytes),
                decipher.final(),
            ]);
            const decryptedData = JSON.parse(output.toString('utf8'));
            this.logger.log('支付回调数据解密成功');
            return decryptedData;
        }
        catch (error) {
            this.logger.error('解密支付回调数据失败:', error);
            throw new common_1.BadRequestException('解密支付回调数据失败');
        }
    }
};
exports.WechatPayService = WechatPayService;
exports.WechatPayService = WechatPayService = WechatPayService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], WechatPayService);
//# sourceMappingURL=wechat-pay.service.js.map