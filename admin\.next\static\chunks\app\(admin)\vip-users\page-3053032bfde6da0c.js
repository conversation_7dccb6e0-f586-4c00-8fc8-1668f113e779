(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1298],{34095:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var l=t(79630),i=t(12115);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 310H732.4c13.6-21.4 21.6-46.8 21.6-74 0-76.1-61.9-138-138-138-41.4 0-78.7 18.4-104 47.4-25.3-29-62.6-47.4-104-47.4-76.1 0-138 61.9-138 138 0 27.2 7.9 52.6 21.6 74H144c-17.7 0-32 14.3-32 32v200c0 4.4 3.6 8 8 8h40v344c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V550h40c4.4 0 8-3.6 8-8V342c0-17.7-14.3-32-32-32zm-334-74c0-38.6 31.4-70 70-70s70 31.4 70 70-31.4 70-70 70h-70v-70zm-138-70c38.6 0 70 31.4 70 70v70h-70c-38.6 0-70-31.4-70-70s31.4-70 70-70zM180 482V378h298v104H180zm48 68h250v308H228V550zm568 308H546V550h250v308zm48-376H546V378h298v104z"}}]},name:"gift",theme:"outlined"};var r=t(62764);let c=i.forwardRef(function(e,s){return i.createElement(r.A,(0,l.A)({},e,{ref:s,icon:n}))})},40357:(e,s,t)=>{Promise.resolve().then(t.bind(t,59852))},50274:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var l=t(79630),i=t(12115);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var r=t(62764);let c=i.forwardRef(function(e,s){return i.createElement(r.A,(0,l.A)({},e,{ref:s,icon:n}))})},59852:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var l=t(95155),i=t(12115),n=t(56020),r=t(24971),c=t(19868),d=t(37974),a=t(12320),o=t(26922),h=t(30662),x=t(19361),j=t(74947),p=t(505),m=t(44297),A=t(20778),g=t(51087),v=t(32814),u=t(97550),y=t(52092),f=t(50274),V=t(34095),S=t(34140),k=t(49179),w=t(30832),z=t.n(w);let{Search:I}=n.A,{RangePicker:Y}=r.A,P=()=>{let[e,s]=(0,i.useState)([]),[t,n]=(0,i.useState)(!1),[r,w]=(0,i.useState)(!1),[P,D]=(0,i.useState)(null),[C,M]=(0,i.useState)({totalUsers:0,vipUsers:0,normalUsers:0,vipRate:0,totalRevenue:0,avgDailyUnlocks:0}),[B,H]=(0,i.useState)(""),[L,U]=(0,i.useState)(""),[R,b]=(0,i.useState)(null),E=async()=>{n(!0);try{let e={};B&&(e.search=B),L&&(e.isVip="true"===L),R&&(e.startDate=R[0].format("YYYY-MM-DD"),e.endDate=R[1].format("YYYY-MM-DD"));let t=(await k.mz.getList(e)).users||[];s(t);let l=_(t);M(l)}catch(e){c.Ay.error("获取用户列表失败")}finally{n(!1)}},_=e=>{let s=e.length,t=e.filter(e=>e.isVip).length,l=s>0?e.reduce((e,s)=>e+s.dailyUnlockCount,0)/s:0;return{totalUsers:s,vipUsers:t,normalUsers:s-t,vipRate:s>0?t/s*100:0,totalRevenue:0,avgDailyUnlocks:l}};(0,i.useEffect)(()=>{E()},[B,L,R]);let O=e=>{D(e),w(!0)},T=async e=>{try{await k.mz.updateVipStatus(e.id,!e.isVip),c.Ay.success("".concat(e.isVip?"取消":"设置","VIP成功")),E()}catch(e){c.Ay.error("VIP状态更新失败")}},N=e=>!e||e.length<11?e:e.replace(/(\d{3})\d{4}(\d{4})/,"$1****$2"),G=[{title:"用户信息",key:"userInfo",width:250,render:(e,s)=>(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:4},children:[(0,l.jsx)("span",{style:{fontWeight:"bold",marginRight:8},children:s.nickname||"微信用户"}),s.isVip&&(0,l.jsx)(d.A,{color:"gold",icon:(0,l.jsx)(u.A,{}),children:"VIP"})]}),(0,l.jsxs)("div",{style:{fontSize:"12px",color:"#666",marginBottom:2},children:["手机: ",N(s.phone)]}),(0,l.jsxs)("div",{style:{fontSize:"12px",color:"#999"},children:["ID: ",s.id]})]})},{title:"游戏数据",key:"gameData",width:150,render:(e,s)=>(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{style:{marginBottom:2},children:["解锁: ",s.unlockedLevels," 关"]}),(0,l.jsxs)("div",{style:{marginBottom:2},children:["完成: ",s.completedLevelIds.length," 关"]}),(0,l.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["总游戏: ",s.totalGames," 次"]})]})},{title:"每日解锁",key:"dailyUnlock",width:150,render:(e,s)=>(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{children:["今日: ",s.dailyUnlockCount,"/",s.isVip?"∞":s.dailyUnlockLimit]}),(0,l.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["分享: ",s.totalShares," 次"]}),s.dailyShared&&(0,l.jsx)(d.A,{color:"green",children:"今日已分享"})]})},{title:"VIP状态",key:"vipStatus",width:100,render:(e,s)=>s.isVip?(0,l.jsx)(d.A,{color:"gold",icon:(0,l.jsx)(u.A,{}),children:"VIP"}):(0,l.jsx)(d.A,{color:"default",children:"普通"})},{title:"最后游戏",key:"lastPlay",width:160,render:(e,s)=>(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:z()(s.lastPlayTime).format("YYYY-MM-DD")}),(0,l.jsx)("div",{style:{fontSize:"12px",color:"#666"},children:z()(s.lastPlayTime).format("HH:mm:ss")})]})},{title:"注册时间",key:"createdAt",width:160,render:(e,s)=>(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:z()(s.createdAt).format("YYYY-MM-DD")}),(0,l.jsx)("div",{style:{fontSize:"12px",color:"#666"},children:z()(s.createdAt).format("HH:mm:ss")})]})},{title:"操作",key:"action",width:180,render:(e,s)=>(0,l.jsxs)(a.A,{size:"small",children:[(0,l.jsx)(o.A,{title:"查看详情",children:(0,l.jsx)(h.Ay,{type:"link",size:"small",icon:(0,l.jsx)(y.A,{}),onClick:()=>O(s),children:"详情"})}),(0,l.jsx)(o.A,{title:s.isVip?"取消VIP":"设为VIP",children:(0,l.jsx)(h.Ay,{type:"link",size:"small",icon:(0,l.jsx)(u.A,{}),style:{color:s.isVip?"#faad14":"#d9d9d9"},onClick:()=>T(s),children:"VIP"})})]})}];return(0,l.jsxs)("div",{style:{padding:"24px"},children:[(0,l.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,l.jsxs)("h1",{style:{fontSize:"24px",fontWeight:"bold",margin:0},children:[(0,l.jsx)(u.A,{style:{color:"#faad14",marginRight:"8px"}}),"VIP用户管理"]}),(0,l.jsx)("p",{style:{color:"#666",margin:"8px 0 0 0"},children:"管理VIP用户，查看用户游戏数据和VIP状态"})]}),(0,l.jsxs)(x.A,{gutter:16,style:{marginBottom:"24px"},children:[(0,l.jsx)(j.A,{span:6,children:(0,l.jsx)(p.A,{children:(0,l.jsx)(m.A,{title:"总用户数",value:C.totalUsers,prefix:(0,l.jsx)(f.A,{})})})}),(0,l.jsx)(j.A,{span:6,children:(0,l.jsx)(p.A,{children:(0,l.jsx)(m.A,{title:"VIP用户",value:C.vipUsers,valueStyle:{color:"#faad14"},prefix:(0,l.jsx)(u.A,{})})})}),(0,l.jsx)(j.A,{span:6,children:(0,l.jsx)(p.A,{children:(0,l.jsx)(m.A,{title:"VIP转化率",value:C.vipRate,precision:1,suffix:"%",valueStyle:{color:C.vipRate>=10?"#3f8600":"#cf1322"}})})}),(0,l.jsx)(j.A,{span:6,children:(0,l.jsx)(p.A,{children:(0,l.jsx)(m.A,{title:"平均解锁数",value:C.avgDailyUnlocks,precision:1,suffix:"次/日",prefix:(0,l.jsx)(V.A,{})})})})]}),(0,l.jsx)(p.A,{style:{marginBottom:"16px"},children:(0,l.jsxs)(x.A,{gutter:16,align:"middle",children:[(0,l.jsx)(j.A,{span:8,children:(0,l.jsx)(I,{placeholder:"搜索用户ID、手机号或昵称",allowClear:!0,onSearch:H,style:{width:"100%"}})}),(0,l.jsx)(j.A,{span:4,children:(0,l.jsxs)(A.A,{placeholder:"VIP状态",allowClear:!0,style:{width:"100%"},value:L||void 0,onChange:U,children:[(0,l.jsx)(A.A.Option,{value:"true",children:"VIP用户"}),(0,l.jsx)(A.A.Option,{value:"false",children:"普通用户"})]})}),(0,l.jsx)(j.A,{span:8,children:(0,l.jsx)(Y,{style:{width:"100%"},value:R,onChange:e=>b(e),placeholder:["开始日期","结束日期"]})}),(0,l.jsx)(j.A,{span:4,children:(0,l.jsx)(h.Ay,{icon:(0,l.jsx)(S.A,{}),onClick:E,loading:t,style:{width:"100%"},children:"刷新"})})]})}),(0,l.jsx)(g.A,{columns:G,dataSource:e,rowKey:"id",loading:t,pagination:{total:e.length,pageSize:20,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"共 ".concat(e," 个用户")},scroll:{x:1200}}),(0,l.jsx)(v.A,{title:"用户详情",open:r,onCancel:()=>w(!1),footer:[(0,l.jsx)(h.Ay,{onClick:()=>w(!1),children:"关闭"},"close")],width:700,children:P&&(0,l.jsxs)("div",{children:[(0,l.jsxs)(x.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"用户ID:"})," ",P.id]})}),(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"VIP状态:"}),P.isVip?(0,l.jsx)(d.A,{color:"gold",icon:(0,l.jsx)(u.A,{}),style:{marginLeft:8},children:"VIP"}):(0,l.jsx)(d.A,{color:"default",style:{marginLeft:8},children:"普通用户"})]})})]}),(0,l.jsxs)(x.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"手机号:"})," ",P.phone]})}),(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"昵称:"})," ",P.nickname||"-"]})})]}),(0,l.jsxs)(x.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"OpenID:"})," ",P.openid||"-"]})}),(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"每日解锁限制:"})," ",P.isVip?"无限制":"".concat(P.dailyUnlockLimit,"次")]})})]}),(0,l.jsxs)(x.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"今日解锁次数:"})," ",P.dailyUnlockCount]})}),(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"总分享次数:"})," ",P.totalShares]})})]}),(0,l.jsxs)(x.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"已解锁关卡:"})," ",P.unlockedLevels]})}),(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"已完成关卡:"})," ",P.completedLevelIds.length]})})]}),(0,l.jsxs)(x.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"总游戏次数:"})," ",P.totalGames]})}),(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"总通关次数:"})," ",P.totalCompletions]})})]}),(0,l.jsxs)(x.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"最后游戏时间:"})," ",z()(P.lastPlayTime).format("YYYY-MM-DD HH:mm:ss")]})}),(0,l.jsx)(j.A,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"注册时间:"})," ",z()(P.createdAt).format("YYYY-MM-DD HH:mm:ss")]})})]}),(0,l.jsx)(x.A,{gutter:16,children:(0,l.jsx)(j.A,{span:24,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"今日是否已分享:"}),P.dailyShared?(0,l.jsx)(d.A,{color:"green",style:{marginLeft:8},children:"已分享"}):(0,l.jsx)(d.A,{color:"default",style:{marginLeft:8},children:"未分享"})]})})})]})})]})}},97550:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var l=t(79630),i=t(12115);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M899.6 276.5L705 396.4 518.4 147.5a8.06 8.06 0 00-12.9 0L319 396.4 124.3 276.5c-5.7-3.5-13.1 1.2-12.2 7.9L188.5 865c1.1 7.9 7.9 14 16 14h615.1c8 0 14.9-6 15.9-14l76.4-580.6c.8-6.7-6.5-11.4-12.3-7.9zm-126 534.1H250.3l-53.8-409.4 139.8 86.1L512 252.9l175.7 234.4 139.8-86.1-53.9 409.4zM512 509c-62.1 0-112.6 50.5-112.6 112.6S449.9 734.2 512 734.2s112.6-50.5 112.6-112.6S574.1 509 512 509zm0 160.9c-26.6 0-48.2-21.6-48.2-48.3 0-26.6 21.6-48.3 48.2-48.3s48.2 21.6 48.2 48.3c0 26.6-21.6 48.3-48.2 48.3z"}}]},name:"crown",theme:"outlined"};var r=t(62764);let c=i.forwardRef(function(e,s){return i.createElement(r.A,(0,l.A)({},e,{ref:s,icon:n}))})}},e=>{var s=s=>e(e.s=s);e.O(0,[8340,547,7469,7497,44,6312,778,2343,3726,4124,2907,9179,8441,1684,7358],()=>s(40357)),_N_E=e.O()}]);