import { Document } from 'mongoose';
export type ThesaurusDocument = Thesaurus & Document;
export declare class Thesaurus {
    id: string;
    name: string;
    description?: string;
    phraseIds: string[];
    createdAt: Date;
    updatedAt: Date;
}
export declare const ThesaurusSchema: import("mongoose").Schema<Thesaurus, import("mongoose").Model<Thesaurus, any, any, any, Document<unknown, any, Thesaurus, any> & Thesaurus & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Thesaurus, Document<unknown, {}, import("mongoose").FlatRecord<Thesaurus>, {}> & import("mongoose").FlatRecord<Thesaurus> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
export type ThesaurusEntity = Thesaurus;
