import { ShareService } from './share.service';
import { CreateShareConfigDto } from './dto/create-share-config.dto';
import { UpdateShareConfigDto } from './dto/update-share-config.dto';
import { ShareConfigResponseDto } from './dto/share-config-response.dto';
export declare class ShareController {
    private readonly shareService;
    constructor(shareService: ShareService);
    create(createShareConfigDto: CreateShareConfigDto): Promise<ShareConfigResponseDto>;
    findAll(): Promise<ShareConfigResponseDto[]>;
    findActive(): Promise<ShareConfigResponseDto[]>;
    getDefault(): Promise<ShareConfigResponseDto | null>;
    findByType(type: string): Promise<ShareConfigResponseDto | null>;
    findOne(id: string): Promise<ShareConfigResponseDto>;
    update(id: string, updateShareConfigDto: UpdateShareConfigDto): Promise<ShareConfigResponseDto>;
    toggleActive(id: string): Promise<ShareConfigResponseDto>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
