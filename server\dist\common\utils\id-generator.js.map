{"version": 3, "file": "id-generator.js", "sourceRoot": "", "sources": ["../../../src/common/utils/id-generator.ts"], "names": [], "mappings": ";;AAaA,oDAaC;AAOD,8DAGC;AAOD,oEAiBC;AAOD,wDAaC;AAMD,oCAEC;AAMD,8CAEC;AAMD,8CAEC;AAQD,4CAcC;AAxHD,+BAAoC;AAOpC,SAAgB,oBAAoB;IAClC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;IAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1D,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACnD,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACrD,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACzD,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAGzD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7D,OAAO,OAAO,IAAI,GAAG,KAAK,GAAG,GAAG,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,IAAI,SAAS,EAAE,CAAC;AAC5E,CAAC;AAOD,SAAgB,yBAAyB;IACvC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC9D,OAAO,OAAO,SAAS,EAAE,CAAC;AAC5B,CAAC;AAOD,SAAgB,4BAA4B,CAAC,IAAY,EAAE,QAAgB;IAEzE,IAAI,IAAI,GAAG,QAAQ,CAAC;IACpB,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC;QAClB,IAAI,GAAG,QAAQ,CAAC;IAClB,CAAC;SAAM,IAAI,QAAQ,IAAI,EAAE,EAAE,CAAC;QAC1B,IAAI,GAAG,SAAS,CAAC;IACnB,CAAC;SAAM,IAAI,QAAQ,IAAI,EAAE,EAAE,CAAC;QAC1B,IAAI,GAAG,WAAW,CAAC;IACrB,CAAC;SAAM,IAAI,QAAQ,IAAI,GAAG,EAAE,CAAC;QAC3B,IAAI,GAAG,QAAQ,CAAC;IAClB,CAAC;IAGD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7D,OAAO,OAAO,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;AACjD,CAAC;AAOD,SAAgB,sBAAsB;IACpC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;IAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1D,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACnD,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACrD,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACzD,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAGzD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7D,OAAO,OAAO,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,GAAG,MAAM,GAAG,MAAM,IAAI,SAAS,EAAE,CAAC;AAC3E,CAAC;AAMD,SAAgB,YAAY;IAC1B,OAAO,IAAA,SAAM,GAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACpC,CAAC;AAMD,SAAgB,iBAAiB;IAC/B,OAAO,IAAA,SAAM,GAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACrD,CAAC;AAMD,SAAgB,iBAAiB;IAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;AACpE,CAAC;AAQM,KAAK,UAAU,gBAAgB,CACpC,OAAyC,EACzC,UAAwB,EACxB,aAAqB,EAAE;IAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;QACpC,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC;QACxB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;QACjC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,eAAe,UAAU,GAAG,CAAC,CAAC;AAChD,CAAC"}