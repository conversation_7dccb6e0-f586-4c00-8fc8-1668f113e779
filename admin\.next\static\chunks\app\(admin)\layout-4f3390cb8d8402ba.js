(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4012],{86784:(e,s,l)=>{Promise.resolve().then(l.bind(l,97177))},97177:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>P});var t=l(95155),n=l(12115),a=l(6874),i=l.n(a),r=l(35695),o=l(86253),c=l(97605),h=l(83803),p=l(82343),d=l(28562),x=l(63330),y=l(73086),j=l(77133),u=l(96097),k=l(44318),g=l(34095),m=l(97550),b=l(36020),A=l(92611),f=l(27540),v=l(50274);let{Header:_,Content:C,Sider:I,Footer:w}=o.A,E=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,t.jsx)(x.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,t.jsx)(y.A,{})},{key:"phrases",label:"词组管理",path:"/phrases",icon:(0,t.jsx)(j.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,t.jsx)(u.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,t.jsx)(k.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,t.jsx)(g.A,{})},{key:"vip-users",label:"VIP用户管理",path:"/vip-users",icon:(0,t.jsx)(m.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,t.jsx)(b.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,t.jsx)(A.A,{})}];function P(e){var s;let{children:l}=e,a=(0,r.useRouter)(),x=(0,r.usePathname)(),[y,j]=(0,n.useState)(!1);(0,n.useEffect)(()=>{localStorage.getItem("admin_token")||a.replace("/login")},[a]);let u=[{key:"logout",icon:(0,t.jsx)(f.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),a.push("/login")}}],k=(null==(s=E.find(e=>x.startsWith(e.path)))?void 0:s.key)||"dashboard";return(0,t.jsxs)(o.A,{style:{minHeight:"100vh"},children:[(0,t.jsxs)(I,{collapsible:!0,collapsed:y,onCollapse:e=>j(e),children:[(0,t.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,t.jsx)(c.A.Text,{style:{color:"white",fontSize:y?"10px":"16px",transition:"font-size 0.2s"},children:y?"后台":"游戏管理后台"})}),(0,t.jsx)(h.A,{theme:"dark",selectedKeys:[k],mode:"inline",items:E.map(e=>({key:e.key,icon:e.icon,label:(0,t.jsx)(i(),{href:e.path,children:e.label})}))})]}),(0,t.jsxs)(o.A,{children:[(0,t.jsxs)(_,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,t.jsx)(p.A,{menu:{items:u},placement:"bottomRight",children:(0,t.jsx)(d.A,{style:{cursor:"pointer"},icon:(0,t.jsx)(v.A,{})})}),(0,t.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,t.jsx)(C,{style:{margin:"16px"},children:l}),(0,t.jsxs)(w,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8340,547,7497,7605,2343,8436,3760,8441,1684,7358],()=>s(86784)),_N_E=e.O()}]);