(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4572],{5983:(e,t,l)=>{Promise.resolve().then(l.bind(l,45742))},18517:(e,t,l)=>{"use strict";l.d(t,{A:()=>r});var n=l(79630),a=l(12115);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.7 630.2 359 721.7 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.7 758.3 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM184 352V232h64v207.6a91.99 91.99 0 01-64-87.6zm520 128c0 49.1-19.1 95.4-53.9 130.1-34.8 34.8-81 53.9-130.1 53.9h-16c-49.1 0-95.4-19.1-130.1-53.9-34.8-34.8-53.9-81-53.9-130.1V184h384v296zm136-128c0 41-26.9 75.8-64 87.6V232h64v120z"}}]},name:"trophy",theme:"outlined"};var s=l(62764);let r=a.forwardRef(function(e,t){return a.createElement(s.A,(0,n.A)({},e,{ref:t,icon:i}))})},34140:(e,t,l)=>{"use strict";l.d(t,{A:()=>r});var n=l(79630),a=l(12115);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"};var s=l(62764);let r=a.forwardRef(function(e,t){return a.createElement(s.A,(0,n.A)({},e,{ref:t,icon:i}))})},44297:(e,t,l)=>{"use strict";l.d(t,{A:()=>b});var n=l(12115),a=l(11719),i=l(16962),s=l(80163),r=l(29300),c=l.n(r),o=l(40032),d=l(15982),p=l(70802);let h=e=>{let t,{value:l,formatter:a,precision:i,decimalSeparator:s,groupSeparator:r="",prefixCls:c}=e;if("function"==typeof a)t=a(l);else{let e=String(l),a=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(a&&"-"!==e){let e=a[1],l=a[2]||"0",o=a[4]||"";l=l.replace(/\B(?=(\d{3})+(?!\d))/g,r),"number"==typeof i&&(o=o.padEnd(i,"0").slice(0,i>0?i:0)),o&&(o="".concat(s).concat(o)),t=[n.createElement("span",{key:"int",className:"".concat(c,"-content-value-int")},e,l),o&&n.createElement("span",{key:"decimal",className:"".concat(c,"-content-value-decimal")},o)]}else t=e}return n.createElement("span",{className:"".concat(c,"-content-value")},t)};var u=l(18184),m=l(45431),x=l(61388);let y=e=>{let{componentCls:t,marginXXS:l,padding:n,colorTextDescription:a,titleFontSize:i,colorTextHeading:s,contentFontSize:r,fontFamily:c}=e;return{[t]:Object.assign(Object.assign({},(0,u.dF)(e)),{["".concat(t,"-title")]:{marginBottom:l,color:a,fontSize:i},["".concat(t,"-skeleton")]:{paddingTop:n},["".concat(t,"-content")]:{color:s,fontSize:r,fontFamily:c,["".concat(t,"-content-value")]:{display:"inline-block",direction:"ltr"},["".concat(t,"-content-prefix, ").concat(t,"-content-suffix")]:{display:"inline-block"},["".concat(t,"-content-prefix")]:{marginInlineEnd:l},["".concat(t,"-content-suffix")]:{marginInlineStart:l}}})}},j=(0,m.OF)("Statistic",e=>[y((0,x.oX)(e,{}))],e=>{let{fontSizeHeading3:t,fontSize:l}=e;return{titleFontSize:l,contentFontSize:t}});var A=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(l[n[a]]=e[n[a]]);return l};let f=n.forwardRef((e,t)=>{let{prefixCls:l,className:a,rootClassName:i,style:s,valueStyle:r,value:u=0,title:m,valueRender:x,prefix:y,suffix:f,loading:v=!1,formatter:g,precision:k,decimalSeparator:w=".",groupSeparator:b=",",onMouseEnter:S,onMouseLeave:O}=e,I=A(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:C,direction:z,className:E,style:V}=(0,d.TP)("statistic"),L=C("statistic",l),[P,D,N]=j(L),U=n.createElement(h,{decimalSeparator:w,groupSeparator:b,prefixCls:L,formatter:g,precision:k,value:u}),T=c()(L,{["".concat(L,"-rtl")]:"rtl"===z},E,a,i,D,N),F=n.useRef(null);n.useImperativeHandle(t,()=>({nativeElement:F.current}));let M=(0,o.A)(I,{aria:!0,data:!0});return P(n.createElement("div",Object.assign({},M,{ref:F,className:T,style:Object.assign(Object.assign({},V),s),onMouseEnter:S,onMouseLeave:O}),m&&n.createElement("div",{className:"".concat(L,"-title")},m),n.createElement(p.A,{paragraph:!1,loading:v,className:"".concat(L,"-skeleton")},n.createElement("div",{style:r,className:"".concat(L,"-content")},y&&n.createElement("span",{className:"".concat(L,"-content-prefix")},y),x?x(U):U,f&&n.createElement("span",{className:"".concat(L,"-content-suffix")},f)))))}),v=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var g=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(l[n[a]]=e[n[a]]);return l};let k=e=>{let{value:t,format:l="HH:mm:ss",onChange:r,onFinish:c,type:o}=e,d=g(e,["value","format","onChange","onFinish","type"]),p="countdown"===o,[h,u]=n.useState(null),m=(0,a._q)(()=>{let e=Date.now(),l=new Date(t).getTime();return u({}),null==r||r(p?l-e:e-l),!p||!(l<e)||(null==c||c(),!1)});return n.useEffect(()=>{let e,t=()=>{e=(0,i.A)(()=>{m()&&t()})};return t(),()=>i.A.cancel(e)},[t,p]),n.useEffect(()=>{u({})},[]),n.createElement(f,Object.assign({},d,{value:t,valueRender:e=>(0,s.Ob)(e,{title:void 0}),formatter:(e,t)=>h?function(e,t,l){let{format:n=""}=t,a=new Date(e).getTime(),i=Date.now();return function(e,t){let l=e,n=/\[[^\]]*]/g,a=(t.match(n)||[]).map(e=>e.slice(1,-1)),i=t.replace(n,"[]"),s=v.reduce((e,t)=>{let[n,a]=t;if(e.includes(n)){let t=Math.floor(l/a);return l-=t*a,e.replace(RegExp("".concat(n,"+"),"g"),e=>{let l=e.length;return t.toString().padStart(l,"0")})}return e},i),r=0;return s.replace(n,()=>{let e=a[r];return r+=1,e})}(l?Math.max(a-i,0):Math.max(i-a,0),n)}(e,Object.assign(Object.assign({},t),{format:l}),p):"-"}))},w=n.memo(e=>n.createElement(k,Object.assign({},e,{type:"countdown"})));f.Timer=k,f.Countdown=w;let b=f},45742:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>V});var n=l(95155),a=l(12115),i=l(44670),s=l(19868),r=l(12320),c=l(28562),o=l(37974),d=l(30662),p=l(26922),h=l(27212),u=l(505),m=l(51087),x=l(32814),y=l(56020),j=l(10642),A=l(13324),f=l(19361),v=l(74947),g=l(44297),k=l(50274),w=l(97550),b=l(18517),S=l(79659),O=l(34140),I=l(56170),C=l(46996),z=l(34095),E=l(49179);function V(){let[e,t]=(0,a.useState)([]),[l,V]=(0,a.useState)(!1),[L,P]=(0,a.useState)(!1),[D,N]=(0,a.useState)(!1),[U,T]=(0,a.useState)(null),[F,M]=(0,a.useState)(null),[R]=i.A.useForm(),H=async()=>{V(!0);try{let e=(await E.Dv.getAll()).users||[];t(e)}catch(e){s.Ay.error("获取用户列表失败")}finally{V(!1)}};(0,a.useEffect)(()=>{H()},[]);let B=async e=>{try{U?(await E.Dv.update(U.id,e),s.Ay.success("用户更新成功")):(await E.Dv.create(e),s.Ay.success("用户创建成功")),P(!1),T(null),R.resetFields(),H()}catch(e){s.Ay.error(U?"更新用户失败":"创建用户失败")}},_=async e=>{try{await E.Dv.delete(e),s.Ay.success("用户删除成功"),H()}catch(e){s.Ay.error("删除用户失败")}},q=async e=>{try{await E.Dv.resetProgress(e),s.Ay.success("用户进度重置成功"),H()}catch(e){s.Ay.error("重置用户进度失败")}},$=async e=>{try{let t=await E.Dv.getStats(e.id);M(t),N(!0)}catch(e){s.Ay.error("获取用户统计失败")}},G=async e=>{try{await E.Dv.update(e.id,{isVip:!e.isVip}),s.Ay.success("".concat(e.isVip?"取消":"设置","VIP成功")),H()}catch(e){s.Ay.error("VIP状态更新失败")}},J=e=>{T(e),R.setFieldsValue({phone:e.phone,openid:e.openid,nickname:e.nickname,avatarUrl:e.avatarUrl,unlockedLevels:e.unlockedLevels,isVip:e.isVip,dailyUnlockLimit:e.dailyUnlockLimit}),P(!0)},K=[{title:"用户信息",key:"user",width:250,render:(e,t)=>(0,n.jsxs)(r.A,{children:[(0,n.jsx)(c.A,{src:t.avatarUrl,icon:(0,n.jsx)(k.A,{})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{children:t.nickname||"未设置昵称"}),(0,n.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["ID: ",t.id]}),(0,n.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["手机: ",t.phone]}),t.openid&&(0,n.jsxs)("div",{style:{fontSize:"12px",color:"#999"},children:["OpenID: ",t.openid.substring(0,8),"..."]}),t.isVip&&(0,n.jsx)(o.A,{color:"gold",icon:(0,n.jsx)(w.A,{}),style:{marginTop:4},children:"VIP"})]})]})},{title:"游戏进度",key:"progress",width:150,render:(e,t)=>(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{children:["已开启: ",t.unlockedLevels," 关"]}),(0,n.jsxs)("div",{children:["已通关: ",t.completedLevelIds.length," 关"]})]})},{title:"游戏统计",key:"stats",width:150,render:(e,t)=>(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{children:["总游戏: ",t.totalGames," 次"]}),(0,n.jsxs)("div",{children:["总通关: ",t.totalCompletions," 次"]})]})},{title:"每日解锁",key:"dailyUnlock",width:150,render:(e,t)=>(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{children:["今日: ",t.dailyUnlockCount||0,"/",t.dailyUnlockLimit||15,t.isVip&&(0,n.jsx)(o.A,{color:"gold",style:{marginLeft:4,fontSize:"12px"},children:"无限制"})]}),(0,n.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["分享: ",t.totalShares||0," 次"]}),t.dailyShared&&(0,n.jsx)(o.A,{color:"green",style:{fontSize:"12px"},children:"今日已分享"})]})},{title:"最后游戏",dataIndex:"lastPlayTime",key:"lastPlayTime",width:180},{title:"注册时间",dataIndex:"createdAt",key:"createdAt",width:180},{title:"操作",key:"action",width:200,render:(e,t)=>(0,n.jsxs)(r.A,{size:"small",wrap:!0,children:[(0,n.jsx)(d.Ay,{type:"link",size:"small",icon:(0,n.jsx)(b.A,{}),onClick:()=>$(t),children:"统计"}),(0,n.jsx)(p.A,{title:t.isVip?"取消VIP":"设为VIP",children:(0,n.jsx)(d.Ay,{type:"link",size:"small",icon:(0,n.jsx)(w.A,{}),style:{color:t.isVip?"#faad14":"#d9d9d9"},onClick:()=>G(t),children:"VIP"})}),(0,n.jsx)(d.Ay,{type:"link",size:"small",icon:(0,n.jsx)(S.A,{}),onClick:()=>J(t),children:"编辑"}),(0,n.jsx)(h.A,{title:"确定要重置用户进度吗？",onConfirm:()=>q(t.id),okText:"确定",cancelText:"取消",children:(0,n.jsx)(d.Ay,{type:"link",size:"small",icon:(0,n.jsx)(O.A,{}),children:"重置"})}),(0,n.jsx)(h.A,{title:"确定要删除这个用户吗？",onConfirm:()=>_(t.id),okText:"确定",cancelText:"取消",children:(0,n.jsx)(d.Ay,{type:"link",danger:!0,size:"small",icon:(0,n.jsx)(I.A,{}),children:"删除"})})]})}];return(0,n.jsxs)("div",{children:[(0,n.jsxs)(u.A,{children:[(0,n.jsxs)("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,n.jsx)("h2",{children:"用户管理"}),(0,n.jsx)(d.Ay,{type:"primary",icon:(0,n.jsx)(C.A,{}),onClick:()=>{T(null),R.resetFields(),P(!0)},children:"创建用户"})]}),(0,n.jsx)(m.A,{columns:K,dataSource:e,rowKey:"id",loading:l,pagination:{total:e.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"共 ".concat(e," 条记录")}})]}),(0,n.jsx)(x.A,{title:U?"编辑用户":"创建用户",open:L,onCancel:()=>{P(!1),T(null),R.resetFields()},footer:null,width:500,children:(0,n.jsxs)(i.A,{form:R,layout:"vertical",onFinish:B,children:[!U&&(0,n.jsx)(i.A.Item,{name:"phone",label:"手机号",rules:[{required:!0,message:"请输入手机号"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号码"}],children:(0,n.jsx)(y.A,{placeholder:"请输入手机号"})}),U&&(0,n.jsx)(i.A.Item,{name:"phone",label:"手机号",rules:[{required:!0,message:"请输入手机号"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号码"}],children:(0,n.jsx)(y.A,{placeholder:"请输入手机号"})}),(0,n.jsx)(i.A.Item,{name:"openid",label:"微信OpenID",children:(0,n.jsx)(y.A,{placeholder:"请输入微信用户OpenID（可选）"})}),(0,n.jsx)(i.A.Item,{name:"nickname",label:"昵称",children:(0,n.jsx)(y.A,{placeholder:"请输入用户昵称"})}),(0,n.jsx)(i.A.Item,{name:"avatarUrl",label:"头像URL",children:(0,n.jsx)(y.A,{placeholder:"请输入头像URL"})}),U&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.A.Item,{name:"unlockedLevels",label:"已开启关卡数",children:(0,n.jsx)(j.A,{min:1,max:1e3,placeholder:"已开启关卡数",style:{width:"100%"}})}),(0,n.jsx)(i.A.Item,{name:"isVip",label:"VIP状态",valuePropName:"checked",children:(0,n.jsx)(A.A,{checkedChildren:(0,n.jsx)(w.A,{}),unCheckedChildren:"普通"})}),(0,n.jsx)(i.A.Item,{name:"dailyUnlockLimit",label:"每日解锁限制",tooltip:"VIP用户无限制，普通用户默认15次",children:(0,n.jsx)(j.A,{min:1,max:999,placeholder:"每日解锁限制次数",style:{width:"100%"},addonAfter:"次/天"})})]}),(0,n.jsx)(i.A.Item,{children:(0,n.jsxs)(r.A,{children:[(0,n.jsx)(d.Ay,{type:"primary",htmlType:"submit",children:U?"更新":"创建"}),(0,n.jsx)(d.Ay,{onClick:()=>{P(!1),T(null),R.resetFields()},children:"取消"})]})})]})}),(0,n.jsx)(x.A,{title:"用户游戏统计",open:D,onCancel:()=>N(!1),footer:[(0,n.jsx)(d.Ay,{onClick:()=>N(!1),children:"关闭"},"close")],width:600,children:F&&(0,n.jsxs)("div",{children:[(0,n.jsxs)(f.A,{gutter:16,style:{marginBottom:24},children:[(0,n.jsx)(v.A,{span:12,children:(0,n.jsx)(g.A,{title:"总游戏次数",value:F.totalGames})}),(0,n.jsx)(v.A,{span:12,children:(0,n.jsx)(g.A,{title:"总通关次数",value:F.totalCompletions})}),(0,n.jsx)(v.A,{span:12,children:(0,n.jsx)(g.A,{title:"已解锁关卡",value:F.unlockedLevels})}),(0,n.jsx)(v.A,{span:12,children:(0,n.jsx)(g.A,{title:"已完成关卡",value:F.completedLevels})}),(0,n.jsx)(v.A,{span:24,children:(0,n.jsx)(g.A,{title:"通关率",value:F.completionRate,precision:2,suffix:"%"})})]}),(0,n.jsx)(u.A,{title:"每日解锁统计",size:"small",children:(0,n.jsxs)(f.A,{gutter:16,children:[(0,n.jsx)(v.A,{span:8,children:(0,n.jsx)(g.A,{title:"今日解锁",value:F.dailyUnlockCount,suffix:"/ ".concat(F.isVip?"∞":F.dailyUnlockLimit)})}),(0,n.jsx)(v.A,{span:8,children:(0,n.jsx)(g.A,{title:"剩余次数",value:F.isVip?"∞":F.remainingUnlocks})}),(0,n.jsx)(v.A,{span:8,children:(0,n.jsx)(g.A,{title:"总分享次数",value:F.totalShares})}),(0,n.jsx)(v.A,{span:24,style:{marginTop:16},children:F.isVip?(0,n.jsx)(o.A,{color:"gold",icon:(0,n.jsx)(w.A,{}),style:{fontSize:"14px",padding:"4px 8px"},children:"VIP用户 - 无限制解锁"}):(0,n.jsxs)(o.A,{color:"blue",icon:(0,n.jsx)(z.A,{}),style:{fontSize:"14px",padding:"4px 8px"},children:["普通用户 - 每日",F.dailyUnlockLimit,"次解锁"]})})]})})]})})]})}},79659:(e,t,l)=>{"use strict";l.d(t,{A:()=>r});var n=l(79630),a=l(12115);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var s=l(62764);let r=a.forwardRef(function(e,t){return a.createElement(s.A,(0,n.A)({},e,{ref:t,icon:i}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[8340,547,7469,7497,44,6312,778,4670,2343,3726,4124,7238,545,8436,9179,8441,1684,7358],()=>t(5983)),_N_E=e.O()}]);