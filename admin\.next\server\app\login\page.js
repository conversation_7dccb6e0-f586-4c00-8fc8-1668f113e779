(()=>{var e={};e.id=520,e.ids=[520],e.modules={19:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),o=r(48088),i=r(88170),n=r.n(i),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94934)),"D:\\web\\other\\yyddp\\admin\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\other\\yyddp\\admin\\src\\app\\login\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4519:e=>{e.exports={container:"LoginPage_container__uSVwT"}},10535:(e,t,r)=>{"use strict";r.d(t,{y:()=>o});var s=r(98501);let o={login:async e=>(await s.F.post("/api/v1/auth/login",e)).data,logout:()=>{localStorage.removeItem("admin_token"),window.location.href="/login"},getToken:()=>localStorage.getItem("admin_token"),setToken:e=>{localStorage.setItem("admin_token",e)},isLoggedIn:()=>!!localStorage.getItem("admin_token")};o.login},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11235:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},11786:(e,t,r)=>{Promise.resolve().then(r.bind(r,91733))},12412:e=>{"use strict";e.exports=require("assert")},16603:(e,t,r)=>{Promise.resolve().then(r.bind(r,65266)),Promise.resolve().then(r.t.bind(r,28087,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35692:()=>{},53395:(e,t,r)=>{Promise.resolve().then(r.bind(r,6468)),Promise.resolve().then(r.bind(r,43741))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},76891:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91733:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(60687),o=r(43210),i=r(35899),n=r(11585),a=r(99053),l=r(28173),d=r(94733),c=r(21411),p=r(71103),u=r(80828);let m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"};var h=r(21898),v=o.forwardRef(function(e,t){return o.createElement(h.A,(0,u.A)({},e,{ref:t,icon:m}))}),x=r(16189),g=r(10535),b=r(4519),y=r.n(b);let f=()=>{let e=(0,x.useRouter)(),[t,r]=(0,o.useState)(!1),u=async t=>{r(!0);try{let r=await g.y.login(t);r.accessToken?(i.Ay.success("登录成功！"),g.y.setToken(r.accessToken),e.push("/dashboard")):i.Ay.error(r.message||"登录失败，请检查用户名或密码！")}catch(e){console.error("登录失败:",e)}finally{r(!1)}};return(0,s.jsx)("div",{className:y().container,children:(0,s.jsx)(n.A,{title:(0,s.jsx)(a.A.Title,{level:3,style:{textAlign:"center",marginBottom:0},children:"后台登录"}),style:{width:400},children:(0,s.jsxs)(l.A,{name:"admin_login",initialValues:{remember:!0},onFinish:u,children:[(0,s.jsx)(l.A.Item,{name:"username",rules:[{required:!0,message:"请输入用户名!"}],children:(0,s.jsx)(d.A,{prefix:(0,s.jsx)(p.A,{}),placeholder:"用户名 (例如: admin)"})}),(0,s.jsx)(l.A.Item,{name:"password",rules:[{required:!0,message:"请输入密码!"}],children:(0,s.jsx)(d.A.Password,{prefix:(0,s.jsx)(v,{}),placeholder:"密码 (例如: password123)"})}),(0,s.jsx)(l.A.Item,{children:(0,s.jsx)(c.Ay,{type:"primary",htmlType:"submit",style:{width:"100%"},loading:t,children:"登录"})})]})})})}},93634:(e,t,r)=>{Promise.resolve().then(r.bind(r,94934))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413);r(61120);var o=r(68016);r(35692),r(28087);let i=({children:e})=>(0,s.jsxs)("html",{lang:"en",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("meta",{charSet:"utf-8"}),(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,s.jsx)("meta",{name:"description",content:"游戏后台管理系统"}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,s.jsx)("title",{children:"游戏管理后台"})]}),(0,s.jsx)("body",{children:(0,s.jsx)(o.Z,{children:e})})]})},94735:e=>{"use strict";e.exports=require("events")},94934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\login\\page.tsx","default")},98501:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var s=r(51060),o=r(35899);let i={BASE_URL:"http://localhost:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return`${this.BASE_URL}${this.API_PREFIX}`}},n=s.A.create({baseURL:i.BASE_URL,timeout:i.TIMEOUT,headers:{"Content-Type":"application/json"}});n.interceptors.request.use(e=>{let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),n.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){let{status:t,data:r}=e.response;switch(t){case 401:o.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:o.Ay.error("没有权限访问该资源");break;case 404:o.Ay.error("请求的资源不存在");break;case 500:o.Ay.error("服务器内部错误");break;default:o.Ay.error(r?.message||"请求失败")}}else e.request?o.Ay.error("网络连接失败，请检查网络"):o.Ay.error("请求配置错误");return Promise.reject(e)});let a={get:(e,t)=>n.get(e,t),post:(e,t,r)=>n.post(e,t,r),put:(e,t,r)=>n.put(e,t,r),patch:(e,t,r)=>n.patch(e,t,r),delete:(e,t)=>n.delete(e,t)}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,433,658,816,331,173],()=>r(19));module.exports=s})();