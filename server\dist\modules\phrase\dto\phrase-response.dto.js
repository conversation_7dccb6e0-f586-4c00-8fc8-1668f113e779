"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhraseResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class PhraseResponseDto {
    id;
    text;
    meaning;
    exampleSentence;
    tags;
    createdAt;
    updatedAt;
}
exports.PhraseResponseDto = PhraseResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词组的唯一ID', example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], PhraseResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词组文本', example: 'Hello World' }),
    __metadata("design:type", String)
], PhraseResponseDto.prototype, "text", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词组含义', example: '你好，世界' }),
    __metadata("design:type", String)
], PhraseResponseDto.prototype, "meaning", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '例句',
        example: 'When you start programming, the first thing you often do is print "Hello World".',
        required: false,
    }),
    __metadata("design:type", String)
], PhraseResponseDto.prototype, "exampleSentence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签', example: ['greeting', 'common'], required: false, type: [String] }),
    __metadata("design:type", Array)
], PhraseResponseDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2023-10-27 10:30:00' }),
    __metadata("design:type", String)
], PhraseResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新时间', example: '2023-10-27 10:35:00' }),
    __metadata("design:type", String)
], PhraseResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=phrase-response.dto.js.map