(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8739],{45233:(e,i,t)=>{"use strict";t.r(i),t.d(i,{default:()=>P});var s=t(95155),r=t(12115),l=t(44670),n=t(19868),c=t(37974),d=t(12320),a=t(30662),o=t(27212),x=t(19361),h=t(74947),A=t(505),p=t(44297),j=t(51087),m=t(32814),u=t(56020),y=t(10642),g=t(13324),v=t(97550),f=t(79659),I=t(56170),k=t(34095),w=t(46996),b=t(34140),S=t(49179);let P=()=>{let[e,i]=(0,r.useState)([]),[t,P]=(0,r.useState)(!1),[V,z]=(0,r.useState)(!1),[C,O]=(0,r.useState)(null),[_]=l.A.useForm(),F=async()=>{P(!0);try{let e=await S.HK.getList();i(e||[])}catch(e){n.Ay.error("获取VIP套餐列表失败")}finally{P(!1)}};(0,r.useEffect)(()=>{F()},[]);let B=async e=>{try{C?(await S.HK.update(C.id,e),n.Ay.success("VIP套餐更新成功")):(await S.HK.create(e),n.Ay.success("VIP套餐创建成功")),z(!1),O(null),_.resetFields(),F()}catch(e){n.Ay.error(C?"更新VIP套餐失败":"创建VIP套餐失败")}},K=async e=>{try{await S.HK.delete(e),n.Ay.success("VIP套餐删除成功"),F()}catch(e){n.Ay.error("删除VIP套餐失败")}},q=async e=>{try{await S.HK.toggleStatus(e.id,!e.isActive),n.Ay.success("套餐".concat(e.isActive?"禁用":"启用","成功")),F()}catch(e){n.Ay.error("更新套餐状态失败")}},H=e=>{O(e),_.setFieldsValue({name:e.name,description:e.description,price:e.price/100,duration:e.duration,sortOrder:e.sortOrder,isActive:e.isActive}),z(!0)},R=[{title:"套餐信息",key:"info",width:250,render:(e,i)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{style:{fontWeight:"bold",fontSize:"16px",marginBottom:4},children:[(0,s.jsx)(v.A,{style:{color:"#faad14",marginRight:8}}),i.name]}),(0,s.jsx)("div",{style:{color:"#666",fontSize:"12px",marginBottom:4},children:i.description}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#999"},children:["ID: ",i.id]})]})},{title:"价格",key:"price",width:120,render:(e,i)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{style:{fontSize:"18px",fontWeight:"bold",color:"#f50"},children:["\xa5",(i.price/100).toFixed(2)]}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:[i.duration,"天"]})]})},{title:"排序",dataIndex:"sortOrder",key:"sortOrder",width:80,sorter:(e,i)=>e.sortOrder-i.sortOrder},{title:"状态",key:"status",width:100,render:(e,i)=>(0,s.jsx)(c.A,{color:i.isActive?"green":"red",children:i.isActive?"启用":"禁用"})},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:180,render:e=>new Date(e).toLocaleString()},{title:"操作",key:"action",width:200,render:(e,i)=>(0,s.jsxs)(d.A,{size:"small",wrap:!0,children:[(0,s.jsx)(a.Ay,{type:"link",size:"small",icon:(0,s.jsx)(f.A,{}),onClick:()=>H(i),children:"编辑"}),(0,s.jsx)(a.Ay,{type:"link",size:"small",onClick:()=>q(i),style:{color:i.isActive?"#ff4d4f":"#52c41a"},children:i.isActive?"禁用":"启用"}),(0,s.jsx)(o.A,{title:"确定要删除这个VIP套餐吗？",description:"删除后无法恢复，请谨慎操作。",onConfirm:()=>K(i.id),okText:"确定",cancelText:"取消",children:(0,s.jsx)(a.Ay,{type:"link",size:"small",danger:!0,icon:(0,s.jsx)(I.A,{}),children:"删除"})})]})}],T={total:e.length,active:e.filter(e=>e.isActive).length,inactive:e.filter(e=>!e.isActive).length,totalRevenue:e.reduce((e,i)=>e+i.price,0)/100};return(0,s.jsxs)("div",{style:{padding:"24px"},children:[(0,s.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,s.jsxs)("h1",{style:{fontSize:"24px",fontWeight:"bold",margin:0},children:[(0,s.jsx)(v.A,{style:{color:"#faad14",marginRight:"8px"}}),"VIP套餐管理"]}),(0,s.jsx)("p",{style:{color:"#666",margin:"8px 0 0 0"},children:"管理VIP会员套餐，设置价格、时长和状态"})]}),(0,s.jsxs)(x.A,{gutter:16,style:{marginBottom:"24px"},children:[(0,s.jsx)(h.A,{span:6,children:(0,s.jsx)(A.A,{children:(0,s.jsx)(p.A,{title:"总套餐数",value:T.total,prefix:(0,s.jsx)(k.A,{})})})}),(0,s.jsx)(h.A,{span:6,children:(0,s.jsx)(A.A,{children:(0,s.jsx)(p.A,{title:"启用套餐",value:T.active,valueStyle:{color:"#3f8600"},prefix:(0,s.jsx)(v.A,{})})})}),(0,s.jsx)(h.A,{span:6,children:(0,s.jsx)(A.A,{children:(0,s.jsx)(p.A,{title:"禁用套餐",value:T.inactive,valueStyle:{color:"#cf1322"}})})}),(0,s.jsx)(h.A,{span:6,children:(0,s.jsx)(A.A,{children:(0,s.jsx)(p.A,{title:"套餐总价值",value:T.totalRevenue,precision:2,prefix:"\xa5",valueStyle:{color:"#faad14"}})})})]}),(0,s.jsx)("div",{style:{marginBottom:"16px"},children:(0,s.jsxs)(d.A,{children:[(0,s.jsx)(a.Ay,{type:"primary",icon:(0,s.jsx)(w.A,{}),onClick:()=>{O(null),_.resetFields(),_.setFieldsValue({sortOrder:e.length+1,isActive:!0}),z(!0)},children:"新增套餐"}),(0,s.jsx)(a.Ay,{icon:(0,s.jsx)(b.A,{}),onClick:F,loading:t,children:"刷新"})]})}),(0,s.jsx)(j.A,{columns:R,dataSource:e,rowKey:"id",loading:t,pagination:{total:e.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"共 ".concat(e," 个套餐")},scroll:{x:1e3}}),(0,s.jsx)(m.A,{title:C?"编辑VIP套餐":"新增VIP套餐",open:V,onCancel:()=>{z(!1),O(null),_.resetFields()},footer:null,width:600,children:(0,s.jsxs)(l.A,{form:_,layout:"vertical",onFinish:e=>{B({...e,price:Math.round(100*e.price)})},children:[!C&&(0,s.jsxs)("div",{style:{background:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"6px",padding:"12px",marginBottom:"16px",fontSize:"14px",color:"#52c41a"},children:[(0,s.jsx)("strong",{children:"提示："}),"套餐ID将根据套餐名称和时长自动生成，格式如：vip_monthly_30d_a1b2"]}),(0,s.jsx)(l.A.Item,{name:"name",label:"套餐名称",rules:[{required:!0,message:"请输入套餐名称"}],children:(0,s.jsx)(u.A,{placeholder:"例如：VIP月卡"})}),(0,s.jsx)(l.A.Item,{name:"description",label:"套餐描述",rules:[{required:!0,message:"请输入套餐描述"}],children:(0,s.jsx)(u.A.TextArea,{rows:3,placeholder:"例如：30天VIP特权，无限制解锁关卡"})}),(0,s.jsxs)(x.A,{gutter:16,children:[(0,s.jsx)(h.A,{span:12,children:(0,s.jsx)(l.A.Item,{name:"price",label:"价格（元）",rules:[{required:!0,message:"请输入价格"},{type:"number",min:.01,message:"价格必须大于0"}],children:(0,s.jsx)(y.A,{style:{width:"100%"},placeholder:"29.00",precision:2,min:.01,max:9999.99})})}),(0,s.jsx)(h.A,{span:12,children:(0,s.jsx)(l.A.Item,{name:"duration",label:"有效期（天）",rules:[{required:!0,message:"请输入有效期"},{type:"number",min:1,message:"有效期必须大于0"}],children:(0,s.jsx)(y.A,{style:{width:"100%"},placeholder:"30",min:1,max:9999999})})})]}),(0,s.jsxs)(x.A,{gutter:16,children:[(0,s.jsx)(h.A,{span:12,children:(0,s.jsx)(l.A.Item,{name:"sortOrder",label:"排序权重",rules:[{required:!0,message:"请输入排序权重"}],children:(0,s.jsx)(y.A,{style:{width:"100%"},placeholder:"1",min:1,max:999})})}),(0,s.jsx)(h.A,{span:12,children:(0,s.jsx)(l.A.Item,{name:"isActive",label:"启用状态",valuePropName:"checked",children:(0,s.jsx)(g.A,{checkedChildren:"启用",unCheckedChildren:"禁用"})})})]}),(0,s.jsx)(l.A.Item,{style:{marginBottom:0,textAlign:"right"},children:(0,s.jsxs)(d.A,{children:[(0,s.jsx)(a.Ay,{onClick:()=>z(!1),children:"取消"}),(0,s.jsx)(a.Ay,{type:"primary",htmlType:"submit",children:C?"更新":"创建"})]})})]})})]})}},81216:(e,i,t)=>{Promise.resolve().then(t.bind(t,45233))}},e=>{var i=i=>e(e.s=i);e.O(0,[8340,547,7469,7497,44,6312,778,4670,2343,3726,4124,7238,545,8063,9179,8441,1684,7358],()=>i(81216)),_N_E=e.O()}]);