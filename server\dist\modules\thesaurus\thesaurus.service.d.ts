import { Model } from 'mongoose';
import { CreateThesaurusDto } from './dto/create-thesaurus.dto';
import { UpdateThesaurusDto } from './dto/update-thesaurus.dto';
import { ThesaurusDocument } from './entities/thesaurus.entity';
import { ThesaurusResponseDto } from './dto/thesaurus-response.dto';
import { PhraseService } from '../phrase/phrase.service';
export declare class ThesaurusService {
    private thesaurusModel;
    private readonly phraseService;
    constructor(thesaurusModel: Model<ThesaurusDocument>, phraseService: PhraseService);
    private _mapToResponseDto;
    getThesaurusEntity(id: string): Promise<ThesaurusDocument>;
    create(createThesaurusDto: CreateThesaurusDto): Promise<ThesaurusResponseDto>;
    findAll(): Promise<ThesaurusResponseDto[]>;
    findOne(id: string): Promise<ThesaurusResponseDto>;
    update(id: string, updateThesaurusDto: UpdateThesaurusDto): Promise<ThesaurusResponseDto>;
    remove(id: string): Promise<void>;
    addPhrase(thesaurusId: string, phraseId: string): Promise<ThesaurusResponseDto>;
    removePhrase(thesaurusId: string, phraseId: string): Promise<ThesaurusResponseDto>;
    isPhraseInAnyOfThesauruses(phraseId: string, thesaurusIds: string[]): Promise<boolean>;
}
