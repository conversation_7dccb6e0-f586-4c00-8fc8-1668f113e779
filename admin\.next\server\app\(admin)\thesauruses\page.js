(()=>{var e={};e.id=496,e.ids=[496],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11235:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},12412:e=>{"use strict";e.exports=require("assert")},13605:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);function a(e,t){let r=(0,s.useRef)([]);return()=>{r.current.push(setTimeout(()=>{var t,r,s,a;(null==(t=e.current)?void 0:t.input)&&(null==(r=e.current)?void 0:r.input.getAttribute("type"))==="password"&&(null==(s=e.current)?void 0:s.input.hasAttribute("value"))&&(null==(a=e.current)||a.input.removeAttribute("value"))}))}}},16603:(e,t,r)=>{Promise.resolve().then(r.bind(r,65266)),Promise.resolve().then(r.t.bind(r,28087,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24600:(e,t,r)=>{Promise.resolve().then(r.bind(r,10814))},25227:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["(admin)",{children:["thesauruses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,57470)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(admin)/thesauruses/page",pathname:"/thesauruses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},25901:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(60687),a=r(43210),n=r(35899),i=r(7872),o=r(48111),l=r(21411),d=r(11585),c=r(99053),u=r(27783),p=r(80828);let h={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"};var m=r(21898),x=a.forwardRef(function(e,t){return a.createElement(m.A,(0,p.A)({},e,{ref:t,icon:h}))}),f=r(53082),y=r(16189),b=r(52931);let v=()=>{let[e,t]=(0,a.useState)([]),[r,p]=(0,a.useState)(!1),h=(0,y.useRouter)(),m=(0,a.useCallback)(async()=>{p(!0);try{let e=await (0,b.zN)();t(e)}catch(e){console.error("获取词库列表失败:",e),n.Ay.error(e.message||"获取词库列表失败！")}finally{p(!1)}},[]);(0,a.useEffect)(()=>{m()},[m]);let v=(0,a.useCallback)(async e=>{try{await (0,b.Io)(e),n.Ay.success("词库删除成功！"),m()}catch(t){console.error(`删除词库 ${e} 失败:`,t),n.Ay.error(t.message||"词库删除失败！")}},[m]),g=(0,a.useCallback)(e=>{i.A.confirm({title:`确定要删除词库 "${e.name}" 吗?`,icon:(0,s.jsx)(x,{}),content:"此操作不可撤销。如果有关联的关卡，请谨慎操作。",okText:"删除",okType:"danger",cancelText:"取消",onOk(){v(e.id)}})},[v]),A=[{title:"名称",dataIndex:"name",key:"name"},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0},{title:"词组数",dataIndex:"phraseIds",key:"phraseCount",render:e=>e?.length||0},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",render:e=>new Date(e).toLocaleString()},{title:"更新时间",dataIndex:"updatedAt",key:"updatedAt",render:e=>new Date(e).toLocaleString()},{title:"操作",key:"action",render:(e,t)=>(0,s.jsxs)(o.A,{size:"middle",children:[(0,s.jsx)(l.Ay,{size:"small",onClick:()=>{n.Ay.info(`编辑词库 ${t.id} (功能待实现)`)},children:"编辑"}),(0,s.jsx)(l.Ay,{size:"small",danger:!0,onClick:()=>g(t),children:"删除"})]})}];return(0,s.jsxs)(d.A,{children:[(0,s.jsx)(c.A.Title,{level:2,children:"词库管理"}),(0,s.jsx)(l.Ay,{type:"primary",icon:(0,s.jsx)(f.A,{}),onClick:()=>h.push("/thesauruses/create"),style:{marginBottom:16},children:"创建词库"}),(0,s.jsx)(u.A,{columns:A,dataSource:e,rowKey:"id",loading:r})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30421:(e,t,r)=>{Promise.resolve().then(r.bind(r,57470))},33873:e=>{"use strict";e.exports=require("path")},35692:()=>{},37912:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var s=r(60687),a=r(43210),n=r(85814),i=r.n(n),o=r(16189),l=r(98836),d=r(99053),c=r(63736),u=r(56072),p=r(78620),h=r(60203),m=r(53788),x=r(28859),f=r(81945),y=r(3788),b=r(73237),v=r(94858),g=r(47453),A=r(72061),j=r(80461),w=r(71103);let{Header:P,Content:k,Sider:C,Footer:$}=l.A,I=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,s.jsx)(h.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,s.jsx)(m.A,{})},{key:"phrases",label:"词组管理",path:"/phrases",icon:(0,s.jsx)(x.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,s.jsx)(f.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,s.jsx)(y.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,s.jsx)(b.A,{})},{key:"vip-users",label:"VIP用户管理",path:"/vip-users",icon:(0,s.jsx)(v.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,s.jsx)(g.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,s.jsx)(A.A,{})}];function _({children:e}){let t=(0,o.useRouter)(),r=(0,o.usePathname)(),[n,h]=(0,a.useState)(!1),m=[{key:"logout",icon:(0,s.jsx)(j.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),t.push("/login")}}],x=I.find(e=>r.startsWith(e.path))?.key||"dashboard";return(0,s.jsxs)(l.A,{style:{minHeight:"100vh"},children:[(0,s.jsxs)(C,{collapsible:!0,collapsed:n,onCollapse:e=>h(e),children:[(0,s.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,s.jsx)(d.A.Text,{style:{color:"white",fontSize:n?"10px":"16px",transition:"font-size 0.2s"},children:n?"后台":"游戏管理后台"})}),(0,s.jsx)(c.A,{theme:"dark",selectedKeys:[x],mode:"inline",items:I.map(e=>({key:e.key,icon:e.icon,label:(0,s.jsx)(i(),{href:e.path,children:e.label})}))})]}),(0,s.jsxs)(l.A,{children:[(0,s.jsxs)(P,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,s.jsx)(u.A,{menu:{items:m},placement:"bottomRight",children:(0,s.jsx)(p.A,{style:{cursor:"pointer"},icon:(0,s.jsx)(w.A,{})})}),(0,s.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,s.jsx)(k,{style:{margin:"16px"},children:e}),(0,s.jsxs)($,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},52931:(e,t,r)=>{"use strict";r.d(t,{Au:()=>a,Io:()=>i,LQ:()=>n,zN:()=>o});var s=r(98501);let a={getAll:async()=>(await s.F.get("/api/v1/thesauruses")).data,getById:async e=>(await s.F.get(`/api/v1/thesauruses/${e}`)).data,create:async e=>(await s.F.post("/api/v1/thesauruses",e)).data,update:async(e,t)=>(await s.F.patch(`/api/v1/thesauruses/${e}`,t)).data,delete:async e=>{await s.F.delete(`/api/v1/thesauruses/${e}`)},addPhrase:async(e,t)=>(await s.F.post(`/api/v1/thesauruses/${e}/phrases`,t)).data,removePhrase:async(e,t)=>(await s.F.delete(`/api/v1/thesauruses/${e}/phrases/${t}`)).data},n=a.create;a.update,a.delete;let i=a.delete;a.getById,a.getAll;let o=a.getAll},53395:(e,t,r)=>{Promise.resolve().then(r.bind(r,6468)),Promise.resolve().then(r.bind(r,43741))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57470:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\thesauruses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\page.tsx","default")},59389:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(80828),a=r(43210);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"}}]},name:"search",theme:"outlined"};var i=r(21898);let o=a.forwardRef(function(e,t){return a.createElement(i.A,(0,s.A)({},e,{ref:t,icon:n}))})},59448:(e,t,r)=>{Promise.resolve().then(r.bind(r,37912))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72277:(e,t,r)=>{Promise.resolve().then(r.bind(r,25901))},74075:e=>{"use strict";e.exports=require("zlib")},76891:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},79551:e=>{"use strict";e.exports=require("url")},81441:(e,t,r)=>{"use strict";r.d(t,{A:()=>j});var s=r(43210),a=r.n(s),n=r(69662),i=r.n(n),o=r(65610),l=r(7224),d=r(62028),c=r(47994),u=r(65539),p=r(71802),h=r(57026),m=r(59897),x=r(40908),f=r(38770),y=r(11503),b=r(72202),v=r(13605),g=r(18599),A=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)0>t.indexOf(s[a])&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(r[s[a]]=e[s[a]]);return r};let j=(0,s.forwardRef)((e,t)=>{let{prefixCls:r,bordered:n=!0,status:j,size:w,disabled:P,onBlur:k,onFocus:C,suffix:$,allowClear:I,addonAfter:_,addonBefore:q,className:z,style:E,styles:O,rootClassName:R,onChange:S,classNames:F,variant:B}=e,L=A(e,["prefixCls","bordered","status","size","disabled","onBlur","onFocus","suffix","allowClear","addonAfter","addonBefore","className","style","styles","rootClassName","onChange","classNames","variant"]),{getPrefixCls:T,direction:D,allowClear:M,autoComplete:N,className:U,style:G,classNames:K,styles:W}=(0,p.TP)("input"),V=T("input",r),X=(0,s.useRef)(null),Q=(0,m.A)(V),[H,Y,Z]=(0,g.MG)(V,R),[J]=(0,g.Ay)(V,Q),{compactSize:ee,compactItemClassnames:et}=(0,b.RQ)(V,D),er=(0,x.A)(e=>{var t;return null!=(t=null!=w?w:ee)?t:e}),es=a().useContext(h.A),{status:ea,hasFeedback:en,feedbackIcon:ei}=(0,s.useContext)(f.$W),eo=(0,u.v)(ea,j),el=function(e){return!!(e.prefix||e.suffix||e.allowClear||e.showCount)}(e)||!!en;(0,s.useRef)(el);let ed=(0,v.A)(X,!0),ec=(en||$)&&a().createElement(a().Fragment,null,$,en&&ei),eu=(0,c.A)(null!=I?I:M),[ep,eh]=(0,y.A)("input",B,n);return H(J(a().createElement(o.A,Object.assign({ref:(0,l.K4)(t,X),prefixCls:V,autoComplete:N},L,{disabled:null!=P?P:es,onBlur:e=>{ed(),null==k||k(e)},onFocus:e=>{ed(),null==C||C(e)},style:Object.assign(Object.assign({},G),E),styles:Object.assign(Object.assign({},W),O),suffix:ec,allowClear:eu,className:i()(z,R,Z,Q,et,U),onChange:e=>{ed(),null==S||S(e)},addonBefore:q&&a().createElement(d.A,{form:!0,space:!0},q),addonAfter:_&&a().createElement(d.A,{form:!0,space:!0},_),classNames:Object.assign(Object.assign(Object.assign({},F),K),{input:i()({[`${V}-sm`]:"small"===er,[`${V}-lg`]:"large"===er,[`${V}-rtl`]:"rtl"===D},null==F?void 0:F.input,K.input,Y),variant:i()({[`${V}-${ep}`]:eh},(0,u.L)(V,eo)),affixWrapper:i()({[`${V}-affix-wrapper-sm`]:"small"===er,[`${V}-affix-wrapper-lg`]:"large"===er,[`${V}-affix-wrapper-rtl`]:"rtl"===D},Y),wrapper:i()({[`${V}-group-rtl`]:"rtl"===D},Y),groupWrapper:i()({[`${V}-group-wrapper-sm`]:"small"===er,[`${V}-group-wrapper-lg`]:"large"===er,[`${V}-group-wrapper-rtl`]:"rtl"===D,[`${V}-group-wrapper-${ep}`]:eh},(0,u.L)(`${V}-group-wrapper`,eo,en),Y)})}))))})},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(37413);r(61120);var a=r(68016);r(35692),r(28087);let n=({children:e})=>(0,s.jsxs)("html",{lang:"en",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("meta",{charSet:"utf-8"}),(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,s.jsx)("meta",{name:"description",content:"游戏后台管理系统"}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,s.jsx)("title",{children:"游戏管理后台"})]}),(0,s.jsx)("body",{children:(0,s.jsx)(a.Z,{children:e})})]})},94735:e=>{"use strict";e.exports=require("events")},98501:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var s=r(51060),a=r(35899);let n={BASE_URL:"http://localhost:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return`${this.BASE_URL}${this.API_PREFIX}`}},i=s.A.create({baseURL:n.BASE_URL,timeout:n.TIMEOUT,headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>{let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),i.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){let{status:t,data:r}=e.response;switch(t){case 401:a.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:a.Ay.error("没有权限访问该资源");break;case 404:a.Ay.error("请求的资源不存在");break;case 500:a.Ay.error("服务器内部错误");break;default:a.Ay.error(r?.message||"请求失败")}}else e.request?a.Ay.error("网络连接失败，请检查网络"):a.Ay.error("请求配置错误");return Promise.reject(e)});let o={get:(e,t)=>i.get(e,t),post:(e,t,r)=>i.post(e,t,r),put:(e,t,r)=>i.put(e,t,r),patch:(e,t,r)=>i.patch(e,t,r),delete:(e,t)=>i.delete(e,t)}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,433,658,816,204,553,84,579,542],()=>r(25227));module.exports=s})();