exports.id=451,exports.ids=[451],exports.modules={10535:(e,a,t)=>{"use strict";t.d(a,{y:()=>i});var s=t(98501);let i={login:async e=>(await s.F.post("/api/v1/auth/login",e)).data,logout:()=>{localStorage.removeItem("admin_token"),window.location.href="/login"},getToken:()=>localStorage.getItem("admin_token"),setToken:e=>{localStorage.setItem("admin_token",e)},isLoggedIn:()=>!!localStorage.getItem("admin_token")};i.login},10814:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},11235:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},16603:(e,a,t)=>{Promise.resolve().then(t.bind(t,65266)),Promise.resolve().then(t.t.bind(t,28087,23))},23870:(e,a,t)=>{"use strict";t.d(a,{k3:()=>l,Yi:()=>d,LY:()=>r,Au:()=>n.Au,Dv:()=>o,HK:()=>p,mz:()=>u});var s=t(98501),i=t(10535);let r={getAll:async()=>(await s.F.get("/api/v1/phrases")).data,getById:async e=>(await s.F.get(`/api/v1/phrases/${e}`)).data,create:async e=>(await s.F.post("/api/v1/phrases",e)).data,update:async(e,a)=>(await s.F.patch(`/api/v1/phrases/${e}`,a)).data,delete:async e=>{await s.F.delete(`/api/v1/phrases/${e}`)}};var n=t(52931);let l={getAll:async()=>(await s.F.get("/api/v1/levels")).data,getById:async e=>(await s.F.get(`/api/v1/levels/${e}`)).data,create:async e=>(await s.F.post("/api/v1/levels",e)).data,update:async(e,a)=>(await s.F.patch(`/api/v1/levels/${e}`,a)).data,delete:async e=>{await s.F.delete(`/api/v1/levels/${e}`)},addPhrase:async(e,a)=>(await s.F.post(`/api/v1/levels/${e}/phrases`,a)).data,removePhrase:async(e,a)=>(await s.F.delete(`/api/v1/levels/${e}/phrases/${a}`)).data,getCount:async()=>(await s.F.get("/api/v1/levels/count")).data,getByDifficulty:async e=>(await s.F.get(`/api/v1/levels/difficulty/${e}`)).data,getWithPhrases:async e=>(await s.F.get(`/api/v1/levels/${e}/with-phrases`)).data};l.getAll;let o={getAll:async()=>(await s.F.get("/api/v1/users")).data,getById:async e=>(await s.F.get(`/api/v1/users/${e}`)).data,getByOpenid:async e=>(await s.F.get(`/api/v1/users/by-openid?openid=${e}`)).data,getByPhone:async e=>(await s.F.get(`/api/v1/users/by-phone?phone=${e}`)).data,create:async e=>(await s.F.post("/api/v1/users",e)).data,update:async(e,a)=>(await s.F.patch(`/api/v1/users/${e}`,a)).data,delete:async e=>{await s.F.delete(`/api/v1/users/${e}`)},completeLevel:async(e,a)=>(await s.F.post(`/api/v1/users/${e}/complete-level`,a)).data,startGame:async e=>(await s.F.post(`/api/v1/users/${e}/start-game`)).data,getStats:async e=>(await s.F.get(`/api/v1/users/${e}/stats`)).data,resetProgress:async e=>(await s.F.post(`/api/v1/users/${e}/reset-progress`)).data};var c=t(43910);let p={getList:async()=>(await s.F.get("/api/v1/payment/vip-packages")).data||[],getById:async e=>(await s.F.get(`/api/v1/payment/vip-packages/${e}`)).data,create:async e=>(await s.F.post("/api/v1/payment/vip-packages",e)).data,update:async(e,a)=>(await s.F.put(`/api/v1/payment/vip-packages/${e}`,a)).data,async delete(e){await s.F.delete(`/api/v1/payment/vip-packages/${e}`)},toggleStatus:async(e,a)=>(await s.F.patch(`/api/v1/payment/vip-packages/${e}/status`,{isActive:a})).data},d={getList:async e=>(await s.F.get("/api/v1/payment/orders",{params:e})).data,getStats:async e=>(await s.F.get("/api/v1/payment/orders/stats",{params:e})).data,getById:async e=>(await s.F.get(`/api/v1/payment/orders/${e}`)).data,queryStatus:async e=>(await s.F.get(`/api/v1/payment/query/${e}`)).data,refreshStatus:async e=>(await s.F.post(`/api/v1/payment/refresh/${e}`)).data,async cancel(e,a){await s.F.post(`/payment/cancel/${e}`,{userId:a})}},u={async getList(e){let a=await s.F.get("/api/v1/users",{params:e});return{users:a.data?.users||[],total:a.data?.total||0}},getById:async e=>(await s.F.get(`/api/v1/users/${e}`)).data,updateVipStatus:async(e,a)=>(await s.F.put(`/api/v1/users/${e}`,{isVip:a,dailyUnlockLimit:a?999999:15})).data};var h=t(90134);i.y,n.Au,c.Dw,h.f},24600:(e,a,t)=>{Promise.resolve().then(t.bind(t,10814))},35692:()=>{},37912:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>S});var s=t(60687),i=t(43210),r=t(85814),n=t.n(r),l=t(16189),o=t(98836),c=t(99053),p=t(63736),d=t(56072),u=t(78620),h=t(60203),y=t(53788),g=t(28859),v=t(81945),m=t(3788),w=t(73237),b=t(94858),F=t(47453),f=t(72061),x=t(80461),A=t(71103);let{Header:k,Content:$,Sider:P,Footer:j}=o.A,I=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,s.jsx)(h.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,s.jsx)(y.A,{})},{key:"phrases",label:"词组管理",path:"/phrases",icon:(0,s.jsx)(g.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,s.jsx)(v.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,s.jsx)(m.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,s.jsx)(w.A,{})},{key:"vip-users",label:"VIP用户管理",path:"/vip-users",icon:(0,s.jsx)(b.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,s.jsx)(F.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,s.jsx)(f.A,{})}];function S({children:e}){let a=(0,l.useRouter)(),t=(0,l.usePathname)(),[r,h]=(0,i.useState)(!1),y=[{key:"logout",icon:(0,s.jsx)(x.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),a.push("/login")}}],g=I.find(e=>t.startsWith(e.path))?.key||"dashboard";return(0,s.jsxs)(o.A,{style:{minHeight:"100vh"},children:[(0,s.jsxs)(P,{collapsible:!0,collapsed:r,onCollapse:e=>h(e),children:[(0,s.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,s.jsx)(c.A.Text,{style:{color:"white",fontSize:r?"10px":"16px",transition:"font-size 0.2s"},children:r?"后台":"游戏管理后台"})}),(0,s.jsx)(p.A,{theme:"dark",selectedKeys:[g],mode:"inline",items:I.map(e=>({key:e.key,icon:e.icon,label:(0,s.jsx)(n(),{href:e.path,children:e.label})}))})]}),(0,s.jsxs)(o.A,{children:[(0,s.jsxs)(k,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,s.jsx)(d.A,{menu:{items:y},placement:"bottomRight",children:(0,s.jsx)(u.A,{style:{cursor:"pointer"},icon:(0,s.jsx)(A.A,{})})}),(0,s.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,s.jsx)($,{style:{margin:"16px"},children:e}),(0,s.jsxs)(j,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},43910:(e,a,t)=>{"use strict";t.d(a,{Dw:()=>i,WS:()=>r,cm:()=>n});var s=t(98501);class i{static async getAllShareConfigs(){return(await s.F.get("/api/v1/share")).data}static async getActiveShareConfigs(){return(await s.F.get("/api/v1/share/active")).data}static async getDefaultShareConfig(){return(await s.F.get("/api/v1/share/default")).data}static async getShareConfigByType(e){return(await s.F.get(`/api/v1/share/type/${e}`)).data}static async getShareConfigById(e){return(await s.F.get(`/api/v1/share/${e}`)).data}static async createShareConfig(e){return(await s.F.post("/api/v1/share",e)).data}static async updateShareConfig(e,a){return(await s.F.patch(`/api/v1/share/${e}`,a)).data}static async toggleShareConfig(e){return(await s.F.put(`/api/v1/share/${e}/toggle`)).data}static async deleteShareConfig(e){return(await s.F.delete(`/api/v1/share/${e}`)).data}}let r=[{value:"default",label:"默认分享",description:"通用分享，适用于首页、游戏页等"},{value:"result",label:"结果分享",description:"展示用户成绩的分享"},{value:"level",label:"关卡分享",description:"邀请好友挑战特定关卡"},{value:"achievement",label:"成就分享",description:"展示用户获得的成就"},{value:"custom",label:"自定义分享",description:"特殊活动或自定义场景"}],n=[{label:"首页",value:"/pages/index/index",description:"小程序首页"},{label:"游戏页",value:"/pages/game/game",description:"游戏主页面"},{label:"关卡页",value:"/pages/level/level?id={levelId}",description:"特定关卡页面，{levelId}会被替换为实际关卡ID"},{label:"结果页",value:"/pages/result/result?score={score}",description:"结果展示页面，{score}会被替换为实际分数"},{label:"排行榜",value:"/pages/rank/rank",description:"排行榜页面"}]},52931:(e,a,t)=>{"use strict";t.d(a,{Au:()=>i,Io:()=>n,LQ:()=>r,zN:()=>l});var s=t(98501);let i={getAll:async()=>(await s.F.get("/api/v1/thesauruses")).data,getById:async e=>(await s.F.get(`/api/v1/thesauruses/${e}`)).data,create:async e=>(await s.F.post("/api/v1/thesauruses",e)).data,update:async(e,a)=>(await s.F.patch(`/api/v1/thesauruses/${e}`,a)).data,delete:async e=>{await s.F.delete(`/api/v1/thesauruses/${e}`)},addPhrase:async(e,a)=>(await s.F.post(`/api/v1/thesauruses/${e}/phrases`,a)).data,removePhrase:async(e,a)=>(await s.F.delete(`/api/v1/thesauruses/${e}/phrases/${a}`)).data},r=i.create;i.update,i.delete;let n=i.delete;i.getById,i.getAll;let l=i.getAll},53395:(e,a,t)=>{Promise.resolve().then(t.bind(t,6468)),Promise.resolve().then(t.bind(t,43741))},59448:(e,a,t)=>{Promise.resolve().then(t.bind(t,37912))},70440:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>i});var s=t(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},76891:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},90134:(e,a,t)=>{"use strict";t.d(a,{f:()=>i});var s=t(98501);let i={getAll:async()=>(await s.F.get("/api/v1/settings")).data,getById:async e=>(await s.F.get(`/api/v1/settings/${e}`)).data,getByKey:async e=>(await s.F.get(`/api/v1/settings/key/${e}`)).data,update:async(e,a)=>(await s.F.patch(`/api/v1/settings/${e}`,a)).data,updateByKey:async(e,a)=>(await s.F.patch(`/api/v1/settings/key/${e}`,{value:a})).data,async initializeDefaults(){await s.F.post("/api/v1/settings/initialize")},async updateAppConfig(e){let a=[];void 0!==e.helpUrl&&a.push(this.updateByKey("help_url",e.helpUrl)),void 0!==e.backgroundMusicUrl&&a.push(this.updateByKey("background_music_url",e.backgroundMusicUrl)),await Promise.all(a)},async getAppConfig(){try{let[e,a]=await Promise.all([this.getByKey("help_url").catch(()=>null),this.getByKey("background_music_url").catch(()=>null)]);return{helpUrl:e?.value||"",backgroundMusicUrl:a?.value||""}}catch(e){return console.error("获取小程序配置失败:",e),{helpUrl:"",backgroundMusicUrl:""}}},async testWeixinAppSettings(){try{return(await s.F.get("/weixin/app-settings")).data}catch(e){throw console.error("测试微信小程序设置接口失败:",e),e}},async testWeixinGlobalConfig(){try{return(await s.F.get("/weixin/global-config")).data}catch(e){throw console.error("测试微信小程序全局配置接口失败:",e),e}}}},94431:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});var s=t(37413);t(61120);var i=t(68016);t(35692),t(28087);let r=({children:e})=>(0,s.jsxs)("html",{lang:"en",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("meta",{charSet:"utf-8"}),(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,s.jsx)("meta",{name:"description",content:"游戏后台管理系统"}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,s.jsx)("title",{children:"游戏管理后台"})]}),(0,s.jsx)("body",{children:(0,s.jsx)(i.Z,{children:e})})]})},98501:(e,a,t)=>{"use strict";t.d(a,{F:()=>l});var s=t(51060),i=t(35899);let r={BASE_URL:"http://localhost:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return`${this.BASE_URL}${this.API_PREFIX}`}},n=s.A.create({baseURL:r.BASE_URL,timeout:r.TIMEOUT,headers:{"Content-Type":"application/json"}});n.interceptors.request.use(e=>{let a=localStorage.getItem("admin_token");return a&&(e.headers.Authorization=`Bearer ${a}`),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),n.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){let{status:a,data:t}=e.response;switch(a){case 401:i.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:i.Ay.error("没有权限访问该资源");break;case 404:i.Ay.error("请求的资源不存在");break;case 500:i.Ay.error("服务器内部错误");break;default:i.Ay.error(t?.message||"请求失败")}}else e.request?i.Ay.error("网络连接失败，请检查网络"):i.Ay.error("请求配置错误");return Promise.reject(e)});let l={get:(e,a)=>n.get(e,a),post:(e,a,t)=>n.post(e,a,t),put:(e,a,t)=>n.put(e,a,t),patch:(e,a,t)=>n.patch(e,a,t),delete:(e,a)=>n.delete(e,a)}}};