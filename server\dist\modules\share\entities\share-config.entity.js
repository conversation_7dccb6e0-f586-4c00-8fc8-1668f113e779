"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShareConfigSchema = exports.ShareConfig = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const swagger_1 = require("@nestjs/swagger");
let ShareConfig = class ShareConfig {
    id;
    name;
    title;
    path;
    imageUrl;
    description;
    type;
    isActive;
    sortOrder;
    createdAt;
    updatedAt;
};
exports.ShareConfig = ShareConfig;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享配置的唯一ID', example: 'share-config-001' }),
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], ShareConfig.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享配置名称', example: '默认分享配置' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], ShareConfig.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享标题', example: '一起来挑战词汇游戏！' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], ShareConfig.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享路径', example: '/pages/index/index' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], ShareConfig.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享图片URL', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], ShareConfig.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享描述', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], ShareConfig.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享类型', example: 'default' }),
    (0, mongoose_1.Prop)({ required: true, default: 'default' }),
    __metadata("design:type", String)
], ShareConfig.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否启用', example: true }),
    (0, mongoose_1.Prop)({ required: true, default: true }),
    __metadata("design:type", Boolean)
], ShareConfig.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序权重', example: 1 }),
    (0, mongoose_1.Prop)({ required: true, default: 1 }),
    __metadata("design:type", Number)
], ShareConfig.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], ShareConfig.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], ShareConfig.prototype, "updatedAt", void 0);
exports.ShareConfig = ShareConfig = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], ShareConfig);
exports.ShareConfigSchema = mongoose_1.SchemaFactory.createForClass(ShareConfig);
//# sourceMappingURL=share-config.entity.js.map