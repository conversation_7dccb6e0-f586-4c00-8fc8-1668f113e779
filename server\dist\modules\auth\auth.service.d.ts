import { OnModuleInit } from '@nestjs/common';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import { LoginAdminDto } from './dto/login-admin.dto';
import { AdminUserDocument } from './entities/admin-user.entity';
export declare class AuthService implements OnModuleInit {
    private adminUserModel;
    private readonly jwtService;
    private readonly logger;
    constructor(adminUserModel: Model<AdminUserDocument>, jwtService: JwtService);
    onModuleInit(): Promise<void>;
    private initializeDefaultAdmin;
    login(loginAdminDto: LoginAdminDto): Promise<{
        message: string;
        accessToken: string;
        user: {
            userId: string;
            username: string;
            roles: string[];
        };
    }>;
    findUserByUsername(username: string): Promise<AdminUserDocument | null>;
    findUserById(userId: string): Promise<AdminUserDocument | null>;
    createAdminUser(userData: {
        userId: string;
        username: string;
        password: string;
        roles?: string[];
    }): Promise<AdminUserDocument>;
}
