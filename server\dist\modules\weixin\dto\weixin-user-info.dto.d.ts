export declare class WeixinUserInfoDto {
    id: string;
    maskedPhone?: string;
    nickname?: string;
    avatarUrl?: string;
    unlockedLevels: number;
    completedLevelIds: string[];
    totalGames: number;
    totalCompletions: number;
    lastPlayTime: string;
    createdAt: string;
}
export declare class WeixinLevelDto {
    id: string;
    name: string;
    difficulty: number;
    description?: string;
    isUnlocked: boolean;
    isCompleted: boolean;
    createdAt: string;
}
