(()=>{var e={};e.id=19,e.ids=[19],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11235:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},12412:e=>{"use strict";e.exports=require("assert")},16603:(e,t,r)=>{Promise.resolve().then(r.bind(r,65266)),Promise.resolve().then(r.t.bind(r,28087,23))},19094:(e,t,r)=>{Promise.resolve().then(r.bind(r,96591))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24600:(e,t,r)=>{Promise.resolve().then(r.bind(r,10814))},26953:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(60687),n=r(43210),i=r(16189),a=r(99053),o=r(94733),l=r(70084),c=r(28173),d=r(35899),p=r(70553),h=r(29220),u=r(21411),m=r(11585),x=r(96625),v=r(4691),g=r(48111),y=r(2535),b=r(59823),f=r(85975),j=r(3788),A=r(97103),w=r(43910);let{Title:P,Text:$}=a.A,{TextArea:k}=o.A,{Option:I}=l.A;function C(){let e=(0,i.useParams)(),t=(0,i.useRouter)(),[r,a]=(0,n.useState)(null),[C,S]=(0,n.useState)(!0),[E,z]=(0,n.useState)(!1),[q]=c.A.useForm(),R=e.id,_=()=>{t.push(`/shares/${R}`)},L=async()=>{try{let e=await q.validateFields();z(!0),await w.Dw.updateShareConfig(R,e),d.Ay.success("分享配置更新成功"),t.push(`/shares/${R}`)}catch(e){if(e&&"object"==typeof e&&"errorFields"in e)d.Ay.error("请检查表单输入");else{let t=e&&"object"==typeof e&&"message"in e?e.message:"更新失败";d.Ay.error(t)}}finally{z(!1)}};return C?(0,s.jsx)("div",{style:{padding:"24px",textAlign:"center"},children:(0,s.jsx)(p.A,{size:"large"})}):r?(0,s.jsx)("div",{style:{padding:"24px"},children:(0,s.jsxs)(m.A,{children:[(0,s.jsx)("div",{style:{marginBottom:"24px"},children:(0,s.jsxs)(x.A,{justify:"space-between",align:"middle",children:[(0,s.jsx)(v.A,{children:(0,s.jsxs)(g.A,{children:[(0,s.jsx)(u.Ay,{icon:(0,s.jsx)(f.A,{}),onClick:_,children:"返回"}),(0,s.jsxs)(P,{level:3,style:{margin:0},children:[(0,s.jsx)(j.A,{style:{marginRight:"8px"}}),"编辑分享配置"]})]})}),(0,s.jsx)(v.A,{children:(0,s.jsxs)(g.A,{children:[(0,s.jsx)(u.Ay,{onClick:_,children:"取消"}),(0,s.jsx)(u.Ay,{type:"primary",icon:(0,s.jsx)(A.A,{}),loading:E,onClick:L,children:"保存"})]})})]})}),(0,s.jsxs)(c.A,{form:q,layout:"vertical",size:"large",children:[(0,s.jsxs)(x.A,{gutter:24,children:[(0,s.jsx)(v.A,{span:12,children:(0,s.jsx)(c.A.Item,{name:"name",label:"配置名称",rules:[{required:!0,message:"请输入配置名称"}],children:(0,s.jsx)(o.A,{placeholder:"请输入配置名称"})})}),(0,s.jsx)(v.A,{span:12,children:(0,s.jsx)(c.A.Item,{name:"type",label:"分享类型",rules:[{required:!0,message:"请选择分享类型"}],children:(0,s.jsx)(l.A,{placeholder:"请选择分享类型",children:w.WS.map(e=>(0,s.jsx)(I,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:e.label}),(0,s.jsx)($,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})})})]}),(0,s.jsx)(c.A.Item,{name:"title",label:"分享标题",rules:[{required:!0,message:"请输入分享标题"}],children:(0,s.jsx)(o.A,{placeholder:"请输入分享标题"})}),(0,s.jsx)(c.A.Item,{name:"path",label:"分享路径",rules:[{required:!0,message:"请输入分享路径"}],children:(0,s.jsx)(l.A,{placeholder:"请选择或输入分享路径",mode:"tags",allowClear:!0,children:w.cm.map(e=>(0,s.jsx)(I,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:e.label}),(0,s.jsx)($,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})}),(0,s.jsx)(c.A.Item,{name:"imageUrl",label:"分享图片URL",rules:[{type:"url",message:"请输入有效的URL"}],children:(0,s.jsx)(o.A,{placeholder:"请输入分享图片URL（可选）"})}),(0,s.jsx)(c.A.Item,{name:"description",label:"分享描述",children:(0,s.jsx)(k,{placeholder:"请输入分享描述（可选）",rows:4,maxLength:200,showCount:!0})}),(0,s.jsxs)(x.A,{gutter:24,children:[(0,s.jsx)(v.A,{span:12,children:(0,s.jsx)(c.A.Item,{name:"sortOrder",label:"排序权重",rules:[{required:!0,message:"请输入排序权重"}],children:(0,s.jsx)(y.A,{min:1,max:999,placeholder:"排序权重",style:{width:"100%"}})})}),(0,s.jsx)(v.A,{span:12,children:(0,s.jsx)(c.A.Item,{name:"isActive",label:"启用状态",valuePropName:"checked",children:(0,s.jsx)(b.A,{checkedChildren:"启用",unCheckedChildren:"禁用",size:"default"})})})]})]}),(0,s.jsx)(h.A,{message:"编辑提示",description:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{children:"• 分享标题和路径是必填项，将直接影响小程序的分享效果"}),(0,s.jsx)("p",{children:"• 分享图片建议使用5:4比例，推荐尺寸500x400px"}),(0,s.jsxs)("p",{children:["• 路径中可以使用变量，如 ","{levelId}"," 会被替换为实际的关卡ID"]}),(0,s.jsx)("p",{children:"• 默认类型的配置不能删除，但可以禁用"})]}),type:"info",showIcon:!0,style:{marginTop:"24px"}})]})}):(0,s.jsx)("div",{style:{padding:"24px"},children:(0,s.jsx)(h.A,{message:"分享配置不存在",description:"请检查URL是否正确，或者该配置已被删除。",type:"error",showIcon:!0,action:(0,s.jsx)(u.Ay,{size:"small",onClick:()=>t.push("/shares"),children:"返回列表"})})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29220:(e,t,r)=>{"use strict";r.d(t,{A:()=>O});var s=r(43210),n=r(91039),i=r(41514),a=r(15693),o=r(51297),l=r(74550),c=r(69662),d=r.n(c),p=r(13934),h=r(44666),u=r(7224),m=r(56883),x=r(71802),v=r(42411),g=r(32476),y=r(13581);let b=(e,t,r,s,n)=>({background:e,border:`${(0,v.zA)(s.lineWidth)} ${s.lineType} ${t}`,[`${n}-icon`]:{color:r}}),f=e=>{let{componentCls:t,motionDurationSlow:r,marginXS:s,marginSM:n,fontSize:i,fontSizeLG:a,lineHeight:o,borderRadiusLG:l,motionEaseInOutCirc:c,withDescriptionIconSize:d,colorText:p,colorTextHeading:h,withDescriptionPadding:u,defaultPadding:m}=e;return{[t]:Object.assign(Object.assign({},(0,g.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:m,wordWrap:"break-word",borderRadius:l,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:s,lineHeight:0},"&-description":{display:"none",fontSize:i,lineHeight:o},"&-message":{color:h},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${r} ${c}, opacity ${r} ${c},
        padding-top ${r} ${c}, padding-bottom ${r} ${c},
        margin-bottom ${r} ${c}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:u,[`${t}-icon`]:{marginInlineEnd:n,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:s,color:h,fontSize:a},[`${t}-description`]:{display:"block",color:p}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},j=e=>{let{componentCls:t,colorSuccess:r,colorSuccessBorder:s,colorSuccessBg:n,colorWarning:i,colorWarningBorder:a,colorWarningBg:o,colorError:l,colorErrorBorder:c,colorErrorBg:d,colorInfo:p,colorInfoBorder:h,colorInfoBg:u}=e;return{[t]:{"&-success":b(n,s,r,e,t),"&-info":b(u,h,p,e,t),"&-warning":b(o,a,i,e,t),"&-error":Object.assign(Object.assign({},b(d,c,l,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},A=e=>{let{componentCls:t,iconCls:r,motionDurationMid:s,marginXS:n,fontSizeIcon:i,colorIcon:a,colorIconHover:o}=e;return{[t]:{"&-action":{marginInlineStart:n},[`${t}-close-icon`]:{marginInlineStart:n,padding:0,overflow:"hidden",fontSize:i,lineHeight:(0,v.zA)(i),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${r}-close`]:{color:a,transition:`color ${s}`,"&:hover":{color:o}}},"&-close-text":{color:a,transition:`color ${s}`,"&:hover":{color:o}}}}},w=(0,y.OF)("Alert",e=>[f(e),j(e),A(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}));var P=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,s=Object.getOwnPropertySymbols(e);n<s.length;n++)0>t.indexOf(s[n])&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]]);return r};let $={success:n.A,info:l.A,error:i.A,warning:o.A},k=e=>{let{icon:t,prefixCls:r,type:n}=e,i=$[n]||null;return t?(0,m.fx)(t,s.createElement("span",{className:`${r}-icon`},t),()=>({className:d()(`${r}-icon`,t.props.className)})):s.createElement(i,{className:`${r}-icon`})},I=e=>{let{isClosable:t,prefixCls:r,closeIcon:n,handleClose:i,ariaProps:o}=e,l=!0===n||void 0===n?s.createElement(a.A,null):n;return t?s.createElement("button",Object.assign({type:"button",onClick:i,className:`${r}-close-icon`,tabIndex:0},o),l):null},C=s.forwardRef((e,t)=>{let{description:r,prefixCls:n,message:i,banner:a,className:o,rootClassName:l,style:c,onMouseEnter:m,onMouseLeave:v,onClick:g,afterClose:y,showIcon:b,closable:f,closeText:j,closeIcon:A,action:$,id:C}=e,S=P(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[E,z]=s.useState(!1),q=s.useRef(null);s.useImperativeHandle(t,()=>({nativeElement:q.current}));let{getPrefixCls:R,direction:_,closable:L,closeIcon:O,className:M,style:D}=(0,x.TP)("alert"),F=R("alert",n),[N,B,H]=w(F),T=t=>{var r;z(!0),null==(r=e.onClose)||r.call(e,t)},U=s.useMemo(()=>void 0!==e.type?e.type:a?"warning":"info",[e.type,a]),V=s.useMemo(()=>"object"==typeof f&&!!f.closeIcon||!!j||("boolean"==typeof f?f:!1!==A&&null!=A||!!L),[j,A,f,L]),G=!!a&&void 0===b||b,W=d()(F,`${F}-${U}`,{[`${F}-with-description`]:!!r,[`${F}-no-icon`]:!G,[`${F}-banner`]:!!a,[`${F}-rtl`]:"rtl"===_},M,o,l,H,B),X=(0,h.A)(S,{aria:!0,data:!0}),K=s.useMemo(()=>"object"==typeof f&&f.closeIcon?f.closeIcon:j||(void 0!==A?A:"object"==typeof L&&L.closeIcon?L.closeIcon:O),[A,f,j,O]),Y=s.useMemo(()=>{let e=null!=f?f:L;if("object"==typeof e){let{closeIcon:t}=e;return P(e,["closeIcon"])}return{}},[f,L]);return N(s.createElement(p.Ay,{visible:!E,motionName:`${F}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:y},({className:t,style:n},a)=>s.createElement("div",Object.assign({id:C,ref:(0,u.K4)(q,a),"data-show":!E,className:d()(W,t),style:Object.assign(Object.assign(Object.assign({},D),c),n),onMouseEnter:m,onMouseLeave:v,onClick:g,role:"alert"},X),G?s.createElement(k,{description:r,icon:e.icon,prefixCls:F,type:U}):null,s.createElement("div",{className:`${F}-content`},i?s.createElement("div",{className:`${F}-message`},i):null,r?s.createElement("div",{className:`${F}-description`},r):null),$?s.createElement("div",{className:`${F}-action`},$):null,s.createElement(I,{isClosable:V,prefixCls:F,closeIcon:K,handleClose:T,ariaProps:Y}))))});var S=r(67737),E=r(49617),z=r(30402),q=r(85764),R=r(1630),_=r(69561);let L=function(e){function t(){var e,r,s;return(0,S.A)(this,t),r=t,s=arguments,r=(0,z.A)(r),(e=(0,R.A)(this,(0,q.A)()?Reflect.construct(r,s||[],(0,z.A)(this).constructor):r.apply(this,s))).state={error:void 0,info:{componentStack:""}},e}return(0,_.A)(t,e),(0,E.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:r,children:n}=this.props,{error:i,info:a}=this.state,o=(null==a?void 0:a.componentStack)||null,l=void 0===e?(i||"").toString():e;return i?s.createElement(C,{id:r,type:"error",message:l,description:s.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?o:t)}):n}}])}(s.Component);C.ErrorBoundary=L;let O=C},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35692:()=>{},37912:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>S});var s=r(60687),n=r(43210),i=r(85814),a=r.n(i),o=r(16189),l=r(98836),c=r(99053),d=r(63736),p=r(56072),h=r(78620),u=r(60203),m=r(53788),x=r(28859),v=r(81945),g=r(3788),y=r(73237),b=r(94858),f=r(47453),j=r(72061),A=r(80461),w=r(71103);let{Header:P,Content:$,Sider:k,Footer:I}=l.A,C=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,s.jsx)(u.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,s.jsx)(m.A,{})},{key:"phrases",label:"词组管理",path:"/phrases",icon:(0,s.jsx)(x.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,s.jsx)(v.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,s.jsx)(g.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,s.jsx)(y.A,{})},{key:"vip-users",label:"VIP用户管理",path:"/vip-users",icon:(0,s.jsx)(b.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,s.jsx)(f.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,s.jsx)(j.A,{})}];function S({children:e}){let t=(0,o.useRouter)(),r=(0,o.usePathname)(),[i,u]=(0,n.useState)(!1),m=[{key:"logout",icon:(0,s.jsx)(A.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),t.push("/login")}}],x=C.find(e=>r.startsWith(e.path))?.key||"dashboard";return(0,s.jsxs)(l.A,{style:{minHeight:"100vh"},children:[(0,s.jsxs)(k,{collapsible:!0,collapsed:i,onCollapse:e=>u(e),children:[(0,s.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,s.jsx)(c.A.Text,{style:{color:"white",fontSize:i?"10px":"16px",transition:"font-size 0.2s"},children:i?"后台":"游戏管理后台"})}),(0,s.jsx)(d.A,{theme:"dark",selectedKeys:[x],mode:"inline",items:C.map(e=>({key:e.key,icon:e.icon,label:(0,s.jsx)(a(),{href:e.path,children:e.label})}))})]}),(0,s.jsxs)(l.A,{children:[(0,s.jsxs)(P,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,s.jsx)(p.A,{menu:{items:m},placement:"bottomRight",children:(0,s.jsx)(h.A,{style:{cursor:"pointer"},icon:(0,s.jsx)(w.A,{})})}),(0,s.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,s.jsx)($,{style:{margin:"16px"},children:e}),(0,s.jsxs)(I,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},42654:(e,t,r)=>{Promise.resolve().then(r.bind(r,26953))},43910:(e,t,r)=>{"use strict";r.d(t,{Dw:()=>n,WS:()=>i,cm:()=>a});var s=r(98501);class n{static async getAllShareConfigs(){return(await s.F.get("/api/v1/share")).data}static async getActiveShareConfigs(){return(await s.F.get("/api/v1/share/active")).data}static async getDefaultShareConfig(){return(await s.F.get("/api/v1/share/default")).data}static async getShareConfigByType(e){return(await s.F.get(`/api/v1/share/type/${e}`)).data}static async getShareConfigById(e){return(await s.F.get(`/api/v1/share/${e}`)).data}static async createShareConfig(e){return(await s.F.post("/api/v1/share",e)).data}static async updateShareConfig(e,t){return(await s.F.patch(`/api/v1/share/${e}`,t)).data}static async toggleShareConfig(e){return(await s.F.put(`/api/v1/share/${e}/toggle`)).data}static async deleteShareConfig(e){return(await s.F.delete(`/api/v1/share/${e}`)).data}}let i=[{value:"default",label:"默认分享",description:"通用分享，适用于首页、游戏页等"},{value:"result",label:"结果分享",description:"展示用户成绩的分享"},{value:"level",label:"关卡分享",description:"邀请好友挑战特定关卡"},{value:"achievement",label:"成就分享",description:"展示用户获得的成就"},{value:"custom",label:"自定义分享",description:"特殊活动或自定义场景"}],a=[{label:"首页",value:"/pages/index/index",description:"小程序首页"},{label:"游戏页",value:"/pages/game/game",description:"游戏主页面"},{label:"关卡页",value:"/pages/level/level?id={levelId}",description:"特定关卡页面，{levelId}会被替换为实际关卡ID"},{label:"结果页",value:"/pages/result/result?score={score}",description:"结果展示页面，{score}会被替换为实际分数"},{label:"排行榜",value:"/pages/rank/rank",description:"排行榜页面"}]},53395:(e,t,r)=>{Promise.resolve().then(r.bind(r,6468)),Promise.resolve().then(r.bind(r,43741))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56689:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=r(65239),n=r(48088),i=r(88170),a=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["(admin)",{children:["shares",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96591)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\edit\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(admin)/shares/[id]/edit/page",pathname:"/shares/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},59448:(e,t,r)=>{Promise.resolve().then(r.bind(r,37912))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},76891:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85975:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(80828),n=r(43210);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"};var a=r(21898);let o=n.forwardRef(function(e,t){return n.createElement(a.A,(0,s.A)({},e,{ref:t,icon:i}))})},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413);r(61120);var n=r(68016);r(35692),r(28087);let i=({children:e})=>(0,s.jsxs)("html",{lang:"en",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("meta",{charSet:"utf-8"}),(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,s.jsx)("meta",{name:"description",content:"游戏后台管理系统"}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,s.jsx)("title",{children:"游戏管理后台"})]}),(0,s.jsx)("body",{children:(0,s.jsx)(n.Z,{children:e})})]})},94735:e=>{"use strict";e.exports=require("events")},96591:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\shares\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\edit\\page.tsx","default")},97103:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(80828),n=r(43210);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"};var a=r(21898);let o=n.forwardRef(function(e,t){return n.createElement(a.A,(0,s.A)({},e,{ref:t,icon:i}))})},98501:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var s=r(51060),n=r(35899);let i={BASE_URL:"http://localhost:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return`${this.BASE_URL}${this.API_PREFIX}`}},a=s.A.create({baseURL:i.BASE_URL,timeout:i.TIMEOUT,headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),a.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){let{status:t,data:r}=e.response;switch(t){case 401:n.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:n.Ay.error("没有权限访问该资源");break;case 404:n.Ay.error("请求的资源不存在");break;case 500:n.Ay.error("服务器内部错误");break;default:n.Ay.error(r?.message||"请求失败")}}else e.request?n.Ay.error("网络连接失败，请检查网络"):n.Ay.error("请求配置错误");return Promise.reject(e)});let o={get:(e,t)=>a.get(e,t),post:(e,t,r)=>a.post(e,t,r),put:(e,t,r)=>a.put(e,t,r),patch:(e,t,r)=>a.patch(e,t,r),delete:(e,t)=>a.delete(e,t)}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,433,658,816,204,331,553,84,173,915],()=>r(56689));module.exports=s})();