import { Document } from 'mongoose';
export type ShareConfigDocument = ShareConfig & Document;
export declare class ShareConfig {
    id: string;
    name: string;
    title: string;
    path: string;
    imageUrl?: string;
    description?: string;
    type: string;
    isActive: boolean;
    sortOrder: number;
    createdAt: Date;
    updatedAt: Date;
}
export declare const ShareConfigSchema: import("mongoose").Schema<ShareConfig, import("mongoose").Model<ShareConfig, any, any, any, Document<unknown, any, ShareConfig, any> & ShareConfig & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, ShareConfig, Document<unknown, {}, import("mongoose").FlatRecord<ShareConfig>, {}> & import("mongoose").FlatRecord<ShareConfig> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
export interface ShareConfigEntity {
    id: string;
    name: string;
    title: string;
    path: string;
    imageUrl?: string;
    description?: string;
    type: string;
    isActive: boolean;
    sortOrder: number;
    createdAt: Date;
    updatedAt: Date;
}
