"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateThesaurusDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateThesaurusDto {
    name;
    description;
}
exports.CreateThesaurusDto = CreateThesaurusDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词库名称', example: '日常用语词库' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '词库名称不能为空' }),
    __metadata("design:type", String)
], CreateThesaurusDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词库描述', example: '包含常见的日常对话用语', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateThesaurusDto.prototype, "description", void 0);
//# sourceMappingURL=create-thesaurus.dto.js.map