"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateShareConfigDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateShareConfigDto {
    name;
    title;
    path;
    imageUrl;
    description;
    type;
    isActive;
    sortOrder;
}
exports.CreateShareConfigDto = CreateShareConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享配置名称', example: '默认分享配置' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '配置名称不能为空' }),
    __metadata("design:type", String)
], CreateShareConfigDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享标题', example: '一起来挑战词汇游戏！' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '分享标题不能为空' }),
    __metadata("design:type", String)
], CreateShareConfigDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享路径', example: '/pages/index/index' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '分享路径不能为空' }),
    __metadata("design:type", String)
], CreateShareConfigDto.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享图片URL', example: 'https://example.com/share.jpg', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)({}, { message: '请输入有效的图片URL' }),
    __metadata("design:type", String)
], CreateShareConfigDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享描述', example: '挑战你的词汇量，看看你能通过多少关！', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateShareConfigDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享类型', example: 'default', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateShareConfigDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否启用', example: true, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateShareConfigDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序权重', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序权重必须是数字' }),
    __metadata("design:type", Number)
], CreateShareConfigDto.prototype, "sortOrder", void 0);
//# sourceMappingURL=create-share-config.dto.js.map