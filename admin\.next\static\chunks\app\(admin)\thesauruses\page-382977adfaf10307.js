(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5496],{12259:(e,t,a)=>{Promise.resolve().then(a.bind(a,72621))},21073:(e,t,a)=>{"use strict";a.d(t,{Au:()=>n,Io:()=>o,LQ:()=>s,zN:()=>c});var r=a(29799);let n={getAll:async()=>(await r.F.get("/api/v1/thesauruses")).data,getById:async e=>(await r.F.get("/api/v1/thesauruses/".concat(e))).data,create:async e=>(await r.F.post("/api/v1/thesauruses",e)).data,update:async(e,t)=>(await r.F.patch("/api/v1/thesauruses/".concat(e),t)).data,delete:async e=>{await r.F.delete("/api/v1/thesauruses/".concat(e))},addPhrase:async(e,t)=>(await r.F.post("/api/v1/thesauruses/".concat(e,"/phrases"),t)).data,removePhrase:async(e,t)=>(await r.F.delete("/api/v1/thesauruses/".concat(e,"/phrases/").concat(t))).data},s=n.create;n.update,n.delete;let o=n.delete;n.getById,n.getAll;let c=n.getAll},29799:(e,t,a)=>{"use strict";a.d(t,{F:()=>c});var r=a(23464),n=a(19868);let s={BASE_URL:"http://127.0.0.1:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}},o=r.A.create({baseURL:s.BASE_URL,timeout:s.TIMEOUT,headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),o.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){let{status:t,data:a}=e.response;switch(t){case 401:n.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:n.Ay.error("没有权限访问该资源");break;case 404:n.Ay.error("请求的资源不存在");break;case 500:n.Ay.error("服务器内部错误");break;default:n.Ay.error((null==a?void 0:a.message)||"请求失败")}}else e.request?n.Ay.error("网络连接失败，请检查网络"):n.Ay.error("请求配置错误");return Promise.reject(e)});let c={get:(e,t)=>o.get(e,t),post:(e,t,a)=>o.post(e,t,a),put:(e,t,a)=>o.put(e,t,a),patch:(e,t,a)=>o.patch(e,t,a),delete:(e,t)=>o.delete(e,t)}},35376:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=e=>({[e.componentCls]:{["".concat(e.antCls,"-motion-collapse-legacy")]:{overflow:"hidden","&-active":{transition:"height ".concat(e.motionDurationMid," ").concat(e.motionEaseInOut,",\n        opacity ").concat(e.motionDurationMid," ").concat(e.motionEaseInOut," !important")}},["".concat(e.antCls,"-motion-collapse")]:{overflow:"hidden",transition:"height ".concat(e.motionDurationMid," ").concat(e.motionEaseInOut,",\n        opacity ").concat(e.motionDurationMid," ").concat(e.motionEaseInOut," !important")}}})},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useServerInsertedHTML")&&a.d(t,{useServerInsertedHTML:function(){return r.useServerInsertedHTML}})},72621:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var r=a(95155),n=a(12115),s=a(19868),o=a(32814),c=a(12320),l=a(30662),i=a(505),u=a(97605),d=a(51087),p=a(79630);let m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"};var f=a(62764),h=n.forwardRef(function(e,t){return n.createElement(f.A,(0,p.A)({},e,{ref:t,icon:m}))}),g=a(46996),y=a(35695),A=a(21073);let v=()=>{let[e,t]=(0,n.useState)([]),[a,p]=(0,n.useState)(!1),m=(0,y.useRouter)(),f=(0,n.useCallback)(async()=>{p(!0);try{let e=await (0,A.zN)();t(e)}catch(e){console.error("获取词库列表失败:",e),s.Ay.error(e.message||"获取词库列表失败！")}finally{p(!1)}},[]);(0,n.useEffect)(()=>{f()},[f]);let v=(0,n.useCallback)(async e=>{try{await (0,A.Io)(e),s.Ay.success("词库删除成功！"),f()}catch(t){console.error("删除词库 ".concat(e," 失败:"),t),s.Ay.error(t.message||"词库删除失败！")}},[f]),w=(0,n.useCallback)(e=>{o.A.confirm({title:'确定要删除词库 "'.concat(e.name,'" 吗?'),icon:(0,r.jsx)(h,{}),content:"此操作不可撤销。如果有关联的关卡，请谨慎操作。",okText:"删除",okType:"danger",cancelText:"取消",onOk(){v(e.id)}})},[v]),b=[{title:"名称",dataIndex:"name",key:"name"},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0},{title:"词组数",dataIndex:"phraseIds",key:"phraseCount",render:e=>(null==e?void 0:e.length)||0},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",render:e=>new Date(e).toLocaleString()},{title:"更新时间",dataIndex:"updatedAt",key:"updatedAt",render:e=>new Date(e).toLocaleString()},{title:"操作",key:"action",render:(e,t)=>(0,r.jsxs)(c.A,{size:"middle",children:[(0,r.jsx)(l.Ay,{size:"small",onClick:()=>{s.Ay.info("编辑词库 ".concat(t.id," (功能待实现)"))},children:"编辑"}),(0,r.jsx)(l.Ay,{size:"small",danger:!0,onClick:()=>w(t),children:"删除"})]})}];return(0,r.jsxs)(i.A,{children:[(0,r.jsx)(u.A.Title,{level:2,children:"词库管理"}),(0,r.jsx)(l.Ay,{type:"primary",icon:(0,r.jsx)(g.A,{}),onClick:()=>m.push("/thesauruses/create"),style:{marginBottom:16},children:"创建词库"}),(0,r.jsx)(d.A,{columns:b,dataSource:e,rowKey:"id",loading:a})]})}},82724:(e,t,a)=>{"use strict";a.d(t,{A:()=>b});var r=a(12115),n=a(29300),s=a.n(n),o=a(11261),c=a(74686),l=a(9184),i=a(53014),u=a(79007),d=a(15982),p=a(44494),m=a(68151),f=a(9836),h=a(63568),g=a(63893),y=a(18574),A=a(84311),v=a(30611),w=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(a[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)0>t.indexOf(r[n])&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(a[r[n]]=e[r[n]]);return a};let b=(0,r.forwardRef)((e,t)=>{let{prefixCls:a,bordered:n=!0,status:b,size:C,disabled:x,onBlur:I,onFocus:O,suffix:k,allowClear:E,addonAfter:j,addonBefore:P,className:R,style:S,styles:L,rootClassName:T,onChange:_,classNames:z,variant:B}=e,F=w(e,["prefixCls","bordered","status","size","disabled","onBlur","onFocus","suffix","allowClear","addonAfter","addonBefore","className","style","styles","rootClassName","onChange","classNames","variant"]),{getPrefixCls:M,direction:N,allowClear:U,autoComplete:D,className:H,style:W,classNames:q,styles:K}=(0,d.TP)("input"),Q=M("input",a),X=(0,r.useRef)(null),G=(0,m.A)(Q),[V,$,J]=(0,v.MG)(Q,T),[Y]=(0,v.Ay)(Q,G),{compactSize:Z,compactItemClassnames:ee}=(0,y.RQ)(Q,N),et=(0,f.A)(e=>{var t;return null!=(t=null!=C?C:Z)?t:e}),ea=r.useContext(p.A),{status:er,hasFeedback:en,feedbackIcon:es}=(0,r.useContext)(h.$W),eo=(0,u.v)(er,b),ec=function(e){return!!(e.prefix||e.suffix||e.allowClear||e.showCount)}(e)||!!en;(0,r.useRef)(ec);let el=(0,A.A)(X,!0),ei=(en||k)&&r.createElement(r.Fragment,null,k,en&&es),eu=(0,i.A)(null!=E?E:U),[ed,ep]=(0,g.A)("input",B,n);return V(Y(r.createElement(o.A,Object.assign({ref:(0,c.K4)(t,X),prefixCls:Q,autoComplete:D},F,{disabled:null!=x?x:ea,onBlur:e=>{el(),null==I||I(e)},onFocus:e=>{el(),null==O||O(e)},style:Object.assign(Object.assign({},W),S),styles:Object.assign(Object.assign({},K),L),suffix:ei,allowClear:eu,className:s()(R,T,J,G,ee,H),onChange:e=>{el(),null==_||_(e)},addonBefore:P&&r.createElement(l.A,{form:!0,space:!0},P),addonAfter:j&&r.createElement(l.A,{form:!0,space:!0},j),classNames:Object.assign(Object.assign(Object.assign({},z),q),{input:s()({["".concat(Q,"-sm")]:"small"===et,["".concat(Q,"-lg")]:"large"===et,["".concat(Q,"-rtl")]:"rtl"===N},null==z?void 0:z.input,q.input,$),variant:s()({["".concat(Q,"-").concat(ed)]:ep},(0,u.L)(Q,eo)),affixWrapper:s()({["".concat(Q,"-affix-wrapper-sm")]:"small"===et,["".concat(Q,"-affix-wrapper-lg")]:"large"===et,["".concat(Q,"-affix-wrapper-rtl")]:"rtl"===N},$),wrapper:s()({["".concat(Q,"-group-rtl")]:"rtl"===N},$),groupWrapper:s()({["".concat(Q,"-group-wrapper-sm")]:"small"===et,["".concat(Q,"-group-wrapper-lg")]:"large"===et,["".concat(Q,"-group-wrapper-rtl")]:"rtl"===N,["".concat(Q,"-group-wrapper-").concat(ed)]:ep},(0,u.L)("".concat(Q,"-group-wrapper"),eo,en),$)})}))))})},84311:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(12115);function n(e,t){let a=(0,r.useRef)([]),n=()=>{a.current.push(setTimeout(()=>{var t,a,r,n;(null==(t=e.current)?void 0:t.input)&&(null==(a=e.current)?void 0:a.input.getAttribute("type"))==="password"&&(null==(r=e.current)?void 0:r.input.hasAttribute("value"))&&(null==(n=e.current)||n.input.removeAttribute("value"))}))};return(0,r.useEffect)(()=>(t&&n(),()=>a.current.forEach(e=>{e&&clearTimeout(e)})),[]),n}},88870:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(79630),n=a(12115);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"}}]},name:"search",theme:"outlined"};var o=a(62764);let c=n.forwardRef(function(e,t){return n.createElement(o.A,(0,r.A)({},e,{ref:t,icon:s}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[8340,547,7469,7497,6312,778,7605,2343,3726,4124,8441,1684,7358],()=>t(12259)),_N_E=e.O()}]);