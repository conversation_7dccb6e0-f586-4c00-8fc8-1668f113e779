"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShareController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const share_service_1 = require("./share.service");
const create_share_config_dto_1 = require("./dto/create-share-config.dto");
const update_share_config_dto_1 = require("./dto/update-share-config.dto");
const share_config_response_dto_1 = require("./dto/share-config-response.dto");
let ShareController = class ShareController {
    shareService;
    constructor(shareService) {
        this.shareService = shareService;
    }
    async create(createShareConfigDto) {
        return this.shareService.create(createShareConfigDto);
    }
    async findAll() {
        return this.shareService.findAll();
    }
    async findActive() {
        return this.shareService.findActive();
    }
    async getDefault() {
        return this.shareService.getDefaultConfig();
    }
    async findByType(type) {
        return this.shareService.findByType(type);
    }
    async findOne(id) {
        return this.shareService.findOne(id);
    }
    async update(id, updateShareConfigDto) {
        return this.shareService.update(id, updateShareConfigDto);
    }
    async toggleActive(id) {
        return this.shareService.toggleActive(id);
    }
    async remove(id) {
        await this.shareService.remove(id);
        return { message: '分享配置删除成功' };
    }
};
exports.ShareController = ShareController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建分享配置' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '分享配置创建成功',
        type: share_config_response_dto_1.ShareConfigResponseDto,
        schema: {
            example: {
                id: 'share-config-001',
                name: '默认分享配置',
                title: '一起来挑战词汇游戏！',
                path: '/pages/index/index',
                imageUrl: 'https://example.com/share.jpg',
                description: '挑战你的词汇量，看看你能通过多少关！',
                type: 'default',
                isActive: true,
                sortOrder: 1,
                createdAt: '2025-06-19T10:00:00.000Z',
                updatedAt: '2025-06-19T10:00:00.000Z'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数无效或默认配置已存在' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_share_config_dto_1.CreateShareConfigDto]),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有分享配置' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '分享配置列表获取成功',
        type: [share_config_response_dto_1.ShareConfigResponseDto]
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('active'),
    (0, swagger_1.ApiOperation)({ summary: '获取启用的分享配置' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '启用的分享配置列表获取成功',
        type: [share_config_response_dto_1.ShareConfigResponseDto]
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "findActive", null);
__decorate([
    (0, common_1.Get)('default'),
    (0, swagger_1.ApiOperation)({ summary: '获取默认分享配置' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '默认分享配置获取成功',
        type: share_config_response_dto_1.ShareConfigResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '默认分享配置不存在' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "getDefault", null);
__decorate([
    (0, common_1.Get)('type/:type'),
    (0, swagger_1.ApiOperation)({ summary: '根据类型获取分享配置' }),
    (0, swagger_1.ApiParam)({ name: 'type', description: '分享类型', example: 'default' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '分享配置获取成功',
        type: share_config_response_dto_1.ShareConfigResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '指定类型的分享配置不存在' }),
    __param(0, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "findByType", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取分享配置' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '分享配置ID', example: 'share-config-001' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '分享配置获取成功',
        type: share_config_response_dto_1.ShareConfigResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '分享配置不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新分享配置' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '分享配置ID', example: 'share-config-001' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '分享配置更新成功',
        type: share_config_response_dto_1.ShareConfigResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '分享配置不存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数无效' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_share_config_dto_1.UpdateShareConfigDto]),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "update", null);
__decorate([
    (0, common_1.Put)(':id/toggle'),
    (0, swagger_1.ApiOperation)({ summary: '启用/禁用分享配置' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '分享配置ID', example: 'share-config-001' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '分享配置状态切换成功',
        type: share_config_response_dto_1.ShareConfigResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '分享配置不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "toggleActive", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除分享配置' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '分享配置ID', example: 'share-config-001' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '分享配置删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '分享配置不存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '不能删除默认配置' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "remove", null);
exports.ShareController = ShareController = __decorate([
    (0, swagger_1.ApiTags)('分享管理'),
    (0, common_1.Controller)('share'),
    __metadata("design:paramtypes", [share_service_1.ShareService])
], ShareController);
//# sourceMappingURL=share.controller.js.map