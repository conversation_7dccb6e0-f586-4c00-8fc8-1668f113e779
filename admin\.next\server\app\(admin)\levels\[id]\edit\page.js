(()=>{var e={};e.id=144,e.ids=[144],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9146:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(60687),i=r(43210),n=r(16189),o=r(99053),a=r(94733),d=r(28173),l=r(35899),p=r(11585),u=r(70553),c=r(29220),m=r(63105),h=r(5948),x=r(70084),y=r(48111),v=r(21411),f=r(23870);let{Title:b}=o.A,{TextArea:A}=a.A;function j(){let[e]=d.A.useForm(),t=(0,n.useRouter)(),r=(0,n.useParams)().id,[o,j]=(0,i.useState)(!1),[g,w]=(0,i.useState)(!0),[q,P]=(0,i.useState)([]),[I,_]=(0,i.useState)([]),[C,k]=(0,i.useState)(null),z=async e=>{if((!e.thesaurusIds||0===e.thesaurusIds.length)&&(!e.phraseIds||0===e.phraseIds.length))return void l.Ay.error("请至少选择词库或词组中的一种");j(!0);try{let s={name:e.name,difficulty:e.difficulty,description:e.description,thesaurusIds:e.thesaurusIds||[],phraseIds:e.phraseIds||[]};await f.k3.update(r,s),l.Ay.success("关卡更新成功"),t.push("/levels")}catch(e){l.Ay.error("更新关卡失败")}finally{j(!1)}};return g?(0,s.jsx)(p.A,{children:(0,s.jsxs)("div",{style:{textAlign:"center",padding:"50px 0"},children:[(0,s.jsx)(u.A,{size:"large"}),(0,s.jsx)("div",{style:{marginTop:16},children:"加载中..."})]})}):(0,s.jsx)("div",{children:(0,s.jsxs)(p.A,{children:[(0,s.jsx)(b,{level:2,children:"编辑关卡"}),C&&(0,s.jsx)(c.A,{message:`正在编辑关卡：${C.name}`,description:`创建时间：${C.createdAt}，最后更新：${C.updatedAt}`,type:"info",style:{marginBottom:16},showIcon:!0}),(0,s.jsxs)(d.A,{form:e,layout:"vertical",onFinish:z,children:[(0,s.jsx)(d.A.Item,{name:"name",label:"关卡名称",rules:[{required:!0,message:"请输入关卡名称"}],children:(0,s.jsx)(a.A,{placeholder:"例如：第1关 - 基础词汇"})}),(0,s.jsx)(d.A.Item,{name:"difficulty",label:"难度等级 (1-5)",rules:[{required:!0,message:"请选择关卡难度"}],children:(0,s.jsx)(m.A,{min:1,max:5,marks:{1:"简单",2:"容易",3:"中等",4:"困难",5:"极难"}})}),(0,s.jsx)(d.A.Item,{name:"description",label:"关卡描述",children:(0,s.jsx)(A,{rows:3,placeholder:"请输入关卡描述（可选）"})}),(0,s.jsx)(h.A,{children:"选择关卡内容"}),(0,s.jsx)(c.A,{message:"提示：您可以通过选择词库或直接选择词组来设置关卡内容，至少需要选择其中一种方式。",type:"info",style:{marginBottom:16}}),(0,s.jsx)(d.A.Item,{name:"thesaurusIds",label:"选择词库（推荐）",help:"选择词库后，关卡将包含该词库中的所有词组",children:(0,s.jsx)(x.A,{mode:"multiple",allowClear:!0,style:{width:"100%"},placeholder:"请选择要关联的词库",filterOption:(e,t)=>t?.label?.toLowerCase().includes(e.toLowerCase()),options:q.map(e=>({label:e.name,value:e.id}))})}),(0,s.jsx)(d.A.Item,{name:"phraseIds",label:"直接选择词组",help:"如果不选择词库，可以直接选择具体的词组",children:(0,s.jsx)(x.A,{mode:"multiple",allowClear:!0,style:{width:"100%"},placeholder:"请选择要包含的词组",filterOption:(e,t)=>t?.label?.toLowerCase().includes(e.toLowerCase()),options:I.map(e=>({label:`${e.text} - ${e.meaning}`,value:e.id}))})}),(0,s.jsx)(d.A.Item,{children:(0,s.jsxs)(y.A,{children:[(0,s.jsx)(v.Ay,{type:"primary",htmlType:"submit",loading:o,children:"更新关卡"}),(0,s.jsx)(v.Ay,{onClick:()=>t.back(),children:"取消"})]})})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40387:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>c,tree:()=>l});var s=r(65239),i=r(48088),n=r(88170),o=r.n(n),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["(admin)",{children:["levels",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,91028)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\[id]\\edit\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(admin)/levels/[id]/edit/page",pathname:"/levels/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77471:(e,t,r)=>{Promise.resolve().then(r.bind(r,9146))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91028:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\levels\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\[id]\\edit\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},95623:(e,t,r)=>{Promise.resolve().then(r.bind(r,91028))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,433,658,816,204,331,553,84,173,405,105,451],()=>r(40387));module.exports=s})();