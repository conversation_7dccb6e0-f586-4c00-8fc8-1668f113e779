"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThesaurusService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const thesaurus_entity_1 = require("./entities/thesaurus.entity");
const uuid_1 = require("uuid");
const date_formatter_1 = require("../../common/utils/date-formatter");
const phrase_service_1 = require("../phrase/phrase.service");
let ThesaurusService = class ThesaurusService {
    thesaurusModel;
    phraseService;
    constructor(thesaurusModel, phraseService) {
        this.thesaurusModel = thesaurusModel;
        this.phraseService = phraseService;
    }
    _mapToResponseDto(thesaurus) {
        return {
            id: thesaurus.id,
            name: thesaurus.name,
            description: thesaurus.description,
            phraseIds: thesaurus.phraseIds,
            createdAt: (0, date_formatter_1.formatDate)(thesaurus.createdAt),
            updatedAt: (0, date_formatter_1.formatDate)(thesaurus.updatedAt),
        };
    }
    async getThesaurusEntity(id) {
        const thesaurus = await this.thesaurusModel.findOne({ id }).exec();
        if (!thesaurus) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的词库`);
        }
        return thesaurus;
    }
    async create(createThesaurusDto) {
        const newThesaurus = new this.thesaurusModel({
            id: (0, uuid_1.v4)(),
            name: createThesaurusDto.name,
            description: createThesaurusDto.description,
            phraseIds: [],
        });
        const savedThesaurus = await newThesaurus.save();
        return this._mapToResponseDto(savedThesaurus);
    }
    async findAll() {
        const thesauruses = await this.thesaurusModel.find().exec();
        return thesauruses.map(thesaurus => this._mapToResponseDto(thesaurus));
    }
    async findOne(id) {
        const thesaurus = await this.getThesaurusEntity(id);
        return this._mapToResponseDto(thesaurus);
    }
    async update(id, updateThesaurusDto) {
        const updatedThesaurus = await this.thesaurusModel.findOneAndUpdate({ id }, { ...updateThesaurusDto, updatedAt: new Date() }, { new: true }).exec();
        if (!updatedThesaurus) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的词库`);
        }
        return this._mapToResponseDto(updatedThesaurus);
    }
    async remove(id) {
        const result = await this.thesaurusModel.deleteOne({ id }).exec();
        if (result.deletedCount === 0) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的词库`);
        }
    }
    async addPhrase(thesaurusId, phraseId) {
        const thesaurus = await this.getThesaurusEntity(thesaurusId);
        await this.phraseService.findOne(phraseId);
        if (thesaurus.phraseIds.includes(phraseId)) {
            throw new common_1.BadRequestException(`词组 ID "${phraseId}" 已存在于词库 "${thesaurusId}" 中`);
        }
        thesaurus.phraseIds.push(phraseId);
        const updatedThesaurus = await thesaurus.save();
        return this._mapToResponseDto(updatedThesaurus);
    }
    async removePhrase(thesaurusId, phraseId) {
        const thesaurus = await this.getThesaurusEntity(thesaurusId);
        const phraseIndexInThesaurus = thesaurus.phraseIds.indexOf(phraseId);
        if (phraseIndexInThesaurus === -1) {
            throw new common_1.NotFoundException(`词组 ID "${phraseId}" 不在词库 "${thesaurusId}" 中`);
        }
        thesaurus.phraseIds.splice(phraseIndexInThesaurus, 1);
        const updatedThesaurus = await thesaurus.save();
        return this._mapToResponseDto(updatedThesaurus);
    }
    async isPhraseInAnyOfThesauruses(phraseId, thesaurusIds) {
        for (const thesaurusId of thesaurusIds) {
            try {
                const thesaurus = await this.getThesaurusEntity(thesaurusId);
                if (thesaurus.phraseIds.includes(phraseId)) {
                    return true;
                }
            }
            catch (error) {
                console.warn(`检查词组是否存在于词库时，词库ID "${thesaurusId}" 未找到。`);
            }
        }
        return false;
    }
};
exports.ThesaurusService = ThesaurusService;
exports.ThesaurusService = ThesaurusService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(thesaurus_entity_1.Thesaurus.name)),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => phrase_service_1.PhraseService))),
    __metadata("design:paramtypes", [mongoose_2.Model,
        phrase_service_1.PhraseService])
], ThesaurusService);
//# sourceMappingURL=thesaurus.service.js.map