(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9019],{14874:(e,a,r)=>{Promise.resolve().then(r.bind(r,45561))},29799:(e,a,r)=>{"use strict";r.d(a,{F:()=>n});var s=r(23464),t=r(19868);let l={BASE_URL:"http://127.0.0.1:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}},i=s.A.create({baseURL:l.BASE_URL,timeout:l.TIMEOUT,headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>{let a=localStorage.getItem("admin_token");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),i.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){let{status:a,data:r}=e.response;switch(a){case 401:t.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:t.Ay.error("没有权限访问该资源");break;case 404:t.Ay.error("请求的资源不存在");break;case 500:t.Ay.error("服务器内部错误");break;default:t.Ay.error((null==r?void 0:r.message)||"请求失败")}}else e.request?t.Ay.error("网络连接失败，请检查网络"):t.Ay.error("请求配置错误");return Promise.reject(e)});let n={get:(e,a)=>i.get(e,a),post:(e,a,r)=>i.post(e,a,r),put:(e,a,r)=>i.put(e,a,r),patch:(e,a,r)=>i.patch(e,a,r),delete:(e,a)=>i.delete(e,a)}},45561:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>F});var s=r(95155),t=r(12115),l=r(35695),i=r(97605),n=r(56020),c=r(20778),d=r(44670),o=r(19868),h=r(16467),p=r(95108),u=r(30662),x=r(505),g=r(19361),A=r(74947),m=r(12320),j=r(10642),y=r(13324),v=r(81730),f=r(44318),b=r(19558),w=r(73884);let{Title:I,Text:S}=i.A,{TextArea:C}=n.A,{Option:k}=c.A;function F(){let e=(0,l.useParams)(),a=(0,l.useRouter)(),[r,i]=(0,t.useState)(null),[F,U]=(0,t.useState)(!0),[_,R]=(0,t.useState)(!1),[E]=d.A.useForm(),L=e.id,B=async()=>{if(L){U(!0);try{let e=await w.Dw.getShareConfigById(L);i(e),E.setFieldsValue({name:e.name,title:e.title,path:e.path,imageUrl:e.imageUrl,description:e.description,type:e.type,isActive:e.isActive,sortOrder:e.sortOrder})}catch(e){o.Ay.error("获取分享配置详情失败"),console.error("获取分享配置详情失败:",e)}finally{U(!1)}}};(0,t.useEffect)(()=>{B()},[L]);let P=()=>{a.push("/shares/".concat(L))},q=async()=>{try{let e=await E.validateFields();R(!0),await w.Dw.updateShareConfig(L,e),o.Ay.success("分享配置更新成功"),a.push("/shares/".concat(L))}catch(e){if(e&&"object"==typeof e&&"errorFields"in e)o.Ay.error("请检查表单输入");else{let a=e&&"object"==typeof e&&"message"in e?e.message:"更新失败";o.Ay.error(a)}}finally{R(!1)}};return F?(0,s.jsx)("div",{style:{padding:"24px",textAlign:"center"},children:(0,s.jsx)(h.A,{size:"large"})}):r?(0,s.jsx)("div",{style:{padding:"24px"},children:(0,s.jsxs)(x.A,{children:[(0,s.jsx)("div",{style:{marginBottom:"24px"},children:(0,s.jsxs)(g.A,{justify:"space-between",align:"middle",children:[(0,s.jsx)(A.A,{children:(0,s.jsxs)(m.A,{children:[(0,s.jsx)(u.Ay,{icon:(0,s.jsx)(v.A,{}),onClick:P,children:"返回"}),(0,s.jsxs)(I,{level:3,style:{margin:0},children:[(0,s.jsx)(f.A,{style:{marginRight:"8px"}}),"编辑分享配置"]})]})}),(0,s.jsx)(A.A,{children:(0,s.jsxs)(m.A,{children:[(0,s.jsx)(u.Ay,{onClick:P,children:"取消"}),(0,s.jsx)(u.Ay,{type:"primary",icon:(0,s.jsx)(b.A,{}),loading:_,onClick:q,children:"保存"})]})})]})}),(0,s.jsxs)(d.A,{form:E,layout:"vertical",size:"large",children:[(0,s.jsxs)(g.A,{gutter:24,children:[(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(d.A.Item,{name:"name",label:"配置名称",rules:[{required:!0,message:"请输入配置名称"}],children:(0,s.jsx)(n.A,{placeholder:"请输入配置名称"})})}),(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(d.A.Item,{name:"type",label:"分享类型",rules:[{required:!0,message:"请选择分享类型"}],children:(0,s.jsx)(c.A,{placeholder:"请选择分享类型",children:w.WS.map(e=>(0,s.jsx)(k,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:e.label}),(0,s.jsx)(S,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})})})]}),(0,s.jsx)(d.A.Item,{name:"title",label:"分享标题",rules:[{required:!0,message:"请输入分享标题"}],children:(0,s.jsx)(n.A,{placeholder:"请输入分享标题"})}),(0,s.jsx)(d.A.Item,{name:"path",label:"分享路径",rules:[{required:!0,message:"请输入分享路径"}],children:(0,s.jsx)(c.A,{placeholder:"请选择或输入分享路径",mode:"tags",allowClear:!0,children:w.cm.map(e=>(0,s.jsx)(k,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:e.label}),(0,s.jsx)(S,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})},e.value))})}),(0,s.jsx)(d.A.Item,{name:"imageUrl",label:"分享图片URL",rules:[{type:"url",message:"请输入有效的URL"}],children:(0,s.jsx)(n.A,{placeholder:"请输入分享图片URL（可选）"})}),(0,s.jsx)(d.A.Item,{name:"description",label:"分享描述",children:(0,s.jsx)(C,{placeholder:"请输入分享描述（可选）",rows:4,maxLength:200,showCount:!0})}),(0,s.jsxs)(g.A,{gutter:24,children:[(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(d.A.Item,{name:"sortOrder",label:"排序权重",rules:[{required:!0,message:"请输入排序权重"}],children:(0,s.jsx)(j.A,{min:1,max:999,placeholder:"排序权重",style:{width:"100%"}})})}),(0,s.jsx)(A.A,{span:12,children:(0,s.jsx)(d.A.Item,{name:"isActive",label:"启用状态",valuePropName:"checked",children:(0,s.jsx)(y.A,{checkedChildren:"启用",unCheckedChildren:"禁用",size:"default"})})})]})]}),(0,s.jsx)(p.A,{message:"编辑提示",description:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{children:"• 分享标题和路径是必填项，将直接影响小程序的分享效果"}),(0,s.jsx)("p",{children:"• 分享图片建议使用5:4比例，推荐尺寸500x400px"}),(0,s.jsxs)("p",{children:["• 路径中可以使用变量，如 ","{levelId}"," 会被替换为实际的关卡ID"]}),(0,s.jsx)("p",{children:"• 默认类型的配置不能删除，但可以禁用"})]}),type:"info",showIcon:!0,style:{marginTop:"24px"}})]})}):(0,s.jsx)("div",{style:{padding:"24px"},children:(0,s.jsx)(p.A,{message:"分享配置不存在",description:"请检查URL是否正确，或者该配置已被删除。",type:"error",showIcon:!0,action:(0,s.jsx)(u.Ay,{size:"small",onClick:()=>a.push("/shares"),children:"返回列表"})})})}},73884:(e,a,r)=>{"use strict";r.d(a,{Dw:()=>t,WS:()=>l,cm:()=>i});var s=r(29799);class t{static async getAllShareConfigs(){return(await s.F.get("/api/v1/share")).data}static async getActiveShareConfigs(){return(await s.F.get("/api/v1/share/active")).data}static async getDefaultShareConfig(){return(await s.F.get("/api/v1/share/default")).data}static async getShareConfigByType(e){return(await s.F.get("/api/v1/share/type/".concat(e))).data}static async getShareConfigById(e){return(await s.F.get("/api/v1/share/".concat(e))).data}static async createShareConfig(e){return(await s.F.post("/api/v1/share",e)).data}static async updateShareConfig(e,a){return(await s.F.patch("/api/v1/share/".concat(e),a)).data}static async toggleShareConfig(e){return(await s.F.put("/api/v1/share/".concat(e,"/toggle"))).data}static async deleteShareConfig(e){return(await s.F.delete("/api/v1/share/".concat(e))).data}}let l=[{value:"default",label:"默认分享",description:"通用分享，适用于首页、游戏页等"},{value:"result",label:"结果分享",description:"展示用户成绩的分享"},{value:"level",label:"关卡分享",description:"邀请好友挑战特定关卡"},{value:"achievement",label:"成就分享",description:"展示用户获得的成就"},{value:"custom",label:"自定义分享",description:"特殊活动或自定义场景"}],i=[{label:"首页",value:"/pages/index/index",description:"小程序首页"},{label:"游戏页",value:"/pages/game/game",description:"游戏主页面"},{label:"关卡页",value:"/pages/level/level?id={levelId}",description:"特定关卡页面，{levelId}会被替换为实际关卡ID"},{label:"结果页",value:"/pages/result/result?score={score}",description:"结果展示页面，{score}会被替换为实际分数"},{label:"排行榜",value:"/pages/rank/rank",description:"排行榜页面"}]}},e=>{var a=a=>e(e.s=a);e.O(0,[8340,547,7469,7497,44,6312,778,7605,4670,545,4741,8441,1684,7358],()=>a(14874)),_N_E=e.O()}]);