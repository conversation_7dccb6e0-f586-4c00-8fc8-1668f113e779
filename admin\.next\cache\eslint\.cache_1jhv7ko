[{"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\dashboard\\page.tsx": "1", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx": "2", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\create\\page.tsx": "3", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\page.tsx": "4", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\[id]\\edit\\page.tsx": "5", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\payment-orders\\page.tsx": "6", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\phrases\\page.tsx": "7", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\settings\\page.tsx": "8", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\page.tsx": "9", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\edit\\page.tsx": "10", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\page.tsx": "11", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\create\\page.tsx": "12", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\page.tsx": "13", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\users\\page.tsx": "14", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\vip-packages\\page.tsx": "15", "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\vip-users\\page.tsx": "16", "D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx": "17", "D:\\web\\other\\yyddp\\admin\\src\\app\\login\\page.tsx": "18", "D:\\web\\other\\yyddp\\admin\\src\\app\\page.tsx": "19", "D:\\web\\other\\yyddp\\admin\\src\\config\\api.ts": "20", "D:\\web\\other\\yyddp\\admin\\src\\examples\\apiUsage.ts": "21", "D:\\web\\other\\yyddp\\admin\\src\\services\\authService.ts": "22", "D:\\web\\other\\yyddp\\admin\\src\\services\\index.ts": "23", "D:\\web\\other\\yyddp\\admin\\src\\services\\levelService.ts": "24", "D:\\web\\other\\yyddp\\admin\\src\\services\\phraseService.ts": "25", "D:\\web\\other\\yyddp\\admin\\src\\services\\request.ts": "26", "D:\\web\\other\\yyddp\\admin\\src\\services\\settingsService.ts": "27", "D:\\web\\other\\yyddp\\admin\\src\\services\\shareService.ts": "28", "D:\\web\\other\\yyddp\\admin\\src\\services\\thesaurusService.ts": "29", "D:\\web\\other\\yyddp\\admin\\src\\services\\userService.ts": "30", "D:\\web\\other\\yyddp\\admin\\src\\services\\vipService.ts": "31", "D:\\web\\other\\yyddp\\admin\\src\\types\\level.ts": "32", "D:\\web\\other\\yyddp\\admin\\src\\types\\share.ts": "33", "D:\\web\\other\\yyddp\\admin\\src\\types\\thesaurus.ts": "34"}, {"size": 9822, "mtime": 1751193919418, "results": "35", "hashOfConfig": "36"}, {"size": 3955, "mtime": 1751561132417, "results": "37", "hashOfConfig": "36"}, {"size": 5115, "mtime": 1751534343180, "results": "38", "hashOfConfig": "36"}, {"size": 7779, "mtime": 1751193919421, "results": "39", "hashOfConfig": "36"}, {"size": 7044, "mtime": 1751193919420, "results": "40", "hashOfConfig": "36"}, {"size": 15526, "mtime": 1751193919422, "results": "41", "hashOfConfig": "36"}, {"size": 6738, "mtime": 1751193919422, "results": "42", "hashOfConfig": "36"}, {"size": 7751, "mtime": 1751193919424, "results": "43", "hashOfConfig": "36"}, {"size": 13415, "mtime": 1751193919426, "results": "44", "hashOfConfig": "36"}, {"size": 9056, "mtime": 1751193919425, "results": "45", "hashOfConfig": "36"}, {"size": 9835, "mtime": 1751193919426, "results": "46", "hashOfConfig": "36"}, {"size": 1835, "mtime": 1751193919427, "results": "47", "hashOfConfig": "36"}, {"size": 3669, "mtime": 1751193919427, "results": "48", "hashOfConfig": "36"}, {"size": 15087, "mtime": 1751193919428, "results": "49", "hashOfConfig": "36"}, {"size": 13480, "mtime": 1751193919429, "results": "50", "hashOfConfig": "36"}, {"size": 15350, "mtime": 1751193919429, "results": "51", "hashOfConfig": "36"}, {"size": 681, "mtime": 1751193919431, "results": "52", "hashOfConfig": "36"}, {"size": 2246, "mtime": 1751193919432, "results": "53", "hashOfConfig": "36"}, {"size": 747, "mtime": 1751193919433, "results": "54", "hashOfConfig": "36"}, {"size": 632, "mtime": 1751560339300, "results": "55", "hashOfConfig": "36"}, {"size": 4658, "mtime": 1751193919435, "results": "56", "hashOfConfig": "36"}, {"size": 1046, "mtime": 1751193919436, "results": "57", "hashOfConfig": "36"}, {"size": 923, "mtime": 1751193919436, "results": "58", "hashOfConfig": "36"}, {"size": 2992, "mtime": 1751193919436, "results": "59", "hashOfConfig": "36"}, {"size": 1484, "mtime": 1751193919437, "results": "60", "hashOfConfig": "36"}, {"size": 2858, "mtime": 1751561333846, "results": "61", "hashOfConfig": "36"}, {"size": 3723, "mtime": 1751193919438, "results": "62", "hashOfConfig": "36"}, {"size": 3488, "mtime": 1751193919438, "results": "63", "hashOfConfig": "36"}, {"size": 2543, "mtime": 1751193919439, "results": "64", "hashOfConfig": "36"}, {"size": 3960, "mtime": 1751193919439, "results": "65", "hashOfConfig": "36"}, {"size": 5047, "mtime": 1751193919439, "results": "66", "hashOfConfig": "36"}, {"size": 247, "mtime": 1751193919443, "results": "67", "hashOfConfig": "36"}, {"size": 1664, "mtime": 1751193919444, "results": "68", "hashOfConfig": "36"}, {"size": 576, "mtime": 1751193919444, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sgjpsj", {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\dashboard\\page.tsx", [], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx", [], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\create\\page.tsx", ["172", "173", "174", "175", "176", "177"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\page.tsx", ["178", "179", "180", "181", "182", "183"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\[id]\\edit\\page.tsx", ["184", "185", "186", "187", "188", "189"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\payment-orders\\page.tsx", ["190", "191", "192", "193", "194"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\phrases\\page.tsx", ["195", "196", "197", "198"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\settings\\page.tsx", ["199"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\page.tsx", [], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\edit\\page.tsx", ["200"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\page.tsx", ["201"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\create\\page.tsx", ["202"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\page.tsx", ["203", "204"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\users\\page.tsx", [], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\vip-packages\\page.tsx", [], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\vip-users\\page.tsx", ["205", "206", "207", "208", "209"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx", [], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\login\\page.tsx", ["210"], [], "D:\\web\\other\\yyddp\\admin\\src\\app\\page.tsx", [], [], "D:\\web\\other\\yyddp\\admin\\src\\config\\api.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\examples\\apiUsage.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\authService.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\index.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\levelService.ts", ["211", "212"], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\phraseService.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\request.ts", ["213", "214", "215", "216", "217", "218", "219", "220"], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\settingsService.ts", ["221"], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\shareService.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\thesaurusService.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\userService.ts", ["222"], [], "D:\\web\\other\\yyddp\\admin\\src\\services\\vipService.ts", ["223", "224", "225", "226"], [], "D:\\web\\other\\yyddp\\admin\\src\\types\\level.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\types\\share.ts", [], [], "D:\\web\\other\\yyddp\\admin\\src\\types\\thesaurus.ts", [], [], {"ruleId": "227", "severity": 1, "message": "228", "line": 6, "column": 24, "nodeType": null, "messageId": "229", "endLine": 6, "endColumn": 40}, {"ruleId": "227", "severity": 1, "message": "230", "line": 6, "column": 65, "nodeType": null, "messageId": "229", "endLine": 6, "endColumn": 74}, {"ruleId": "227", "severity": 1, "message": "231", "line": 23, "column": 14, "nodeType": null, "messageId": "229", "endLine": 23, "endColumn": 19}, {"ruleId": "227", "severity": 1, "message": "231", "line": 33, "column": 14, "nodeType": null, "messageId": "229", "endLine": 33, "endColumn": 19}, {"ruleId": "232", "severity": 1, "message": "233", "line": 44, "column": 39, "nodeType": "234", "messageId": "235", "endLine": 44, "endColumn": 42, "suggestions": "236"}, {"ruleId": "227", "severity": 1, "message": "231", "line": 64, "column": 14, "nodeType": null, "messageId": "229", "endLine": 64, "endColumn": 19}, {"ruleId": "227", "severity": 1, "message": "231", "line": 28, "column": 14, "nodeType": null, "messageId": "229", "endLine": 28, "endColumn": 19}, {"ruleId": "227", "severity": 1, "message": "231", "line": 40, "column": 14, "nodeType": null, "messageId": "229", "endLine": 40, "endColumn": 19}, {"ruleId": "237", "severity": 1, "message": "238", "line": 48, "column": 6, "nodeType": "239", "endLine": 48, "endColumn": 24, "suggestions": "240"}, {"ruleId": "227", "severity": 1, "message": "231", "line": 57, "column": 14, "nodeType": null, "messageId": "229", "endLine": 57, "endColumn": 19}, {"ruleId": "232", "severity": 1, "message": "233", "line": 78, "column": 56, "nodeType": "234", "messageId": "235", "endLine": 78, "endColumn": 59, "suggestions": "241"}, {"ruleId": "227", "severity": 1, "message": "231", "line": 88, "column": 14, "nodeType": null, "messageId": "229", "endLine": 88, "endColumn": 19}, {"ruleId": "227", "severity": 1, "message": "231", "line": 28, "column": 14, "nodeType": null, "messageId": "229", "endLine": 28, "endColumn": 19}, {"ruleId": "227", "severity": 1, "message": "231", "line": 38, "column": 14, "nodeType": null, "messageId": "229", "endLine": 38, "endColumn": 19}, {"ruleId": "227", "severity": 1, "message": "231", "line": 57, "column": 14, "nodeType": null, "messageId": "229", "endLine": 57, "endColumn": 19}, {"ruleId": "237", "severity": 1, "message": "242", "line": 77, "column": 6, "nodeType": "239", "endLine": 77, "endColumn": 15, "suggestions": "243"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 80, "column": 39, "nodeType": "234", "messageId": "235", "endLine": 80, "endColumn": 42, "suggestions": "244"}, {"ruleId": "227", "severity": 1, "message": "231", "line": 101, "column": 14, "nodeType": null, "messageId": "229", "endLine": 101, "endColumn": 19}, {"ruleId": "227", "severity": 1, "message": "245", "line": 5, "column": 39, "nodeType": null, "messageId": "229", "endLine": 5, "endColumn": 53}, {"ruleId": "232", "severity": 1, "message": "233", "line": 78, "column": 21, "nodeType": "234", "messageId": "235", "endLine": 78, "endColumn": 24, "suggestions": "246"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 106, "column": 21, "nodeType": "234", "messageId": "235", "endLine": 106, "endColumn": 24, "suggestions": "247"}, {"ruleId": "237", "severity": 1, "message": "248", "line": 122, "column": 6, "nodeType": "239", "endLine": 122, "endColumn": 43, "suggestions": "249"}, {"ruleId": "237", "severity": 1, "message": "248", "line": 127, "column": 6, "nodeType": "239", "endLine": 127, "endColumn": 8, "suggestions": "250"}, {"ruleId": "227", "severity": 1, "message": "231", "line": 24, "column": 14, "nodeType": null, "messageId": "229", "endLine": 24, "endColumn": 19}, {"ruleId": "232", "severity": 1, "message": "233", "line": 36, "column": 39, "nodeType": "234", "messageId": "235", "endLine": 36, "endColumn": 42, "suggestions": "251"}, {"ruleId": "227", "severity": 1, "message": "231", "line": 56, "column": 14, "nodeType": null, "messageId": "229", "endLine": 56, "endColumn": 19}, {"ruleId": "227", "severity": 1, "message": "231", "line": 67, "column": 14, "nodeType": null, "messageId": "229", "endLine": 67, "endColumn": 19}, {"ruleId": "237", "severity": 1, "message": "252", "line": 125, "column": 6, "nodeType": "239", "endLine": 125, "endColumn": 8, "suggestions": "253"}, {"ruleId": "237", "severity": 1, "message": "254", "line": 73, "column": 6, "nodeType": "239", "endLine": 73, "endColumn": 15, "suggestions": "255"}, {"ruleId": "237", "severity": 1, "message": "254", "line": 59, "column": 6, "nodeType": "239", "endLine": 59, "endColumn": 15, "suggestions": "256"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 22, "column": 21, "nodeType": "234", "messageId": "235", "endLine": 22, "endColumn": 24, "suggestions": "257"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 20, "column": 21, "nodeType": "234", "messageId": "235", "endLine": 20, "endColumn": 24, "suggestions": "258"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 37, "column": 21, "nodeType": "234", "messageId": "235", "endLine": 37, "endColumn": 24, "suggestions": "259"}, {"ruleId": "227", "severity": 1, "message": "260", "line": 4, "column": 107, "nodeType": null, "messageId": "229", "endLine": 4, "endColumn": 113}, {"ruleId": "227", "severity": 1, "message": "245", "line": 5, "column": 39, "nodeType": null, "messageId": "229", "endLine": 5, "endColumn": 53}, {"ruleId": "227", "severity": 1, "message": "261", "line": 5, "column": 98, "nodeType": null, "messageId": "229", "endLine": 5, "endColumn": 112}, {"ruleId": "232", "severity": 1, "message": "233", "line": 65, "column": 21, "nodeType": "234", "messageId": "235", "endLine": 65, "endColumn": 24, "suggestions": "262"}, {"ruleId": "237", "severity": 1, "message": "263", "line": 108, "column": 6, "nodeType": "239", "endLine": 108, "endColumn": 40, "suggestions": "264"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 26, "column": 21, "nodeType": "234", "messageId": "235", "endLine": 26, "endColumn": 24, "suggestions": "265"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 94, "column": 66, "nodeType": "234", "messageId": "235", "endLine": 94, "endColumn": 69, "suggestions": "266"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 95, "column": 55, "nodeType": "234", "messageId": "235", "endLine": 95, "endColumn": 58, "suggestions": "267"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 74, "column": 13, "nodeType": "234", "messageId": "235", "endLine": 74, "endColumn": 16, "suggestions": "268"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 79, "column": 14, "nodeType": "234", "messageId": "235", "endLine": 79, "endColumn": 17, "suggestions": "269"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 79, "column": 39, "nodeType": "234", "messageId": "235", "endLine": 79, "endColumn": 42, "suggestions": "270"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 84, "column": 13, "nodeType": "234", "messageId": "235", "endLine": 84, "endColumn": 16, "suggestions": "271"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 84, "column": 38, "nodeType": "234", "messageId": "235", "endLine": 84, "endColumn": 41, "suggestions": "272"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 89, "column": 15, "nodeType": "234", "messageId": "235", "endLine": 89, "endColumn": 18, "suggestions": "273"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 89, "column": 40, "nodeType": "234", "messageId": "235", "endLine": 89, "endColumn": 43, "suggestions": "274"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 94, "column": 16, "nodeType": "234", "messageId": "235", "endLine": 94, "endColumn": 19, "suggestions": "275"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 125, "column": 43, "nodeType": "234", "messageId": "235", "endLine": 125, "endColumn": 46, "suggestions": "276"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 75, "column": 36, "nodeType": "234", "messageId": "235", "endLine": 75, "endColumn": 39, "suggestions": "277"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 162, "column": 24, "nodeType": "234", "messageId": "235", "endLine": 162, "endColumn": 27, "suggestions": "278"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 171, "column": 38, "nodeType": "234", "messageId": "235", "endLine": 171, "endColumn": 41, "suggestions": "279"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 177, "column": 62, "nodeType": "234", "messageId": "235", "endLine": 177, "endColumn": 65, "suggestions": "280"}, {"ruleId": "281", "severity": 1, "message": "282", "line": 187, "column": 1, "nodeType": "283", "endLine": 191, "endColumn": 3}, "@typescript-eslint/no-unused-vars", "'thesaurusService' is defined but never used.", "unusedVar", "'Thesaurus' is defined but never used.", "'error' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["284", "285"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchLevels'. Either include it or remove the dependency array.", "ArrayExpression", ["286"], ["287", "288"], "React Hook useEffect has a missing dependency: 'fetchCurrentLevel'. Either include it or remove the dependency array.", ["289"], ["290", "291"], "'SearchOutlined' is defined but never used.", ["292", "293"], ["294", "295"], "React Hook useEffect has missing dependencies: 'fetchOrders' and 'fetchStats'. Either include them or remove the dependency array.", ["296"], ["297"], ["298", "299"], "React Hook useEffect has a missing dependency: 'fetchConfig'. Either include it or remove the dependency array.", ["300"], "React Hook useEffect has a missing dependency: 'fetchShareDetail'. Either include it or remove the dependency array.", ["301"], ["302"], ["303", "304"], ["305", "306"], ["307", "308"], "'Switch' is defined but never used.", "'TrophyOutlined' is defined but never used.", ["309", "310"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["311"], ["312", "313"], ["314", "315"], ["316", "317"], ["318", "319"], ["320", "321"], ["322", "323"], ["324", "325"], ["326", "327"], ["328", "329"], ["330", "331"], ["332", "333"], ["334", "335"], ["336", "337"], ["338", "339"], ["340", "341"], ["342", "343"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"messageId": "344", "fix": "345", "desc": "346"}, {"messageId": "347", "fix": "348", "desc": "349"}, {"desc": "350", "fix": "351"}, {"messageId": "344", "fix": "352", "desc": "346"}, {"messageId": "347", "fix": "353", "desc": "349"}, {"desc": "354", "fix": "355"}, {"messageId": "344", "fix": "356", "desc": "346"}, {"messageId": "347", "fix": "357", "desc": "349"}, {"messageId": "344", "fix": "358", "desc": "346"}, {"messageId": "347", "fix": "359", "desc": "349"}, {"messageId": "344", "fix": "360", "desc": "346"}, {"messageId": "347", "fix": "361", "desc": "349"}, {"desc": "362", "fix": "363"}, {"desc": "364", "fix": "365"}, {"messageId": "344", "fix": "366", "desc": "346"}, {"messageId": "347", "fix": "367", "desc": "349"}, {"desc": "368", "fix": "369"}, {"desc": "370", "fix": "371"}, {"desc": "370", "fix": "372"}, {"messageId": "344", "fix": "373", "desc": "346"}, {"messageId": "347", "fix": "374", "desc": "349"}, {"messageId": "344", "fix": "375", "desc": "346"}, {"messageId": "347", "fix": "376", "desc": "349"}, {"messageId": "344", "fix": "377", "desc": "346"}, {"messageId": "347", "fix": "378", "desc": "349"}, {"messageId": "344", "fix": "379", "desc": "346"}, {"messageId": "347", "fix": "380", "desc": "349"}, {"desc": "381", "fix": "382"}, {"messageId": "344", "fix": "383", "desc": "346"}, {"messageId": "347", "fix": "384", "desc": "349"}, {"messageId": "344", "fix": "385", "desc": "346"}, {"messageId": "347", "fix": "386", "desc": "349"}, {"messageId": "344", "fix": "387", "desc": "346"}, {"messageId": "347", "fix": "388", "desc": "349"}, {"messageId": "344", "fix": "389", "desc": "346"}, {"messageId": "347", "fix": "390", "desc": "349"}, {"messageId": "344", "fix": "391", "desc": "346"}, {"messageId": "347", "fix": "392", "desc": "349"}, {"messageId": "344", "fix": "393", "desc": "346"}, {"messageId": "347", "fix": "394", "desc": "349"}, {"messageId": "344", "fix": "395", "desc": "346"}, {"messageId": "347", "fix": "396", "desc": "349"}, {"messageId": "344", "fix": "397", "desc": "346"}, {"messageId": "347", "fix": "398", "desc": "349"}, {"messageId": "344", "fix": "399", "desc": "346"}, {"messageId": "347", "fix": "400", "desc": "349"}, {"messageId": "344", "fix": "401", "desc": "346"}, {"messageId": "347", "fix": "402", "desc": "349"}, {"messageId": "344", "fix": "403", "desc": "346"}, {"messageId": "347", "fix": "404", "desc": "349"}, {"messageId": "344", "fix": "405", "desc": "346"}, {"messageId": "347", "fix": "406", "desc": "349"}, {"messageId": "344", "fix": "407", "desc": "346"}, {"messageId": "347", "fix": "408", "desc": "349"}, {"messageId": "344", "fix": "409", "desc": "346"}, {"messageId": "347", "fix": "410", "desc": "349"}, {"messageId": "344", "fix": "411", "desc": "346"}, {"messageId": "347", "fix": "412", "desc": "349"}, {"messageId": "344", "fix": "413", "desc": "346"}, {"messageId": "347", "fix": "414", "desc": "349"}, "suggestUnknown", {"range": "415", "text": "416"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "417", "text": "418"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [difficultyFilter, fetchLevels]", {"range": "419", "text": "420"}, {"range": "421", "text": "416"}, {"range": "422", "text": "418"}, "Update the dependencies array to be: [fetchCurrentLevel, levelId]", {"range": "423", "text": "424"}, {"range": "425", "text": "416"}, {"range": "426", "text": "418"}, {"range": "427", "text": "416"}, {"range": "428", "text": "418"}, {"range": "429", "text": "416"}, {"range": "430", "text": "418"}, "Update the dependencies array to be: [searchText, statusFilter, dateRange, fetchOrders, fetchStats]", {"range": "431", "text": "432"}, "Update the dependencies array to be: [fetchOrders, fetchStats]", {"range": "433", "text": "434"}, {"range": "435", "text": "416"}, {"range": "436", "text": "418"}, "Update the dependencies array to be: [fetchConfig]", {"range": "437", "text": "438"}, "Update the dependencies array to be: [fetchShareDetail, shareId]", {"range": "439", "text": "440"}, {"range": "441", "text": "440"}, {"range": "442", "text": "416"}, {"range": "443", "text": "418"}, {"range": "444", "text": "416"}, {"range": "445", "text": "418"}, {"range": "446", "text": "416"}, {"range": "447", "text": "418"}, {"range": "448", "text": "416"}, {"range": "449", "text": "418"}, "Update the dependencies array to be: [searchText, vipFilter, dateRange, fetchUsers]", {"range": "450", "text": "451"}, {"range": "452", "text": "416"}, {"range": "453", "text": "418"}, {"range": "454", "text": "416"}, {"range": "455", "text": "418"}, {"range": "456", "text": "416"}, {"range": "457", "text": "418"}, {"range": "458", "text": "416"}, {"range": "459", "text": "418"}, {"range": "460", "text": "416"}, {"range": "461", "text": "418"}, {"range": "462", "text": "416"}, {"range": "463", "text": "418"}, {"range": "464", "text": "416"}, {"range": "465", "text": "418"}, {"range": "466", "text": "416"}, {"range": "467", "text": "418"}, {"range": "468", "text": "416"}, {"range": "469", "text": "418"}, {"range": "470", "text": "416"}, {"range": "471", "text": "418"}, {"range": "472", "text": "416"}, {"range": "473", "text": "418"}, {"range": "474", "text": "416"}, {"range": "475", "text": "418"}, {"range": "476", "text": "416"}, {"range": "477", "text": "418"}, {"range": "478", "text": "416"}, {"range": "479", "text": "418"}, {"range": "480", "text": "416"}, {"range": "481", "text": "418"}, {"range": "482", "text": "416"}, {"range": "483", "text": "418"}, [1301, 1304], "unknown", [1301, 1304], "never", [1533, 1551], "[difficultyFilter, fetchLevels]", [2598, 2601], [2598, 2601], [2166, 2175], "[fetchCurrentLevel, levelId]", [2232, 2235], [2232, 2235], [2251, 2254], [2251, 2254], [2987, 2990], [2987, 2990], [3394, 3431], "[searchText, statusFilter, dateRange, fetchOrders, fetchStats]", [3502, 3504], "[fetchOrders, fetchStats]", [1130, 1133], [1130, 1133], [3138, 3140], "[fetchConfig]", [1797, 1806], "[fetchShareDetail, shareId]", [1341, 1350], [740, 743], [740, 743], [826, 829], [826, 829], [1285, 1288], [1285, 1288], [1891, 1894], [1891, 1894], [3163, 3197], "[searchText, vip<PERSON><PERSON>er, dateRange, fetchUsers]", [913, 916], [913, 916], [2559, 2562], [2559, 2562], [2628, 2631], [2628, 2631], [1755, 1758], [1755, 1758], [1909, 1912], [1909, 1912], [1934, 1937], [1934, 1937], [2080, 2083], [2080, 2083], [2105, 2108], [2105, 2108], [2254, 2257], [2254, 2257], [2279, 2282], [2279, 2282], [2432, 2435], [2432, 2435], [3174, 3177], [3174, 3177], [1697, 1700], [1697, 1700], [4064, 4067], [4064, 4067], [4324, 4327], [4324, 4327], [4504, 4507], [4504, 4507]]