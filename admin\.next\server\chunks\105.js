"use strict";exports.id=105,exports.ids=[105],exports.modules={63105:(e,t,n)=>{n.d(t,{A:()=>er});var r=n(43210),a=n.n(r),o=n(69662),l=n.n(o),i=n(219),u=n(95243),c=n(78651),s=n(83192),d=n(82853),f=n(26165),v=n(28344),g=n(25725),m=n(70393),h=n(80828),b=n(78135),p=n(51215);function k(e,t,n,r){var a=(t-n)/(r-n),o={};switch(e){case"rtl":o.right="".concat(100*a,"%"),o.transform="translateX(50%)";break;case"btt":o.bottom="".concat(100*a,"%"),o.transform="translateY(50%)";break;case"ttb":o.top="".concat(100*a,"%"),o.transform="translateY(-50%)";break;default:o.left="".concat(100*a,"%"),o.transform="translateX(-50%)"}return o}function A(e,t){return Array.isArray(e)?e[t]:e}var C=n(2291),x=r.createContext({min:0,max:0,direction:"ltr",step:1,includedStart:0,includedEnd:0,tabIndex:0,keyboard:!0,styles:{},classNames:{}}),y=r.createContext({}),E=["prefixCls","value","valueIndex","onStartMove","onDelete","style","render","dragging","draggingDelete","onOffsetChange","onChangeComplete","onFocus","onMouseEnter"],S=r.forwardRef(function(e,t){var n,a=e.prefixCls,o=e.value,c=e.valueIndex,s=e.onStartMove,d=e.onDelete,f=e.style,v=e.render,g=e.dragging,m=e.draggingDelete,p=e.onOffsetChange,y=e.onChangeComplete,S=e.onFocus,$=e.onMouseEnter,M=(0,b.A)(e,E),w=r.useContext(x),O=w.min,B=w.max,D=w.direction,R=w.disabled,j=w.keyboard,F=w.range,P=w.tabIndex,H=w.ariaLabelForHandle,N=w.ariaLabelledByForHandle,I=w.ariaRequired,L=w.ariaValueTextFormatterForHandle,z=w.styles,q=w.classNames,T="".concat(a,"-handle"),V=function(e){R||s(e,c)},X=k(D,o,O,B),W={};null!==c&&(W={tabIndex:R?null:A(P,c),role:"slider","aria-valuemin":O,"aria-valuemax":B,"aria-valuenow":o,"aria-disabled":R,"aria-label":A(H,c),"aria-labelledby":A(N,c),"aria-required":A(I,c),"aria-valuetext":null==(n=A(L,c))?void 0:n(o),"aria-orientation":"ltr"===D||"rtl"===D?"horizontal":"vertical",onMouseDown:V,onTouchStart:V,onFocus:function(e){null==S||S(e,c)},onMouseEnter:function(e){$(e,c)},onKeyDown:function(e){if(!R&&j){var t=null;switch(e.which||e.keyCode){case C.A.LEFT:t="ltr"===D||"btt"===D?-1:1;break;case C.A.RIGHT:t="ltr"===D||"btt"===D?1:-1;break;case C.A.UP:t="ttb"!==D?1:-1;break;case C.A.DOWN:t="ttb"!==D?-1:1;break;case C.A.HOME:t="min";break;case C.A.END:t="max";break;case C.A.PAGE_UP:t=2;break;case C.A.PAGE_DOWN:t=-2;break;case C.A.BACKSPACE:case C.A.DELETE:d(c)}null!==t&&(e.preventDefault(),p(t,c))}},onKeyUp:function(e){switch(e.which||e.keyCode){case C.A.LEFT:case C.A.RIGHT:case C.A.UP:case C.A.DOWN:case C.A.HOME:case C.A.END:case C.A.PAGE_UP:case C.A.PAGE_DOWN:null==y||y()}}});var Y=r.createElement("div",(0,h.A)({ref:t,className:l()(T,(0,u.A)((0,u.A)((0,u.A)({},"".concat(T,"-").concat(c+1),null!==c&&F),"".concat(T,"-dragging"),g),"".concat(T,"-dragging-delete"),m),q.handle),style:(0,i.A)((0,i.A)((0,i.A)({},X),f),z.handle)},W,M));return v&&(Y=v(Y,{index:c,prefixCls:a,value:o,dragging:g,draggingDelete:m})),Y}),$=["prefixCls","style","onStartMove","onOffsetChange","values","handleRender","activeHandleRender","draggingIndex","draggingDelete","onFocus"],M=r.forwardRef(function(e,t){var n=e.prefixCls,a=e.style,o=e.onStartMove,l=e.onOffsetChange,u=e.values,c=e.handleRender,s=e.activeHandleRender,f=e.draggingIndex,v=e.draggingDelete,g=e.onFocus,m=(0,b.A)(e,$),k=r.useRef({}),C=r.useState(!1),x=(0,d.A)(C,2),y=x[0],E=x[1],M=r.useState(-1),w=(0,d.A)(M,2),O=w[0],B=w[1],D=function(e){B(e),E(!0)};r.useImperativeHandle(t,function(){return{focus:function(e){var t;null==(t=k.current[e])||t.focus()},hideHelp:function(){(0,p.flushSync)(function(){E(!1)})}}});var R=(0,i.A)({prefixCls:n,onStartMove:o,onOffsetChange:l,render:c,onFocus:function(e,t){D(t),null==g||g(e)},onMouseEnter:function(e,t){D(t)}},m);return r.createElement(r.Fragment,null,u.map(function(e,t){var n=f===t;return r.createElement(S,(0,h.A)({ref:function(e){e?k.current[t]=e:delete k.current[t]},dragging:n,draggingDelete:n&&v,style:A(a,t),key:t,value:e,valueIndex:t},R))}),s&&y&&r.createElement(S,(0,h.A)({key:"a11y"},R,{value:u[O],valueIndex:null,dragging:-1!==f,draggingDelete:v,render:s,style:{pointerEvents:"none"},tabIndex:null,"aria-hidden":!0})))});let w=function(e){var t=e.prefixCls,n=e.style,a=e.children,o=e.value,c=e.onClick,s=r.useContext(x),d=s.min,f=s.max,v=s.direction,g=s.includedStart,m=s.includedEnd,h=s.included,b="".concat(t,"-text"),p=k(v,o,d,f);return r.createElement("span",{className:l()(b,(0,u.A)({},"".concat(b,"-active"),h&&g<=o&&o<=m)),style:(0,i.A)((0,i.A)({},p),n),onMouseDown:function(e){e.stopPropagation()},onClick:function(){c(o)}},a)},O=function(e){var t=e.prefixCls,n=e.marks,a=e.onClick,o="".concat(t,"-mark");return n.length?r.createElement("div",{className:o},n.map(function(e){var t=e.value,n=e.style,l=e.label;return r.createElement(w,{key:t,prefixCls:o,style:n,value:t,onClick:a},l)})):null},B=function(e){var t=e.prefixCls,n=e.value,a=e.style,o=e.activeStyle,c=r.useContext(x),s=c.min,d=c.max,f=c.direction,v=c.included,g=c.includedStart,m=c.includedEnd,h="".concat(t,"-dot"),b=v&&g<=n&&n<=m,p=(0,i.A)((0,i.A)({},k(f,n,s,d)),"function"==typeof a?a(n):a);return b&&(p=(0,i.A)((0,i.A)({},p),"function"==typeof o?o(n):o)),r.createElement("span",{className:l()(h,(0,u.A)({},"".concat(h,"-active"),b)),style:p})},D=function(e){var t=e.prefixCls,n=e.marks,a=e.dots,o=e.style,l=e.activeStyle,i=r.useContext(x),u=i.min,c=i.max,s=i.step,d=r.useMemo(function(){var e=new Set;if(n.forEach(function(t){e.add(t.value)}),a&&null!==s)for(var t=u;t<=c;)e.add(t),t+=s;return Array.from(e)},[u,c,s,a,n]);return r.createElement("div",{className:"".concat(t,"-step")},d.map(function(e){return r.createElement(B,{prefixCls:t,key:e,value:e,style:o,activeStyle:l})}))},R=function(e){var t=e.prefixCls,n=e.style,a=e.start,o=e.end,c=e.index,s=e.onStartMove,d=e.replaceCls,f=r.useContext(x),v=f.direction,g=f.min,m=f.max,h=f.disabled,b=f.range,p=f.classNames,k="".concat(t,"-track"),A=(a-g)/(m-g),C=(o-g)/(m-g),y=function(e){!h&&s&&s(e,-1)},E={};switch(v){case"rtl":E.right="".concat(100*A,"%"),E.width="".concat(100*C-100*A,"%");break;case"btt":E.bottom="".concat(100*A,"%"),E.height="".concat(100*C-100*A,"%");break;case"ttb":E.top="".concat(100*A,"%"),E.height="".concat(100*C-100*A,"%");break;default:E.left="".concat(100*A,"%"),E.width="".concat(100*C-100*A,"%")}var S=d||l()(k,(0,u.A)((0,u.A)({},"".concat(k,"-").concat(c+1),null!==c&&b),"".concat(t,"-track-draggable"),s),p.track);return r.createElement("div",{className:S,style:(0,i.A)((0,i.A)({},E),n),onMouseDown:y,onTouchStart:y})},j=function(e){var t=e.prefixCls,n=e.style,a=e.values,o=e.startPoint,u=e.onStartMove,c=r.useContext(x),s=c.included,d=c.range,f=c.min,v=c.styles,g=c.classNames,m=r.useMemo(function(){if(!d){if(0===a.length)return[];var e=null!=o?o:f,t=a[0];return[{start:Math.min(e,t),end:Math.max(e,t)}]}for(var n=[],r=0;r<a.length-1;r+=1)n.push({start:a[r],end:a[r+1]});return n},[a,d,o,f]);if(!s)return null;var h=null!=m&&m.length&&(g.tracks||v.tracks)?r.createElement(R,{index:null,prefixCls:t,start:m[0].start,end:m[m.length-1].end,replaceCls:l()(g.tracks,"".concat(t,"-tracks")),style:v.tracks}):null;return r.createElement(r.Fragment,null,h,m.map(function(e,a){var o=e.start,l=e.end;return r.createElement(R,{index:a,prefixCls:t,style:(0,i.A)((0,i.A)({},A(n,a)),v.track),start:o,end:l,key:a,onStartMove:u})}))};var F=n(37262);function P(e){var t="targetTouches"in e?e.targetTouches[0]:e;return{pageX:t.pageX,pageY:t.pageY}}let H=function(e,t,n,a,o,l,i,u,s,v,g){var m=r.useState(null),h=(0,d.A)(m,2),b=h[0],p=h[1],k=r.useState(-1),A=(0,d.A)(k,2),C=A[0],x=A[1],E=r.useState(!1),S=(0,d.A)(E,2),$=S[0],M=S[1],w=r.useState(n),O=(0,d.A)(w,2),B=O[0],D=O[1],R=r.useState(n),j=(0,d.A)(R,2),H=j[0],N=j[1],I=r.useRef(null),L=r.useRef(null),z=r.useRef(null),q=r.useContext(y),T=q.onDragStart,V=q.onDragChange;(0,F.A)(function(){-1===C&&D(n)},[n,C]),r.useEffect(function(){return function(){document.removeEventListener("mousemove",I.current),document.removeEventListener("mouseup",L.current),z.current&&(z.current.removeEventListener("touchmove",I.current),z.current.removeEventListener("touchend",L.current))}},[]);var X=function(e,t,n){void 0!==t&&p(t),D(e);var r=e;n&&(r=e.filter(function(e,t){return t!==C})),i(r),V&&V({rawValues:e,deleteIndex:n?C:-1,draggingIndex:C,draggingValue:t})},W=(0,f.A)(function(e,t,n){if(-1===e){var r=H[0],i=H[H.length-1],u=t*(o-a);u=Math.min(u=Math.max(u,a-r),o-i),u=l(r+u)-r,X(H.map(function(e){return e+u}))}else{var d=(0,c.A)(B);d[e]=H[e];var f=s(d,(o-a)*t,e,"dist");X(f.values,f.value,n)}});return[C,b,$,r.useMemo(function(){var e=(0,c.A)(n).sort(function(e,t){return e-t}),t=(0,c.A)(B).sort(function(e,t){return e-t}),r={};t.forEach(function(e){r[e]=(r[e]||0)+1}),e.forEach(function(e){r[e]=(r[e]||0)-1});var a=+!!v;return Object.values(r).reduce(function(e,t){return e+Math.abs(t)},0)<=a?B:n},[n,B,v]),function(r,a,o){r.stopPropagation();var l=o||n,i=l[a];x(a),p(i),N(l),D(l),M(!1);var c=P(r),s=c.pageX,d=c.pageY,f=!1;T&&T({rawValues:l,draggingIndex:a,draggingValue:i});var m=function(n){n.preventDefault();var r,o,l=P(n),i=l.pageX,u=l.pageY,c=i-s,m=u-d,h=e.current.getBoundingClientRect(),b=h.width,p=h.height;switch(t){case"btt":r=-m/p,o=c;break;case"ttb":r=m/p,o=c;break;case"rtl":r=-c/b,o=m;break;default:r=c/b,o=m}M(f=!!v&&Math.abs(o)>130&&g<B.length),W(a,r,f)},h=function e(t){t.preventDefault(),document.removeEventListener("mouseup",e),document.removeEventListener("mousemove",m),z.current&&(z.current.removeEventListener("touchmove",I.current),z.current.removeEventListener("touchend",L.current)),I.current=null,L.current=null,z.current=null,u(f),x(-1),M(!1)};document.addEventListener("mouseup",h),document.addEventListener("mousemove",m),r.currentTarget.addEventListener("touchend",h),r.currentTarget.addEventListener("touchmove",m),I.current=m,L.current=h,z.current=r.currentTarget}]};var N=r.forwardRef(function(e,t){var n,a,o,h,b,p,k,A=e.prefixCls,C=void 0===A?"rc-slider":A,y=e.className,E=e.style,S=e.classNames,$=e.styles,w=e.id,B=e.disabled,R=void 0!==B&&B,F=e.keyboard,P=void 0===F||F,N=e.autoFocus,I=e.onFocus,L=e.onBlur,z=e.min,q=void 0===z?0:z,T=e.max,V=void 0===T?100:T,X=e.step,W=void 0===X?1:X,Y=e.value,G=e.defaultValue,U=e.range,K=e.count,_=e.onChange,J=e.onBeforeChange,Q=e.onAfterChange,Z=e.onChangeComplete,ee=e.allowCross,et=e.pushable,en=void 0!==et&&et,er=e.reverse,ea=e.vertical,eo=e.included,el=void 0===eo||eo,ei=e.startPoint,eu=e.trackStyle,ec=e.handleStyle,es=e.railStyle,ed=e.dotStyle,ef=e.activeDotStyle,ev=e.marks,eg=e.dots,em=e.handleRender,eh=e.activeHandleRender,eb=e.track,ep=e.tabIndex,ek=void 0===ep?0:ep,eA=e.ariaLabelForHandle,eC=e.ariaLabelledByForHandle,ex=e.ariaRequired,ey=e.ariaValueTextFormatterForHandle,eE=r.useRef(null),eS=r.useRef(null),e$=r.useMemo(function(){return ea?er?"ttb":"btt":er?"rtl":"ltr"},[er,ea]),eM=(0,r.useMemo)(function(){if(!0===U||!U)return[!!U,!1,!1,0];var e=U.editable,t=U.draggableTrack;return[!0,e,!e&&t,U.minCount||0,U.maxCount]},[U]),ew=(0,d.A)(eM,5),eO=ew[0],eB=ew[1],eD=ew[2],eR=ew[3],ej=ew[4],eF=r.useMemo(function(){return isFinite(q)?q:0},[q]),eP=r.useMemo(function(){return isFinite(V)?V:100},[V]),eH=r.useMemo(function(){return null!==W&&W<=0?1:W},[W]),eN=r.useMemo(function(){return"boolean"==typeof en?!!en&&eH:en>=0&&en},[en,eH]),eI=r.useMemo(function(){return Object.keys(ev||{}).map(function(e){var t=ev[e],n={value:Number(e)};return t&&"object"===(0,s.A)(t)&&!r.isValidElement(t)&&("label"in t||"style"in t)?(n.style=t.style,n.label=t.label):n.label=t,n}).filter(function(e){var t=e.label;return t||"number"==typeof t}).sort(function(e,t){return e.value-t.value})},[ev]),eL=(n=void 0===ee||ee,a=r.useCallback(function(e){return Math.max(eF,Math.min(eP,e))},[eF,eP]),o=r.useCallback(function(e){if(null!==eH){var t=eF+Math.round((a(e)-eF)/eH)*eH,n=function(e){return(String(e).split(".")[1]||"").length},r=Math.max(n(eH),n(eP),n(eF)),o=Number(t.toFixed(r));return eF<=o&&o<=eP?o:null}return null},[eH,eF,eP,a]),h=r.useCallback(function(e){var t=a(e),n=eI.map(function(e){return e.value});null!==eH&&n.push(o(e)),n.push(eF,eP);var r=n[0],l=eP-eF;return n.forEach(function(e){var n=Math.abs(t-e);n<=l&&(r=e,l=n)}),r},[eF,eP,eI,eH,a,o]),b=function e(t,n,r){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit";if("number"==typeof n){var l,i=t[r],u=i+n,s=[];eI.forEach(function(e){s.push(e.value)}),s.push(eF,eP),s.push(o(i));var d=n>0?1:-1;"unit"===a?s.push(o(i+d*eH)):s.push(o(u)),s=s.filter(function(e){return null!==e}).filter(function(e){return n<0?e<=i:e>=i}),"unit"===a&&(s=s.filter(function(e){return e!==i}));var f="unit"===a?i:u,v=Math.abs((l=s[0])-f);if(s.forEach(function(e){var t=Math.abs(e-f);t<v&&(l=e,v=t)}),void 0===l)return n<0?eF:eP;if("dist"===a)return l;if(Math.abs(n)>1){var g=(0,c.A)(t);return g[r]=l,e(g,n-d,r,a)}return l}return"min"===n?eF:"max"===n?eP:void 0},p=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",a=e[n],o=b(e,t,n,r);return{value:o,changed:o!==a}},k=function(e){return null===eN&&0===e||"number"==typeof eN&&e<eN},[h,function(e,t,r){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",o=e.map(h),l=o[r],i=b(o,t,r,a);if(o[r]=i,!1===n){var u=eN||0;r>0&&o[r-1]!==l&&(o[r]=Math.max(o[r],o[r-1]+u)),r<o.length-1&&o[r+1]!==l&&(o[r]=Math.min(o[r],o[r+1]-u))}else if("number"==typeof eN||null===eN){for(var c=r+1;c<o.length;c+=1)for(var s=!0;k(o[c]-o[c-1])&&s;){var d=p(o,1,c);o[c]=d.value,s=d.changed}for(var f=r;f>0;f-=1)for(var v=!0;k(o[f]-o[f-1])&&v;){var g=p(o,-1,f-1);o[f-1]=g.value,v=g.changed}for(var m=o.length-1;m>0;m-=1)for(var A=!0;k(o[m]-o[m-1])&&A;){var C=p(o,-1,m-1);o[m-1]=C.value,A=C.changed}for(var x=0;x<o.length-1;x+=1)for(var y=!0;k(o[x+1]-o[x])&&y;){var E=p(o,1,x+1);o[x+1]=E.value,y=E.changed}}return{value:o[r],values:o}}]),ez=(0,d.A)(eL,2),eq=ez[0],eT=ez[1],eV=(0,v.A)(G,{value:Y}),eX=(0,d.A)(eV,2),eW=eX[0],eY=eX[1],eG=r.useMemo(function(){var e=null==eW?[]:Array.isArray(eW)?eW:[eW],t=(0,d.A)(e,1)[0],n=void 0===t?eF:t,r=null===eW?[]:[n];if(eO){if(r=(0,c.A)(e),K||void 0===eW){var a,o=K>=0?K+1:2;for(r=r.slice(0,o);r.length<o;)r.push(null!=(a=r[r.length-1])?a:eF)}r.sort(function(e,t){return e-t})}return r.forEach(function(e,t){r[t]=eq(e)}),r},[eW,eO,eF,K,eq]),eU=function(e){return eO?e:e[0]},eK=(0,f.A)(function(e){var t=(0,c.A)(e).sort(function(e,t){return e-t});_&&!(0,g.A)(t,eG,!0)&&_(eU(t)),eY(t)}),e_=(0,f.A)(function(e){e&&eE.current.hideHelp();var t=eU(eG);null==Q||Q(t),(0,m.Ay)(!Q,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null==Z||Z(t)}),eJ=H(eS,e$,eG,eF,eP,eq,eK,e_,eT,eB,eR),eQ=(0,d.A)(eJ,5),eZ=eQ[0],e0=eQ[1],e1=eQ[2],e2=eQ[3],e3=eQ[4],e5=function(e,t){if(!R){var n,r,a=(0,c.A)(eG),o=0,l=0,i=eP-eF;eG.forEach(function(t,n){var r=Math.abs(e-t);r<=i&&(i=r,o=n),t<e&&(l=n)});var u=o;eB&&0!==i&&(!ej||eG.length<ej)?(a.splice(l+1,0,e),u=l+1):a[o]=e,eO&&!eG.length&&void 0===K&&a.push(e);var s=eU(a);null==J||J(s),eK(a),t?(null==(n=document.activeElement)||null==(r=n.blur)||r.call(n),eE.current.focus(u),e3(t,u,a)):(null==Q||Q(s),(0,m.Ay)(!Q,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null==Z||Z(s))}},e4=r.useState(null),e8=(0,d.A)(e4,2),e6=e8[0],e7=e8[1];r.useEffect(function(){if(null!==e6){var e=eG.indexOf(e6);e>=0&&eE.current.focus(e)}e7(null)},[e6]);var e9=r.useMemo(function(){return(!eD||null!==eH)&&eD},[eD,eH]),te=(0,f.A)(function(e,t){e3(e,t),null==J||J(eU(eG))}),tt=-1!==eZ;r.useEffect(function(){if(!tt){var e=eG.lastIndexOf(e0);eE.current.focus(e)}},[tt]);var tn=r.useMemo(function(){return(0,c.A)(e2).sort(function(e,t){return e-t})},[e2]),tr=r.useMemo(function(){return eO?[tn[0],tn[tn.length-1]]:[eF,tn[0]]},[tn,eO,eF]),ta=(0,d.A)(tr,2),to=ta[0],tl=ta[1];r.useImperativeHandle(t,function(){return{focus:function(){eE.current.focus(0)},blur:function(){var e,t=document.activeElement;null!=(e=eS.current)&&e.contains(t)&&(null==t||t.blur())}}}),r.useEffect(function(){N&&eE.current.focus(0)},[]);var ti=r.useMemo(function(){return{min:eF,max:eP,direction:e$,disabled:R,keyboard:P,step:eH,included:el,includedStart:to,includedEnd:tl,range:eO,tabIndex:ek,ariaLabelForHandle:eA,ariaLabelledByForHandle:eC,ariaRequired:ex,ariaValueTextFormatterForHandle:ey,styles:$||{},classNames:S||{}}},[eF,eP,e$,R,P,eH,el,to,tl,eO,ek,eA,eC,ex,ey,$,S]);return r.createElement(x.Provider,{value:ti},r.createElement("div",{ref:eS,className:l()(C,y,(0,u.A)((0,u.A)((0,u.A)((0,u.A)({},"".concat(C,"-disabled"),R),"".concat(C,"-vertical"),ea),"".concat(C,"-horizontal"),!ea),"".concat(C,"-with-marks"),eI.length)),style:E,onMouseDown:function(e){e.preventDefault();var t,n=eS.current.getBoundingClientRect(),r=n.width,a=n.height,o=n.left,l=n.top,i=n.bottom,u=n.right,c=e.clientX,s=e.clientY;switch(e$){case"btt":t=(i-s)/a;break;case"ttb":t=(s-l)/a;break;case"rtl":t=(u-c)/r;break;default:t=(c-o)/r}e5(eq(eF+t*(eP-eF)),e)},id:w},r.createElement("div",{className:l()("".concat(C,"-rail"),null==S?void 0:S.rail),style:(0,i.A)((0,i.A)({},es),null==$?void 0:$.rail)}),!1!==eb&&r.createElement(j,{prefixCls:C,style:eu,values:eG,startPoint:ei,onStartMove:e9?te:void 0}),r.createElement(D,{prefixCls:C,marks:eI,dots:eg,style:ed,activeStyle:ef}),r.createElement(M,{ref:eE,prefixCls:C,style:ec,values:e2,draggingIndex:eZ,draggingDelete:e1,onStartMove:te,onOffsetChange:function(e,t){if(!R){var n=eT(eG,e,t);null==J||J(eU(eG)),eK(n.values),e7(n.value)}},onFocus:I,onBlur:L,handleRender:em,activeHandleRender:eh,onChangeComplete:e_,onDelete:eB?function(e){if(!R&&eB&&!(eG.length<=eR)){var t=(0,c.A)(eG);t.splice(e,1),null==J||J(eU(t)),eK(t);var n=Math.max(0,e-1);eE.current.hideHelp(),eE.current.focus(n)}}:void 0}),r.createElement(O,{prefixCls:C,marks:eI,onClick:e5})))}),I=n(53428),L=n(57026);let z=(0,r.createContext)({});var q=n(7224),T=n(33519);let V=r.forwardRef((e,t)=>{let{open:n,draggingDelete:a,value:o}=e,l=(0,r.useRef)(null),i=n&&!a,u=(0,r.useRef)(null);function c(){I.A.cancel(u.current),u.current=null}return r.useEffect(()=>(i?u.current=(0,I.A)(()=>{var e;null==(e=l.current)||e.forceAlign(),u.current=null}):c(),c),[i,e.title,o]),r.createElement(T.A,Object.assign({ref:(0,q.K4)(l,t)},e,{open:i}))});var X=n(42411),W=n(73117),Y=n(32476),G=n(13581),U=n(60254);let K=e=>{let{componentCls:t,antCls:n,controlSize:r,dotSize:a,marginFull:o,marginPart:l,colorFillContentHover:i,handleColorDisabled:u,calc:c,handleSize:s,handleSizeHover:d,handleActiveColor:f,handleActiveOutlineColor:v,handleLineWidth:g,handleLineWidthHover:m,motionDurationMid:h}=e;return{[t]:Object.assign(Object.assign({},(0,Y.dF)(e)),{position:"relative",height:r,margin:`${(0,X.zA)(l)} ${(0,X.zA)(o)}`,padding:0,cursor:"pointer",touchAction:"none","&-vertical":{margin:`${(0,X.zA)(o)} ${(0,X.zA)(l)}`},[`${t}-rail`]:{position:"absolute",backgroundColor:e.railBg,borderRadius:e.borderRadiusXS,transition:`background-color ${h}`},[`${t}-track,${t}-tracks`]:{position:"absolute",transition:`background-color ${h}`},[`${t}-track`]:{backgroundColor:e.trackBg,borderRadius:e.borderRadiusXS},[`${t}-track-draggable`]:{boxSizing:"content-box",backgroundClip:"content-box",border:"solid rgba(0,0,0,0)"},"&:hover":{[`${t}-rail`]:{backgroundColor:e.railHoverBg},[`${t}-track`]:{backgroundColor:e.trackHoverBg},[`${t}-dot`]:{borderColor:i},[`${t}-handle::after`]:{boxShadow:`0 0 0 ${(0,X.zA)(g)} ${e.colorPrimaryBorderHover}`},[`${t}-dot-active`]:{borderColor:e.dotActiveBorderColor}},[`${t}-handle`]:{position:"absolute",width:s,height:s,outline:"none",userSelect:"none","&-dragging-delete":{opacity:0},"&::before":{content:'""',position:"absolute",insetInlineStart:c(g).mul(-1).equal(),insetBlockStart:c(g).mul(-1).equal(),width:c(s).add(c(g).mul(2)).equal(),height:c(s).add(c(g).mul(2)).equal(),backgroundColor:"transparent"},"&::after":{content:'""',position:"absolute",insetBlockStart:0,insetInlineStart:0,width:s,height:s,backgroundColor:e.colorBgElevated,boxShadow:`0 0 0 ${(0,X.zA)(g)} ${e.handleColor}`,outline:"0px solid transparent",borderRadius:"50%",cursor:"pointer",transition:`
            inset-inline-start ${h},
            inset-block-start ${h},
            width ${h},
            height ${h},
            box-shadow ${h},
            outline ${h}
          `},"&:hover, &:active, &:focus":{"&::before":{insetInlineStart:c(d).sub(s).div(2).add(m).mul(-1).equal(),insetBlockStart:c(d).sub(s).div(2).add(m).mul(-1).equal(),width:c(d).add(c(m).mul(2)).equal(),height:c(d).add(c(m).mul(2)).equal()},"&::after":{boxShadow:`0 0 0 ${(0,X.zA)(m)} ${f}`,outline:`6px solid ${v}`,width:d,height:d,insetInlineStart:e.calc(s).sub(d).div(2).equal(),insetBlockStart:e.calc(s).sub(d).div(2).equal()}}},[`&-lock ${t}-handle`]:{"&::before, &::after":{transition:"none"}},[`${t}-mark`]:{position:"absolute",fontSize:e.fontSize},[`${t}-mark-text`]:{position:"absolute",display:"inline-block",color:e.colorTextDescription,textAlign:"center",wordBreak:"keep-all",cursor:"pointer",userSelect:"none","&-active":{color:e.colorText}},[`${t}-step`]:{position:"absolute",background:"transparent",pointerEvents:"none"},[`${t}-dot`]:{position:"absolute",width:a,height:a,backgroundColor:e.colorBgElevated,border:`${(0,X.zA)(g)} solid ${e.dotBorderColor}`,borderRadius:"50%",cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,pointerEvents:"auto","&-active":{borderColor:e.dotActiveBorderColor}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-rail`]:{backgroundColor:`${e.railBg} !important`},[`${t}-track`]:{backgroundColor:`${e.trackBgDisabled} !important`},[`
          ${t}-dot
        `]:{backgroundColor:e.colorBgElevated,borderColor:e.trackBgDisabled,boxShadow:"none",cursor:"not-allowed"},[`${t}-handle::after`]:{backgroundColor:e.colorBgElevated,cursor:"not-allowed",width:s,height:s,boxShadow:`0 0 0 ${(0,X.zA)(g)} ${u}`,insetInlineStart:0,insetBlockStart:0},[`
          ${t}-mark-text,
          ${t}-dot
        `]:{cursor:"not-allowed !important"}},[`&-tooltip ${n}-tooltip-inner`]:{minWidth:"unset"}})}},_=(e,t)=>{let{componentCls:n,railSize:r,handleSize:a,dotSize:o,marginFull:l,calc:i}=e,u=t?"width":"height",c=t?"height":"width",s=t?"insetBlockStart":"insetInlineStart",d=t?"top":"insetInlineStart",f=i(r).mul(3).sub(a).div(2).equal(),v=i(a).sub(r).div(2).equal(),g=t?{borderWidth:`${(0,X.zA)(v)} 0`,transform:`translateY(${(0,X.zA)(i(v).mul(-1).equal())})`}:{borderWidth:`0 ${(0,X.zA)(v)}`,transform:`translateX(${(0,X.zA)(e.calc(v).mul(-1).equal())})`};return{[t?"paddingBlock":"paddingInline"]:r,[c]:i(r).mul(3).equal(),[`${n}-rail`]:{[u]:"100%",[c]:r},[`${n}-track,${n}-tracks`]:{[c]:r},[`${n}-track-draggable`]:Object.assign({},g),[`${n}-handle`]:{[s]:f},[`${n}-mark`]:{insetInlineStart:0,top:0,[d]:i(r).mul(3).add(t?0:l).equal(),[u]:"100%"},[`${n}-step`]:{insetInlineStart:0,top:0,[d]:r,[u]:"100%",[c]:r},[`${n}-dot`]:{position:"absolute",[s]:i(r).sub(o).div(2).equal()}}},J=e=>{let{componentCls:t,marginPartWithMark:n}=e;return{[`${t}-horizontal`]:Object.assign(Object.assign({},_(e,!0)),{[`&${t}-with-marks`]:{marginBottom:n}})}},Q=e=>{let{componentCls:t}=e;return{[`${t}-vertical`]:Object.assign(Object.assign({},_(e,!1)),{height:"100%"})}},Z=(0,G.OF)("Slider",e=>{let t=(0,U.oX)(e,{marginPart:e.calc(e.controlHeight).sub(e.controlSize).div(2).equal(),marginFull:e.calc(e.controlSize).div(2).equal(),marginPartWithMark:e.calc(e.controlHeightLG).sub(e.controlSize).equal()});return[K(t),J(t),Q(t)]},e=>{let t=e.controlHeightLG/4,n=e.controlHeightSM/2,r=e.lineWidth+1,a=e.lineWidth+1.5,o=e.colorPrimary,l=new W.Y(o).setA(.2).toRgbString();return{controlSize:t,railSize:4,handleSize:t,handleSizeHover:n,dotSize:8,handleLineWidth:r,handleLineWidthHover:a,railBg:e.colorFillTertiary,railHoverBg:e.colorFillSecondary,trackBg:e.colorPrimaryBorder,trackHoverBg:e.colorPrimaryBorderHover,handleColor:e.colorPrimaryBorder,handleActiveColor:o,handleActiveOutlineColor:l,handleColorDisabled:new W.Y(e.colorTextDisabled).onBackground(e.colorBgContainer).toHexString(),dotBorderColor:e.colorBorderSecondary,dotActiveBorderColor:e.colorPrimaryBorder,trackBgDisabled:e.colorBgContainerDisabled}});function ee(){let[e,t]=r.useState(!1),n=r.useRef(null),a=()=>{I.A.cancel(n.current)};return r.useEffect(()=>a,[]),[e,e=>{a(),e?t(e):n.current=(0,I.A)(()=>{t(e)})}]}var et=n(71802),en=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};let er=a().forwardRef((e,t)=>{let{prefixCls:n,range:r,className:o,rootClassName:i,style:u,disabled:c,tooltipPrefixCls:s,tipFormatter:d,tooltipVisible:f,getTooltipPopupContainer:v,tooltipPlacement:g,tooltip:m={},onChangeComplete:h,classNames:b,styles:p}=e,k=en(e,["prefixCls","range","className","rootClassName","style","disabled","tooltipPrefixCls","tipFormatter","tooltipVisible","getTooltipPopupContainer","tooltipPlacement","tooltip","onChangeComplete","classNames","styles"]),{vertical:A}=e,{getPrefixCls:C,direction:x,className:y,style:E,classNames:S,styles:$,getPopupContainer:M}=(0,et.TP)("slider"),w=a().useContext(L.A),{handleRender:O,direction:B}=a().useContext(z),D="rtl"===(B||x),[R,j]=ee(),[F,P]=ee(),H=Object.assign({},m),{open:q,placement:T,getPopupContainer:X,prefixCls:W,formatter:Y}=H,G=null!=q?q:f,U=(R||F)&&!1!==G,K=function(e,t){return e||null===e?e:t||null===t?t:e=>"number"==typeof e?e.toString():""}(Y,d),[_,J]=ee(),Q=(e,t)=>e||(t?D?"left":"right":"top"),er=C("slider",n),[ea,eo,el]=Z(er),ei=l()(o,y,S.root,null==b?void 0:b.root,i,{[`${er}-rtl`]:D,[`${er}-lock`]:_},eo,el);D&&!k.vertical&&(k.reverse=!k.reverse),a().useEffect(()=>{let e=()=>{(0,I.A)(()=>{P(!1)},1)};return document.addEventListener("mouseup",e),()=>{document.removeEventListener("mouseup",e)}},[]);let eu=r&&!G,ec=O||((e,t)=>{let{index:n}=t,r=e.props;function o(e,t,n){var a,o;n&&(null==(a=k[e])||a.call(k,t)),null==(o=r[e])||o.call(r,t)}let l=Object.assign(Object.assign({},r),{onMouseEnter:e=>{j(!0),o("onMouseEnter",e)},onMouseLeave:e=>{j(!1),o("onMouseLeave",e)},onMouseDown:e=>{P(!0),J(!0),o("onMouseDown",e)},onFocus:e=>{var t;P(!0),null==(t=k.onFocus)||t.call(k,e),o("onFocus",e,!0)},onBlur:e=>{var t;P(!1),null==(t=k.onBlur)||t.call(k,e),o("onBlur",e,!0)}}),i=a().cloneElement(e,l),u=(!!G||U)&&null!==K;return eu?i:a().createElement(V,Object.assign({},H,{prefixCls:C("tooltip",null!=W?W:s),title:K?K(t.value):"",value:t.value,open:u,placement:Q(null!=T?T:g,A),key:n,classNames:{root:`${er}-tooltip`},getPopupContainer:X||v||M}),i)}),es=eu?(e,t)=>{let n=a().cloneElement(e,{style:Object.assign(Object.assign({},e.props.style),{visibility:"hidden"})});return a().createElement(V,Object.assign({},H,{prefixCls:C("tooltip",null!=W?W:s),title:K?K(t.value):"",open:null!==K&&U,placement:Q(null!=T?T:g,A),key:"tooltip",classNames:{root:`${er}-tooltip`},getPopupContainer:X||v||M,draggingDelete:t.draggingDelete}),n)}:void 0,ed=Object.assign(Object.assign(Object.assign(Object.assign({},$.root),E),null==p?void 0:p.root),u),ef=Object.assign(Object.assign({},$.tracks),null==p?void 0:p.tracks),ev=l()(S.tracks,null==b?void 0:b.tracks);return ea(a().createElement(N,Object.assign({},k,{classNames:Object.assign({handle:l()(S.handle,null==b?void 0:b.handle),rail:l()(S.rail,null==b?void 0:b.rail),track:l()(S.track,null==b?void 0:b.track)},ev?{tracks:ev}:{}),styles:Object.assign({handle:Object.assign(Object.assign({},$.handle),null==p?void 0:p.handle),rail:Object.assign(Object.assign({},$.rail),null==p?void 0:p.rail),track:Object.assign(Object.assign({},$.track),null==p?void 0:p.track)},Object.keys(ef).length?{tracks:ef}:{}),step:k.step,range:r,className:ei,style:ed,disabled:null!=c?c:w,ref:t,prefixCls:er,handleRender:ec,activeHandleRender:es,onChangeComplete:e=>{null==h||h(e),J(!1)}})))})}};