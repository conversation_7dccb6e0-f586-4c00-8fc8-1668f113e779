"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShareConfigResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ShareConfigResponseDto {
    id;
    name;
    title;
    path;
    imageUrl;
    description;
    type;
    isActive;
    sortOrder;
    createdAt;
    updatedAt;
}
exports.ShareConfigResponseDto = ShareConfigResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享配置ID', example: 'share-config-001' }),
    __metadata("design:type", String)
], ShareConfigResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享配置名称', example: '默认分享配置' }),
    __metadata("design:type", String)
], ShareConfigResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享标题', example: '一起来挑战词汇游戏！' }),
    __metadata("design:type", String)
], ShareConfigResponseDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享路径', example: '/pages/index/index' }),
    __metadata("design:type", String)
], ShareConfigResponseDto.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享图片URL', required: false }),
    __metadata("design:type", String)
], ShareConfigResponseDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享描述', required: false }),
    __metadata("design:type", String)
], ShareConfigResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享类型', example: 'default' }),
    __metadata("design:type", String)
], ShareConfigResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否启用', example: true }),
    __metadata("design:type", Boolean)
], ShareConfigResponseDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序权重', example: 1 }),
    __metadata("design:type", Number)
], ShareConfigResponseDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2025-06-19T10:00:00.000Z' }),
    __metadata("design:type", String)
], ShareConfigResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间', example: '2025-06-19T12:00:00.000Z' }),
    __metadata("design:type", String)
], ShareConfigResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=share-config-response.dto.js.map