"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UserService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_entity_1 = require("./entities/user.entity");
const date_formatter_1 = require("../../common/utils/date-formatter");
const level_service_1 = require("../level/level.service");
let UserService = UserService_1 = class UserService {
    userModel;
    levelService;
    logger = new common_1.Logger(UserService_1.name);
    constructor(userModel, levelService) {
        this.userModel = userModel;
        this.levelService = levelService;
    }
    async generateUniqueUserId() {
        let id;
        do {
            id = Math.floor(Math.random() * 90000000 + 10000000).toString();
        } while (await this.userModel.findOne({ id }).exec());
        return id;
    }
    _mapToUserResponseDto(user) {
        return {
            id: user.id,
            phone: user.phone,
            openid: user.openid,
            nickname: user.nickname,
            avatarUrl: user.avatarUrl,
            unlockedLevels: user.unlockedLevels,
            completedLevelIds: user.completedLevelIds,
            totalGames: user.totalGames,
            totalCompletions: user.totalCompletions,
            lastPlayTime: (0, date_formatter_1.formatDate)(user.lastPlayTime),
            isVip: user.isVip,
            vipExpiresAt: user.vipExpiresAt
                ? (0, date_formatter_1.formatDate)(user.vipExpiresAt)
                : undefined,
            dailyUnlockLimit: user.dailyUnlockLimit,
            dailyUnlockCount: user.dailyUnlockCount,
            dailyShared: user.dailyShared,
            lastPlayDate: user.lastPlayDate,
            totalShares: user.totalShares,
            createdAt: (0, date_formatter_1.formatDate)(user.createdAt),
            updatedAt: (0, date_formatter_1.formatDate)(user.updatedAt),
        };
    }
    async getUserEntity(id) {
        const user = await this.userModel.findOne({ id }).exec();
        if (!user) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的用户`);
        }
        return user;
    }
    async getUserByOpenid(openid) {
        if (!openid)
            return null;
        return this.userModel.findOne({ openid }).exec();
    }
    async getUserByPhone(phone) {
        return this.userModel.findOne({ phone }).exec();
    }
    async create(createUserDto) {
        if (createUserDto.phone) {
            const existingUserByPhone = await this.getUserByPhone(createUserDto.phone);
            if (existingUserByPhone) {
                throw new common_1.BadRequestException(`手机号 "${createUserDto.phone}" 已存在`);
            }
        }
        if (createUserDto.openid) {
            const existingUserByOpenid = await this.getUserByOpenid(createUserDto.openid);
            if (existingUserByOpenid) {
                throw new common_1.BadRequestException(`openid "${createUserDto.openid}" 已存在`);
            }
        }
        if (!createUserDto.phone && !createUserDto.openid) {
            throw new common_1.BadRequestException('至少需要提供手机号或openid中的一个');
        }
        const today = new Date().toISOString().split('T')[0];
        const newUser = new this.userModel({
            id: await this.generateUniqueUserId(),
            phone: createUserDto.phone,
            openid: createUserDto.openid,
            nickname: createUserDto.nickname,
            avatarUrl: createUserDto.avatarUrl,
            unlockedLevels: 1,
            completedLevelIds: [],
            totalGames: 0,
            totalCompletions: 0,
            lastPlayTime: new Date(),
            isVip: false,
            dailyUnlockLimit: 15,
            dailyUnlockCount: 0,
            dailyShared: false,
            lastPlayDate: today,
            totalShares: 0,
        });
        const savedUser = await newUser.save();
        return this._mapToUserResponseDto(savedUser);
    }
    async findAll(params) {
        const { search, isVip, startDate, endDate, page = 1, pageSize = 20, } = params || {};
        const query = {};
        if (search) {
            query.$or = [
                { phone: { $regex: search, $options: 'i' } },
                { nickname: { $regex: search, $options: 'i' } },
                { id: { $regex: search, $options: 'i' } },
            ];
        }
        if (isVip !== undefined) {
            query.isVip = isVip;
        }
        if (startDate || endDate) {
            query.createdAt = {};
            if (startDate) {
                query.createdAt.$gte = new Date(startDate);
            }
            if (endDate) {
                query.createdAt.$lte = new Date(endDate + 'T23:59:59.999Z');
            }
        }
        const skip = (page - 1) * pageSize;
        const [users, total] = await Promise.all([
            this.userModel
                .find(query)
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(pageSize)
                .exec(),
            this.userModel.countDocuments(query).exec(),
        ]);
        const updatedUsers = await Promise.all(users.map(async (user) => {
            const updatedUser = await this.checkAndUpdateVipStatus(user);
            return this._mapToUserResponseDto(updatedUser);
        }));
        return {
            users: updatedUsers,
            total,
        };
    }
    async findOne(id) {
        const user = await this.getUserEntity(id);
        return this._mapToUserResponseDto(user);
    }
    async findByOpenid(openid) {
        const user = await this.getUserByOpenid(openid);
        if (!user) {
            throw new common_1.NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
        }
        return this._mapToUserResponseDto(user);
    }
    async findByPhone(phone) {
        const user = await this.getUserByPhone(phone);
        if (!user) {
            throw new common_1.NotFoundException(`未找到手机号为 "${phone}" 的用户`);
        }
        return this._mapToUserResponseDto(user);
    }
    async update(id, updateUserDto) {
        const existingUser = await this.getUserEntity(id);
        if (updateUserDto.phone && updateUserDto.phone !== existingUser.phone) {
            const existingUserByPhone = await this.getUserByPhone(updateUserDto.phone);
            if (existingUserByPhone && existingUserByPhone.id !== id) {
                throw new common_1.BadRequestException(`手机号 "${updateUserDto.phone}" 已被其他用户使用`);
            }
        }
        if (updateUserDto.openid && updateUserDto.openid !== existingUser.openid) {
            const existingUserByOpenid = await this.getUserByOpenid(updateUserDto.openid);
            if (existingUserByOpenid && existingUserByOpenid.id !== id) {
                throw new common_1.BadRequestException(`openid "${updateUserDto.openid}" 已被其他用户使用`);
            }
        }
        const updatedUser = await this.userModel
            .findOneAndUpdate({ id }, { ...updateUserDto, updatedAt: new Date() }, { new: true })
            .exec();
        if (!updatedUser) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的用户`);
        }
        return this._mapToUserResponseDto(updatedUser);
    }
    async remove(id) {
        const result = await this.userModel.deleteOne({ id }).exec();
        if (result.deletedCount === 0) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的用户`);
        }
    }
    async completeLevel(userId, completeLevelDto) {
        const user = await this.getUserEntity(userId);
        const { levelId } = completeLevelDto;
        await this.levelService.getLevelEntity(levelId);
        if (user.completedLevelIds.includes(levelId)) {
            throw new common_1.BadRequestException(`用户已经完成过关卡 "${levelId}"`);
        }
        user.completedLevelIds.push(levelId);
        user.totalCompletions += 1;
        user.totalGames += 1;
        user.lastPlayTime = new Date();
        const currentLevel = user.completedLevelIds.length;
        if (currentLevel >= user.unlockedLevels && user.unlockedLevels < 1000) {
            user.unlockedLevels = Math.min(currentLevel + 1, 1000);
        }
        const updatedUser = await user.save();
        return this._mapToUserResponseDto(updatedUser);
    }
    async startGame(userId) {
        const user = await this.getUserEntity(userId);
        user.totalGames += 1;
        user.lastPlayTime = new Date();
        const updatedUser = await user.save();
        return this._mapToUserResponseDto(updatedUser);
    }
    async getUserStats(userId) {
        const user = await this.getUserEntity(userId);
        return {
            totalGames: user.totalGames,
            totalCompletions: user.totalCompletions,
            unlockedLevels: user.unlockedLevels,
            completedLevels: user.completedLevelIds.length,
            completionRate: user.totalGames > 0
                ? (user.totalCompletions / user.totalGames) * 100
                : 0,
        };
    }
    async resetUserProgress(userId) {
        const user = await this.getUserEntity(userId);
        user.unlockedLevels = 1;
        user.completedLevelIds = [];
        user.totalGames = 0;
        user.totalCompletions = 0;
        const updatedUser = await user.save();
        return this._mapToUserResponseDto(updatedUser);
    }
    maskPhone(phone) {
        if (!phone || phone.length !== 11)
            return phone;
        return phone.substring(0, 3) + '****' + phone.substring(7);
    }
    async bindWeixinUser(bindDto) {
        const existingUserByPhone = await this.getUserByPhone(bindDto.phone);
        if (existingUserByPhone) {
            throw new common_1.BadRequestException(`手机号 "${bindDto.phone}" 已存在`);
        }
        const existingUserByOpenid = await this.getUserByOpenid(bindDto.openid);
        if (existingUserByOpenid) {
            throw new common_1.BadRequestException(`openid "${bindDto.openid}" 已存在`);
        }
        const today = new Date().toISOString().split('T')[0];
        const newUser = new this.userModel({
            id: await this.generateUniqueUserId(),
            phone: bindDto.phone,
            openid: bindDto.openid,
            nickname: bindDto.nickname,
            avatarUrl: bindDto.avatarUrl,
            unlockedLevels: 1,
            completedLevelIds: [],
            totalGames: 0,
            totalCompletions: 0,
            lastPlayTime: new Date(),
            isVip: false,
            dailyUnlockLimit: 15,
            dailyUnlockCount: 0,
            dailyShared: false,
            lastPlayDate: today,
            totalShares: 0,
        });
        const savedUser = await newUser.save();
        return {
            id: savedUser.id,
            maskedPhone: this.maskPhone(savedUser.phone),
            nickname: savedUser.nickname,
            avatarUrl: savedUser.avatarUrl,
            unlockedLevels: savedUser.unlockedLevels,
            completedLevelIds: savedUser.completedLevelIds,
            totalGames: savedUser.totalGames,
            totalCompletions: savedUser.totalCompletions,
            lastPlayTime: (0, date_formatter_1.formatDate)(savedUser.lastPlayTime),
            createdAt: (0, date_formatter_1.formatDate)(savedUser.createdAt),
        };
    }
    async getWeixinUserInfo(openid) {
        const user = await this.getUserByOpenid(openid);
        if (!user) {
            throw new common_1.NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
        }
        return {
            id: user.id,
            maskedPhone: this.maskPhone(user.phone),
            nickname: user.nickname,
            avatarUrl: user.avatarUrl,
            unlockedLevels: user.unlockedLevels,
            completedLevelIds: user.completedLevelIds,
            totalGames: user.totalGames,
            totalCompletions: user.totalCompletions,
            lastPlayTime: (0, date_formatter_1.formatDate)(user.lastPlayTime),
            createdAt: (0, date_formatter_1.formatDate)(user.createdAt),
        };
    }
    async getWeixinUserInfoById(id) {
        const user = await this.getUserEntity(id);
        return {
            id: user.id,
            maskedPhone: this.maskPhone(user.phone),
            nickname: user.nickname,
            avatarUrl: user.avatarUrl,
            unlockedLevels: user.unlockedLevels,
            completedLevelIds: user.completedLevelIds,
            totalGames: user.totalGames,
            totalCompletions: user.totalCompletions,
            lastPlayTime: (0, date_formatter_1.formatDate)(user.lastPlayTime),
            createdAt: (0, date_formatter_1.formatDate)(user.createdAt),
        };
    }
    async getWeixinLevelsWithProgress(openid) {
        const user = await this.getUserByOpenid(openid);
        if (!user) {
            throw new common_1.NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
        }
        const allLevels = await this.levelService.findAll();
        return allLevels.map((level, index) => ({
            id: level.id,
            name: level.name,
            difficulty: level.difficulty,
            description: level.description,
            isUnlocked: index + 1 <= user.unlockedLevels,
            isCompleted: user.completedLevelIds.includes(level.id),
            createdAt: level.createdAt,
        }));
    }
    async getWeixinLevelDetail(openid, levelId) {
        const user = await this.getUserByOpenid(openid);
        if (!user) {
            throw new common_1.NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
        }
        const levelWithPhrases = await this.levelService.getLevelWithPhrases(levelId);
        const allLevels = await this.levelService.findAll();
        const levelIndex = allLevels.findIndex((level) => level.id === levelId);
        if (levelIndex === -1) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${levelId}" 的关卡`);
        }
        const isUnlocked = levelIndex + 1 <= user.unlockedLevels;
        if (!isUnlocked) {
            throw new common_1.BadRequestException(`关卡 "${levelId}" 尚未解锁`);
        }
        const phrases = levelWithPhrases.phrases.map((phrase) => ({
            id: phrase.id,
            text: phrase.text,
            meaning: phrase.meaning,
            exampleSentence: phrase.exampleSentence,
            tags: phrase.tags,
        }));
        return {
            id: levelWithPhrases.id,
            name: levelWithPhrases.name,
            difficulty: levelWithPhrases.difficulty,
            description: levelWithPhrases.description,
            isUnlocked: true,
            isCompleted: user.completedLevelIds.includes(levelId),
            phrases,
            createdAt: levelWithPhrases.createdAt,
        };
    }
    async weixinCompleteLevel(completeLevelDto) {
        const { openid, levelId } = completeLevelDto;
        let user = await this.getUserByOpenid(openid);
        if (!user) {
            throw new common_1.NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
        }
        user = await this.checkAndResetDailyData(user);
        const { canUnlock, reason } = await this.canUserUnlock(user);
        if (!canUnlock) {
            throw new common_1.BadRequestException(reason);
        }
        await this.levelService.getLevelEntity(levelId);
        if (user.completedLevelIds.includes(levelId)) {
            throw new common_1.BadRequestException(`用户已经完成过关卡 "${levelId}"`);
        }
        const allLevels = await this.levelService.findAll();
        const levelIndex = allLevels.findIndex((level) => level.id === levelId);
        if (levelIndex === -1) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${levelId}" 的关卡`);
        }
        const isUnlocked = levelIndex + 1 <= user.unlockedLevels;
        if (!isUnlocked) {
            throw new common_1.BadRequestException(`关卡 "${levelId}" 尚未解锁`);
        }
        user.completedLevelIds.push(levelId);
        user.totalCompletions += 1;
        user.totalGames += 1;
        user.lastPlayTime = new Date();
        const currentLevel = user.completedLevelIds.length;
        let hasUnlockedNewLevel = false;
        if (currentLevel >= user.unlockedLevels && user.unlockedLevels < 1000) {
            user.unlockedLevels = Math.min(currentLevel + 1, 1000);
            user.dailyUnlockCount += 1;
            hasUnlockedNewLevel = true;
        }
        const updatedUser = await user.save();
        this.logger.log(`🎮 用户通关: userId=${updatedUser.id} levelId=${levelId} dailyUnlockCount=${updatedUser.dailyUnlockCount}/${updatedUser.dailyUnlockLimit}`);
        const remainingUnlocks = updatedUser.isVip
            ? -1
            : Math.max(0, updatedUser.dailyUnlockLimit - updatedUser.dailyUnlockCount);
        return {
            message: hasUnlockedNewLevel
                ? '恭喜！关卡通关成功，解锁新关卡！'
                : '恭喜！关卡通关成功！',
            userId: updatedUser.id,
            levelId,
            unlockedLevels: updatedUser.unlockedLevels,
            totalCompletions: updatedUser.totalCompletions,
            hasUnlockedNewLevel,
            dailyUnlockCount: updatedUser.dailyUnlockCount,
            dailyUnlockLimit: updatedUser.dailyUnlockLimit,
            remainingUnlocks,
            isVip: updatedUser.isVip,
        };
    }
    async weixinLogin(loginDto, weixinApiData) {
        const { openid, sessionKey, unionid } = weixinApiData;
        this.logger.debug(`🔐 会话密钥已获取: ${sessionKey.substring(0, 8)}...`);
        const user = await this.getUserByOpenid(openid);
        if (user) {
            this.logger.log(`👤 用户登录: openid=${openid.substring(0, 8)}...`);
            user.lastPlayTime = new Date();
            await user.save();
            return {
                status: 'success',
                message: '登录成功',
                openid,
                unionid,
                userInfo: {
                    id: user.id,
                    maskedPhone: this.maskPhone(user.phone),
                    nickname: user.nickname,
                    avatarUrl: user.avatarUrl,
                    unlockedLevels: user.unlockedLevels,
                    completedLevelIds: user.completedLevelIds,
                    totalGames: user.totalGames,
                    totalCompletions: user.totalCompletions,
                    lastPlayTime: (0, date_formatter_1.formatDate)(user.lastPlayTime),
                    createdAt: (0, date_formatter_1.formatDate)(user.createdAt),
                },
            };
        }
        else {
            if (loginDto.phone) {
                const existingUserByPhone = await this.getUserByPhone(loginDto.phone);
                if (existingUserByPhone) {
                    throw new common_1.BadRequestException(`手机号 "${loginDto.phone}" 已被其他用户使用`);
                }
            }
            const today = new Date().toISOString().split('T')[0];
            const newUser = new this.userModel({
                id: await this.generateUniqueUserId(),
                phone: loginDto.phone,
                openid,
                nickname: loginDto.nickname || '微信用户',
                avatarUrl: loginDto.avatarUrl,
                unlockedLevels: 1,
                completedLevelIds: [],
                totalGames: 0,
                totalCompletions: 0,
                lastPlayTime: new Date(),
                isVip: false,
                dailyUnlockLimit: 15,
                dailyUnlockCount: 0,
                dailyShared: false,
                lastPlayDate: today,
                totalShares: 0,
            });
            const savedUser = await newUser.save();
            this.logger.log(`🆕 新用户注册并登录: openid=${openid.substring(0, 8)}... phone=${loginDto.phone || '未提供'}`);
            return {
                status: 'success',
                message: '注册并登录成功',
                openid,
                unionid,
                userInfo: {
                    id: savedUser.id,
                    maskedPhone: savedUser.phone
                        ? this.maskPhone(savedUser.phone)
                        : undefined,
                    nickname: savedUser.nickname,
                    avatarUrl: savedUser.avatarUrl,
                    unlockedLevels: savedUser.unlockedLevels,
                    completedLevelIds: savedUser.completedLevelIds,
                    totalGames: savedUser.totalGames,
                    totalCompletions: savedUser.totalCompletions,
                    lastPlayTime: (0, date_formatter_1.formatDate)(savedUser.lastPlayTime),
                    createdAt: (0, date_formatter_1.formatDate)(savedUser.createdAt),
                },
            };
        }
    }
    async weixinBindPhone(bindDto) {
        const { openid, phone, nickname, avatarUrl } = bindDto;
        const existingUserByOpenid = await this.getUserByOpenid(openid);
        if (existingUserByOpenid) {
            throw new common_1.BadRequestException(`该微信账号已绑定用户`);
        }
        const existingUserByPhone = await this.getUserByPhone(phone);
        if (existingUserByPhone) {
            throw new common_1.BadRequestException(`手机号 "${phone}" 已被其他用户使用`);
        }
        const today = new Date().toISOString().split('T')[0];
        const newUser = new this.userModel({
            id: await this.generateUniqueUserId(),
            phone,
            openid,
            nickname: nickname || '微信用户',
            avatarUrl,
            unlockedLevels: 1,
            completedLevelIds: [],
            totalGames: 0,
            totalCompletions: 0,
            lastPlayTime: new Date(),
            isVip: false,
            dailyUnlockLimit: 15,
            dailyUnlockCount: 0,
            dailyShared: false,
            lastPlayDate: today,
            totalShares: 0,
        });
        const savedUser = await newUser.save();
        this.logger.log(`📱 用户绑定手机号成功: openid=${openid.substring(0, 8)}... phone=${phone}`);
        return {
            status: 'success',
            message: '绑定成功',
            openid,
            userInfo: {
                id: savedUser.id,
                maskedPhone: this.maskPhone(savedUser.phone),
                nickname: savedUser.nickname,
                avatarUrl: savedUser.avatarUrl,
                unlockedLevels: savedUser.unlockedLevels,
                completedLevelIds: savedUser.completedLevelIds,
                totalGames: savedUser.totalGames,
                totalCompletions: savedUser.totalCompletions,
                lastPlayTime: (0, date_formatter_1.formatDate)(savedUser.lastPlayTime),
                createdAt: (0, date_formatter_1.formatDate)(savedUser.createdAt),
            },
        };
    }
    async checkAndResetDailyData(user) {
        const today = new Date().toISOString().split('T')[0];
        if (user.lastPlayDate !== today) {
            user.dailyUnlockCount = 0;
            user.dailyUnlockLimit = 15;
            user.dailyShared = false;
            user.lastPlayDate = today;
            this.logger.log(`🔄 重置用户每日数据: userId=${user.id} date=${today}`);
            return await user.save();
        }
        return user;
    }
    async checkAndUpdateVipStatus(user) {
        if (user.isVip && user.vipExpiresAt) {
            const now = new Date();
            if (now > user.vipExpiresAt) {
                user.isVip = false;
                user.vipExpiresAt = undefined;
                user.dailyUnlockLimit = 15;
                await user.save();
                this.logger.log(`用户VIP已过期: userId=${user.id}, 过期时间=${user.vipExpiresAt}`);
            }
        }
        return user;
    }
    async canUserUnlock(user) {
        user = await this.checkAndUpdateVipStatus(user);
        if (user.isVip) {
            return { canUnlock: true };
        }
        if (user.dailyUnlockCount >= user.dailyUnlockLimit) {
            return {
                canUnlock: false,
                reason: `今日解锁次数已达上限（${user.dailyUnlockLimit}次），请明天再来或分享获得额外机会`,
            };
        }
        return { canUnlock: true };
    }
    async getWeixinDailyStatus(openid) {
        let user = await this.getUserByOpenid(openid);
        if (!user) {
            throw new common_1.NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
        }
        user = await this.checkAndResetDailyData(user);
        const { canUnlock, reason } = await this.canUserUnlock(user);
        const remainingUnlocks = user.isVip
            ? -1
            : Math.max(0, user.dailyUnlockLimit - user.dailyUnlockCount);
        return {
            id: user.id,
            dailyUnlockCount: user.dailyUnlockCount,
            dailyUnlockLimit: user.dailyUnlockLimit,
            remainingUnlocks,
            dailyShared: user.dailyShared,
            isVip: user.isVip,
            lastPlayDate: user.lastPlayDate,
            totalShares: user.totalShares,
            canUnlock,
            limitReason: reason,
        };
    }
    async weixinShare(shareDto) {
        const { openid } = shareDto;
        let user = await this.getUserByOpenid(openid);
        if (!user) {
            throw new common_1.NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
        }
        user = await this.checkAndResetDailyData(user);
        if (user.dailyShared) {
            const remainingUnlocks = user.isVip
                ? -1
                : Math.max(0, user.dailyUnlockLimit - user.dailyUnlockCount);
            return {
                status: 'already_shared',
                message: '今日已分享过，无法重复获得奖励',
                userId: user.id,
                dailyUnlockCount: user.dailyUnlockCount,
                dailyUnlockLimit: user.dailyUnlockLimit,
                remainingUnlocks,
                isVip: user.isVip,
                totalShares: user.totalShares,
            };
        }
        user.dailyShared = true;
        user.totalShares += 1;
        user.dailyUnlockLimit += 5;
        const updatedUser = await user.save();
        const remainingUnlocks = updatedUser.isVip
            ? -1
            : Math.max(0, updatedUser.dailyUnlockLimit - updatedUser.dailyUnlockCount);
        this.logger.log(`📤 用户分享成功: userId=${updatedUser.id} totalShares=${updatedUser.totalShares}`);
        return {
            status: 'success',
            message: '分享成功，获得5次额外解锁机会！',
            userId: updatedUser.id,
            dailyUnlockCount: updatedUser.dailyUnlockCount,
            dailyUnlockLimit: updatedUser.dailyUnlockLimit,
            remainingUnlocks,
            isVip: updatedUser.isVip,
            totalShares: updatedUser.totalShares,
        };
    }
};
exports.UserService = UserService;
exports.UserService = UserService = UserService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_entity_1.User.name)),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => level_service_1.LevelService))),
    __metadata("design:paramtypes", [mongoose_2.Model,
        level_service_1.LevelService])
], UserService);
//# sourceMappingURL=user.service.js.map