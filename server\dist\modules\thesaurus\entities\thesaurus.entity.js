"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThesaurusSchema = exports.Thesaurus = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const swagger_1 = require("@nestjs/swagger");
let Thesaurus = class Thesaurus {
    id;
    name;
    description;
    phraseIds;
    createdAt;
    updatedAt;
};
exports.Thesaurus = Thesaurus;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词库的唯一ID' }),
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], Thesaurus.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词库名称' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Thesaurus.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词库描述', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Thesaurus.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '存储关联的词组ID', type: [String] }),
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], Thesaurus.prototype, "phraseIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间 (Date Object)' }),
    __metadata("design:type", Date)
], Thesaurus.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新时间 (Date Object)' }),
    __metadata("design:type", Date)
], Thesaurus.prototype, "updatedAt", void 0);
exports.Thesaurus = Thesaurus = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Thesaurus);
exports.ThesaurusSchema = mongoose_1.SchemaFactory.createForClass(Thesaurus);
//# sourceMappingURL=thesaurus.entity.js.map