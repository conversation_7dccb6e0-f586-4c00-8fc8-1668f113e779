"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePhraseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreatePhraseDto {
    text;
    meaning;
    exampleSentence;
    tags;
}
exports.CreatePhraseDto = CreatePhraseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词组文本', example: 'Hello World' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '词组文本不能为空' }),
    __metadata("design:type", String)
], CreatePhraseDto.prototype, "text", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词组含义', example: '你好，世界' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '词组含义不能为空' }),
    __metadata("design:type", String)
], CreatePhraseDto.prototype, "meaning", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '例句', example: 'When you start programming, the first thing you often do is print "Hello World".', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePhraseDto.prototype, "exampleSentence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签', example: ['greeting', 'common'], required: false, type: [String] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreatePhraseDto.prototype, "tags", void 0);
//# sourceMappingURL=create-phrase.dto.js.map