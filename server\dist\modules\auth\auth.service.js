"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const jwt_1 = require("@nestjs/jwt");
const bcrypt = require("bcrypt");
const admin_user_entity_1 = require("./entities/admin-user.entity");
let AuthService = AuthService_1 = class AuthService {
    adminUserModel;
    jwtService;
    logger = new common_1.Logger(AuthService_1.name);
    constructor(adminUserModel, jwtService) {
        this.adminUserModel = adminUserModel;
        this.jwtService = jwtService;
    }
    async onModuleInit() {
        await this.initializeDefaultAdmin();
    }
    async initializeDefaultAdmin() {
        try {
            const existingAdmin = await this.adminUserModel.findOne({ username: 'admin' }).exec();
            if (!existingAdmin) {
                const hashedPassword = await bcrypt.hash('password123', 10);
                const defaultAdmin = new this.adminUserModel({
                    userId: 'admin001',
                    username: 'admin',
                    password: hashedPassword,
                    roles: ['admin'],
                    isActive: true,
                });
                await defaultAdmin.save();
                this.logger.log('✅ 默认管理员账户创建成功: admin/password123');
            }
            else {
                this.logger.log('📝 默认管理员账户已存在');
            }
        }
        catch (error) {
            this.logger.error('❌ 初始化默认管理员账户失败:', error);
        }
    }
    async login(loginAdminDto) {
        const { username, password } = loginAdminDto;
        const user = await this.adminUserModel.findOne({
            username,
            isActive: true
        }).exec();
        if (!user) {
            throw new common_1.UnauthorizedException('用户名或密码错误');
        }
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('用户名或密码错误');
        }
        user.lastLoginAt = new Date();
        await user.save();
        const payload = {
            username: user.username,
            sub: user.userId,
            roles: user.roles
        };
        this.logger.log(`👤 用户登录成功: ${username}`);
        return {
            message: '登录成功',
            accessToken: this.jwtService.sign(payload),
            user: {
                userId: user.userId,
                username: user.username,
                roles: user.roles,
            },
        };
    }
    async findUserByUsername(username) {
        return this.adminUserModel.findOne({
            username,
            isActive: true
        }).exec();
    }
    async findUserById(userId) {
        return this.adminUserModel.findOne({
            userId,
            isActive: true
        }).exec();
    }
    async createAdminUser(userData) {
        const hashedPassword = await bcrypt.hash(userData.password, 10);
        const newUser = new this.adminUserModel({
            ...userData,
            password: hashedPassword,
            roles: userData.roles || ['admin'],
            isActive: true,
        });
        return newUser.save();
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(admin_user_entity_1.AdminUser.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map