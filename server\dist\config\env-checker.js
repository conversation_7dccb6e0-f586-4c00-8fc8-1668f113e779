"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnvironmentChecker = void 0;
const fs = require("fs");
const path = require("path");
const common_1 = require("@nestjs/common");
class EnvironmentChecker {
    static logger = new common_1.Logger('EnvironmentChecker');
    static checkEnvironmentConfig() {
        const nodeEnv = process.env.NODE_ENV || 'development';
        const envFile = `.env.${nodeEnv}`;
        const envPath = path.join(process.cwd(), envFile);
        this.logger.log(`🔍 检查环境配置...`);
        this.logger.log(`📝 当前环境: ${nodeEnv}`);
        this.logger.log(`📁 配置文件路径: ${envPath}`);
        if (!fs.existsSync(envPath)) {
            this.logger.error(`❌ 环境配置文件不存在: ${envFile}`);
            this.logger.error(`💡 请根据以下步骤创建配置文件:`);
            if (nodeEnv === 'development') {
                this.logger.error(`   1. 复制 .env.development 文件`);
                this.logger.error(`   2. 根据本地环境修改配置`);
            }
            else if (nodeEnv === 'staging') {
                this.logger.error(`   1. 复制 .env.staging 文件`);
                this.logger.error(`   2. 根据预发环境修改配置`);
            }
            else if (nodeEnv === 'production') {
                this.logger.error(`   1. 复制 .env.production.template 为 .env.production`);
                this.logger.error(`   2. 填入真实的生产环境配置`);
                this.logger.error(`   3. 确保使用安全的密钥和数据库连接`);
            }
            this.logger.error(`🚨 应用启动失败: 缺少必要的环境配置文件`);
            process.exit(1);
        }
        const requiredVars = ['MONGODB_URI', 'JWT_SECRET'];
        const missingVars = requiredVars.filter(varName => !process.env[varName]);
        if (missingVars.length > 0) {
            this.logger.error(`❌ 缺少必要的环境变量: ${missingVars.join(', ')}`);
            this.logger.error(`💡 请在 ${envFile} 文件中配置这些变量`);
            this.logger.error(`🚨 应用启动失败: 环境变量配置不完整`);
            process.exit(1);
        }
        if (nodeEnv === 'production') {
            this.validateProductionConfig();
        }
        this.logger.log(`✅ 环境配置检查通过`);
    }
    static validateProductionConfig() {
        const jwtSecret = process.env.JWT_SECRET;
        const mongoUri = process.env.MONGODB_URI;
        if (jwtSecret && (jwtSecret.includes('dev') ||
            jwtSecret.includes('test') ||
            jwtSecret.length < 32)) {
            this.logger.warn(`⚠️  生产环境JWT密钥可能不够安全`);
        }
        if (mongoUri && mongoUri.includes('localhost')) {
            this.logger.warn(`⚠️  生产环境使用localhost数据库连接`);
        }
        this.logger.log(`🔒 生产环境安全检查完成`);
    }
    static logStartupInfo() {
        const nodeEnv = process.env.NODE_ENV || 'development';
        const port = process.env.PORT || '3001';
        const dbName = process.env.MONGODB_DB_NAME || 'game-db';
        this.logger.log(`🚀 应用启动信息:`);
        this.logger.log(`   环境: ${nodeEnv}`);
        this.logger.log(`   端口: ${port}`);
        this.logger.log(`   数据库: ${dbName}`);
        if (nodeEnv === 'development') {
            this.logger.log(`   Swagger文档: http://localhost:${port}/api-docs`);
        }
    }
}
exports.EnvironmentChecker = EnvironmentChecker;
//# sourceMappingURL=env-checker.js.map