"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhraseSchema = exports.Phrase = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const swagger_1 = require("@nestjs/swagger");
let Phrase = class Phrase {
    id;
    text;
    meaning;
    exampleSentence;
    tags;
    createdAt;
    updatedAt;
};
exports.Phrase = Phrase;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词组的唯一ID', example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' }),
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], Phrase.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词组文本', example: 'Hello World' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Phrase.prototype, "text", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词组含义', example: '你好，世界' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Phrase.prototype, "meaning", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '例句', example: 'When you start programming, the first thing you often do is print "Hello World".', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Phrase.prototype, "exampleSentence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签', example: ['greeting', 'common'], required: false, type: [String] }),
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], Phrase.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间 (Date Object)' }),
    __metadata("design:type", Date)
], Phrase.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新时间 (Date Object)' }),
    __metadata("design:type", Date)
], Phrase.prototype, "updatedAt", void 0);
exports.Phrase = Phrase = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Phrase);
exports.PhraseSchema = mongoose_1.SchemaFactory.createForClass(Phrase);
//# sourceMappingURL=phrase.entity.js.map