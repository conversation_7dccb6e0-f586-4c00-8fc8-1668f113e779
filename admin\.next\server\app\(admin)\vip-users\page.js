(()=>{var e={};e.id=298,e.ids=[298],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6714:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>I});var r=t(60687),i=t(43210),n=t(94733),l=t(28243),d=t(35899),o=t(52378),a=t(48111),c=t(33519),x=t(21411),h=t(96625),p=t(4691),u=t(11585),j=t(8662),m=t(70084),v=t(27783),y=t(7872),g=t(94858),A=t(79505),f=t(71103),P=t(73237),w=t(92950),b=t(23870),k=t(85668),V=t.n(k);let{Search:D}=n.A,{RangePicker:S}=l.A,I=()=>{let[e,s]=(0,i.useState)([]),[t,n]=(0,i.useState)(!1),[l,k]=(0,i.useState)(!1),[I,Y]=(0,i.useState)(null),[q,C]=(0,i.useState)({totalUsers:0,vipUsers:0,normalUsers:0,vipRate:0,totalRevenue:0,avgDailyUnlocks:0}),[z,M]=(0,i.useState)(""),[_,B]=(0,i.useState)(""),[U,L]=(0,i.useState)(null),R=async()=>{n(!0);try{let e={};z&&(e.search=z),_&&(e.isVip="true"===_),U&&(e.startDate=U[0].format("YYYY-MM-DD"),e.endDate=U[1].format("YYYY-MM-DD"));let t=(await b.mz.getList(e)).users||[];s(t);let r=G(t);C(r)}catch{d.Ay.error("获取用户列表失败")}finally{n(!1)}},G=e=>{let s=e.length,t=e.filter(e=>e.isVip).length,r=s>0?e.reduce((e,s)=>e+s.dailyUnlockCount,0)/s:0;return{totalUsers:s,vipUsers:t,normalUsers:s-t,vipRate:s>0?t/s*100:0,totalRevenue:0,avgDailyUnlocks:r}};(0,i.useEffect)(()=>{R()},[z,_,U]);let H=e=>{Y(e),k(!0)},E=async e=>{try{await b.mz.updateVipStatus(e.id,!e.isVip),d.Ay.success(`${e.isVip?"取消":"设置"}VIP成功`),R()}catch{d.Ay.error("VIP状态更新失败")}},T=e=>!e||e.length<11?e:e.replace(/(\d{3})\d{4}(\d{4})/,"$1****$2"),$=[{title:"用户信息",key:"userInfo",width:250,render:(e,s)=>(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:4},children:[(0,r.jsx)("span",{style:{fontWeight:"bold",marginRight:8},children:s.nickname||"微信用户"}),s.isVip&&(0,r.jsx)(o.A,{color:"gold",icon:(0,r.jsx)(g.A,{}),children:"VIP"})]}),(0,r.jsxs)("div",{style:{fontSize:"12px",color:"#666",marginBottom:2},children:["手机: ",T(s.phone)]}),(0,r.jsxs)("div",{style:{fontSize:"12px",color:"#999"},children:["ID: ",s.id]})]})},{title:"游戏数据",key:"gameData",width:150,render:(e,s)=>(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{style:{marginBottom:2},children:["解锁: ",s.unlockedLevels," 关"]}),(0,r.jsxs)("div",{style:{marginBottom:2},children:["完成: ",s.completedLevelIds.length," 关"]}),(0,r.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["总游戏: ",s.totalGames," 次"]})]})},{title:"每日解锁",key:"dailyUnlock",width:150,render:(e,s)=>(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{children:["今日: ",s.dailyUnlockCount,"/",s.isVip?"∞":s.dailyUnlockLimit]}),(0,r.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["分享: ",s.totalShares," 次"]}),s.dailyShared&&(0,r.jsx)(o.A,{color:"green",children:"今日已分享"})]})},{title:"VIP状态",key:"vipStatus",width:100,render:(e,s)=>s.isVip?(0,r.jsx)(o.A,{color:"gold",icon:(0,r.jsx)(g.A,{}),children:"VIP"}):(0,r.jsx)(o.A,{color:"default",children:"普通"})},{title:"最后游戏",key:"lastPlay",width:160,render:(e,s)=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:V()(s.lastPlayTime).format("YYYY-MM-DD")}),(0,r.jsx)("div",{style:{fontSize:"12px",color:"#666"},children:V()(s.lastPlayTime).format("HH:mm:ss")})]})},{title:"注册时间",key:"createdAt",width:160,render:(e,s)=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:V()(s.createdAt).format("YYYY-MM-DD")}),(0,r.jsx)("div",{style:{fontSize:"12px",color:"#666"},children:V()(s.createdAt).format("HH:mm:ss")})]})},{title:"操作",key:"action",width:180,render:(e,s)=>(0,r.jsxs)(a.A,{size:"small",children:[(0,r.jsx)(c.A,{title:"查看详情",children:(0,r.jsx)(x.Ay,{type:"link",size:"small",icon:(0,r.jsx)(A.A,{}),onClick:()=>H(s),children:"详情"})}),(0,r.jsx)(c.A,{title:s.isVip?"取消VIP":"设为VIP",children:(0,r.jsx)(x.Ay,{type:"link",size:"small",icon:(0,r.jsx)(g.A,{}),style:{color:s.isVip?"#faad14":"#d9d9d9"},onClick:()=>E(s),children:"VIP"})})]})}];return(0,r.jsxs)("div",{style:{padding:"24px"},children:[(0,r.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,r.jsxs)("h1",{style:{fontSize:"24px",fontWeight:"bold",margin:0},children:[(0,r.jsx)(g.A,{style:{color:"#faad14",marginRight:"8px"}}),"VIP用户管理"]}),(0,r.jsx)("p",{style:{color:"#666",margin:"8px 0 0 0"},children:"管理VIP用户，查看用户游戏数据和VIP状态"})]}),(0,r.jsxs)(h.A,{gutter:16,style:{marginBottom:"24px"},children:[(0,r.jsx)(p.A,{span:6,children:(0,r.jsx)(u.A,{children:(0,r.jsx)(j.A,{title:"总用户数",value:q.totalUsers,prefix:(0,r.jsx)(f.A,{})})})}),(0,r.jsx)(p.A,{span:6,children:(0,r.jsx)(u.A,{children:(0,r.jsx)(j.A,{title:"VIP用户",value:q.vipUsers,valueStyle:{color:"#faad14"},prefix:(0,r.jsx)(g.A,{})})})}),(0,r.jsx)(p.A,{span:6,children:(0,r.jsx)(u.A,{children:(0,r.jsx)(j.A,{title:"VIP转化率",value:q.vipRate,precision:1,suffix:"%",valueStyle:{color:q.vipRate>=10?"#3f8600":"#cf1322"}})})}),(0,r.jsx)(p.A,{span:6,children:(0,r.jsx)(u.A,{children:(0,r.jsx)(j.A,{title:"平均解锁数",value:q.avgDailyUnlocks,precision:1,suffix:"次/日",prefix:(0,r.jsx)(P.A,{})})})})]}),(0,r.jsx)(u.A,{style:{marginBottom:"16px"},children:(0,r.jsxs)(h.A,{gutter:16,align:"middle",children:[(0,r.jsx)(p.A,{span:8,children:(0,r.jsx)(D,{placeholder:"搜索用户ID、手机号或昵称",allowClear:!0,onSearch:M,style:{width:"100%"}})}),(0,r.jsx)(p.A,{span:4,children:(0,r.jsxs)(m.A,{placeholder:"VIP状态",allowClear:!0,style:{width:"100%"},value:_||void 0,onChange:B,children:[(0,r.jsx)(m.A.Option,{value:"true",children:"VIP用户"}),(0,r.jsx)(m.A.Option,{value:"false",children:"普通用户"})]})}),(0,r.jsx)(p.A,{span:8,children:(0,r.jsx)(S,{style:{width:"100%"},value:U,onChange:e=>L(e),placeholder:["开始日期","结束日期"]})}),(0,r.jsx)(p.A,{span:4,children:(0,r.jsx)(x.Ay,{icon:(0,r.jsx)(w.A,{}),onClick:R,loading:t,style:{width:"100%"},children:"刷新"})})]})}),(0,r.jsx)(v.A,{columns:$,dataSource:e,rowKey:"id",loading:t,pagination:{total:e.length,pageSize:20,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 个用户`},scroll:{x:1200}}),(0,r.jsx)(y.A,{title:"用户详情",open:l,onCancel:()=>k(!1),footer:[(0,r.jsx)(x.Ay,{onClick:()=>k(!1),children:"关闭"},"close")],width:700,children:I&&(0,r.jsxs)("div",{children:[(0,r.jsxs)(h.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"用户ID:"})," ",I.id]})}),(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"VIP状态:"}),I.isVip?(0,r.jsx)(o.A,{color:"gold",icon:(0,r.jsx)(g.A,{}),style:{marginLeft:8},children:"VIP"}):(0,r.jsx)(o.A,{color:"default",style:{marginLeft:8},children:"普通用户"})]})})]}),(0,r.jsxs)(h.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"手机号:"})," ",I.phone]})}),(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"昵称:"})," ",I.nickname||"-"]})})]}),(0,r.jsxs)(h.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"OpenID:"})," ",I.openid||"-"]})}),(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"每日解锁限制:"})," ",I.isVip?"无限制":`${I.dailyUnlockLimit}次`]})})]}),(0,r.jsxs)(h.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"今日解锁次数:"})," ",I.dailyUnlockCount]})}),(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"总分享次数:"})," ",I.totalShares]})})]}),(0,r.jsxs)(h.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"已解锁关卡:"})," ",I.unlockedLevels]})}),(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"已完成关卡:"})," ",I.completedLevelIds.length]})})]}),(0,r.jsxs)(h.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"总游戏次数:"})," ",I.totalGames]})}),(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"总通关次数:"})," ",I.totalCompletions]})})]}),(0,r.jsxs)(h.A,{gutter:16,style:{marginBottom:"16px"},children:[(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"最后游戏时间:"})," ",V()(I.lastPlayTime).format("YYYY-MM-DD HH:mm:ss")]})}),(0,r.jsx)(p.A,{span:12,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"注册时间:"})," ",V()(I.createdAt).format("YYYY-MM-DD HH:mm:ss")]})})]}),(0,r.jsx)(h.A,{gutter:16,children:(0,r.jsx)(p.A,{span:24,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"今日是否已分享:"}),I.dailyShared?(0,r.jsx)(o.A,{color:"green",style:{marginLeft:8},children:"已分享"}):(0,r.jsx)(o.A,{color:"default",style:{marginLeft:8},children:"未分享"})]})})})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19475:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>a});var r=t(65239),i=t(48088),n=t(88170),l=t.n(n),d=t(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(s,o);let a={children:["",{children:["(admin)",{children:["vip-users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78084)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\vip-users\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\vip-users\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(admin)/vip-users/page",pathname:"/vip-users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:a}})},21820:e=>{"use strict";e.exports=require("os")},26075:(e,s,t)=>{Promise.resolve().then(t.bind(t,6714))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60419:(e,s,t)=>{Promise.resolve().then(t.bind(t,78084))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78084:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\vip-users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\vip-users\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,433,658,816,204,331,553,84,579,542,210,451],()=>t(19475));module.exports=r})();