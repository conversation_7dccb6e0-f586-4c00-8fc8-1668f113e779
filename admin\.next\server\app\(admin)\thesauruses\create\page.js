(()=>{var e={};e.id=221,e.ids=[221],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10715:(e,t,r)=>{Promise.resolve().then(r.bind(r,72315))},10814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11235:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},12412:e=>{"use strict";e.exports=require("assert")},16603:(e,t,r)=>{Promise.resolve().then(r.bind(r,65266)),Promise.resolve().then(r.t.bind(r,28087,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24600:(e,t,r)=>{Promise.resolve().then(r.bind(r,10814))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35692:()=>{},37912:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(60687),i=r(43210),n=r(85814),a=r.n(n),o=r(16189),l=r(98836),d=r(99053),c=r(63736),p=r(56072),u=r(78620),h=r(60203),m=r(53788),x=r(28859),y=r(81945),b=r(3788),v=r(73237),f=r(94858),g=r(47453),A=r(72061),j=r(80461),P=r(71103);let{Header:k,Content:w,Sider:_,Footer:q}=l.A,I=[{key:"dashboard",label:"主控面板",path:"/dashboard",icon:(0,s.jsx)(h.A,{})},{key:"levels",label:"关卡管理",path:"/levels",icon:(0,s.jsx)(m.A,{})},{key:"phrases",label:"词组管理",path:"/phrases",icon:(0,s.jsx)(x.A,{})},{key:"users",label:"用户管理",path:"/users",icon:(0,s.jsx)(y.A,{})},{key:"shares",label:"分享管理",path:"/shares",icon:(0,s.jsx)(b.A,{})},{key:"vip-packages",label:"VIP套餐管理",path:"/vip-packages",icon:(0,s.jsx)(v.A,{})},{key:"vip-users",label:"VIP用户管理",path:"/vip-users",icon:(0,s.jsx)(f.A,{})},{key:"payment-orders",label:"支付订单管理",path:"/payment-orders",icon:(0,s.jsx)(g.A,{})},{key:"settings",label:"小程序设置",path:"/settings",icon:(0,s.jsx)(A.A,{})}];function C({children:e}){let t=(0,o.useRouter)(),r=(0,o.usePathname)(),[n,h]=(0,i.useState)(!1),m=[{key:"logout",icon:(0,s.jsx)(j.A,{}),label:"退出登录",onClick:()=>{localStorage.removeItem("admin_token"),t.push("/login")}}],x=I.find(e=>r.startsWith(e.path))?.key||"dashboard";return(0,s.jsxs)(l.A,{style:{minHeight:"100vh"},children:[(0,s.jsxs)(_,{collapsible:!0,collapsed:n,onCollapse:e=>h(e),children:[(0,s.jsx)("div",{style:{height:32,margin:16,background:"rgba(255, 255, 255, 0.2)",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,s.jsx)(d.A.Text,{style:{color:"white",fontSize:n?"10px":"16px",transition:"font-size 0.2s"},children:n?"后台":"游戏管理后台"})}),(0,s.jsx)(c.A,{theme:"dark",selectedKeys:[x],mode:"inline",items:I.map(e=>({key:e.key,icon:e.icon,label:(0,s.jsx)(a(),{href:e.path,children:e.label})}))})]}),(0,s.jsxs)(l.A,{children:[(0,s.jsxs)(k,{style:{padding:"0 16px",background:"#fff",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[(0,s.jsx)(p.A,{menu:{items:m},placement:"bottomRight",children:(0,s.jsx)(u.A,{style:{cursor:"pointer"},icon:(0,s.jsx)(P.A,{})})}),(0,s.jsx)("span",{style:{marginLeft:8},children:"Admin"})]}),(0,s.jsx)(w,{style:{margin:"16px"},children:e}),(0,s.jsxs)(q,{style:{textAlign:"center"},children:["游戏管理后台 \xa9",new Date().getFullYear()]})]})]})}},45059:(e,t,r)=>{Promise.resolve().then(r.bind(r,65677))},52931:(e,t,r)=>{"use strict";r.d(t,{Au:()=>i,Io:()=>a,LQ:()=>n,zN:()=>o});var s=r(98501);let i={getAll:async()=>(await s.F.get("/api/v1/thesauruses")).data,getById:async e=>(await s.F.get(`/api/v1/thesauruses/${e}`)).data,create:async e=>(await s.F.post("/api/v1/thesauruses",e)).data,update:async(e,t)=>(await s.F.patch(`/api/v1/thesauruses/${e}`,t)).data,delete:async e=>{await s.F.delete(`/api/v1/thesauruses/${e}`)},addPhrase:async(e,t)=>(await s.F.post(`/api/v1/thesauruses/${e}/phrases`,t)).data,removePhrase:async(e,t)=>(await s.F.delete(`/api/v1/thesauruses/${e}/phrases/${t}`)).data},n=i.create;i.update,i.delete;let a=i.delete;i.getById,i.getAll;let o=i.getAll},53395:(e,t,r)=>{Promise.resolve().then(r.bind(r,6468)),Promise.resolve().then(r.bind(r,43741))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59448:(e,t,r)=>{Promise.resolve().then(r.bind(r,37912))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65677:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\thesauruses\\\\create\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\create\\page.tsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72315:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(60687),i=r(43210),n=r(99053),a=r(28173),o=r(35899),l=r(11585),d=r(94733),c=r(21411),p=r(16189),u=r(52931);let{Title:h}=n.A,m=()=>{let[e]=a.A.useForm(),t=(0,p.useRouter)(),[r,n]=(0,i.useState)(!1),m=async e=>{n(!0);try{await (0,u.LQ)(e),o.Ay.success("词库创建成功！"),t.push("/thesauruses")}catch(e){o.Ay.error(e.message||"词库创建失败！")}finally{n(!1)}};return(0,s.jsxs)(l.A,{children:[(0,s.jsx)(h,{level:3,children:"创建新词库"}),(0,s.jsxs)(a.A,{form:e,layout:"vertical",onFinish:m,children:[(0,s.jsx)(a.A.Item,{name:"name",label:"词库名称",rules:[{required:!0,message:"请输入词库名称"}],children:(0,s.jsx)(d.A,{placeholder:"例如：日常用语"})}),(0,s.jsx)(a.A.Item,{name:"description",label:"描述 (可选)",children:(0,s.jsx)(d.A.TextArea,{rows:3,placeholder:"词库简介"})}),(0,s.jsxs)(a.A.Item,{children:[(0,s.jsx)(c.Ay,{type:"primary",htmlType:"submit",loading:r,children:"创建词库"}),(0,s.jsx)(c.Ay,{style:{marginLeft:8},onClick:()=>t.back(),children:"取消"})]})]})]})}},74075:e=>{"use strict";e.exports=require("zlib")},76891:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(37413);r(61120);var i=r(68016);r(35692),r(28087);let n=({children:e})=>(0,s.jsxs)("html",{lang:"en",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("meta",{charSet:"utf-8"}),(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,s.jsx)("meta",{name:"description",content:"游戏后台管理系统"}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,s.jsx)("title",{children:"游戏管理后台"})]}),(0,s.jsx)("body",{children:(0,s.jsx)(i.Z,{children:e})})]})},94735:e=>{"use strict";e.exports=require("events")},98473:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),i=r(48088),n=r(88170),a=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["(admin)",{children:["thesauruses",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,65677)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\thesauruses\\create\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(admin)/thesauruses/create/page",pathname:"/thesauruses/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},98501:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var s=r(51060),i=r(35899);let n={BASE_URL:"http://localhost:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return`${this.BASE_URL}${this.API_PREFIX}`}},a=s.A.create({baseURL:n.BASE_URL,timeout:n.TIMEOUT,headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),a.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){let{status:t,data:r}=e.response;switch(t){case 401:i.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:i.Ay.error("没有权限访问该资源");break;case 404:i.Ay.error("请求的资源不存在");break;case 500:i.Ay.error("服务器内部错误");break;default:i.Ay.error(r?.message||"请求失败")}}else e.request?i.Ay.error("网络连接失败，请检查网络"):i.Ay.error("请求配置错误");return Promise.reject(e)});let o={get:(e,t)=>a.get(e,t),post:(e,t,r)=>a.post(e,t,r),put:(e,t,r)=>a.put(e,t,r),patch:(e,t,r)=>a.patch(e,t,r),delete:(e,t)=>a.delete(e,t)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,433,658,816,204,331,173],()=>r(98473));module.exports=s})();