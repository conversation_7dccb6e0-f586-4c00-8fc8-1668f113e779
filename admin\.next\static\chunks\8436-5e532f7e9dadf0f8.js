"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8436],{28562:(e,t,r)=>{r.d(t,{A:()=>w});var a=r(12115),n=r(29300),c=r.n(n),l=r(32417),o=r(74686),s=r(39496),i=r(15982),u=r(68151),d=r(9836),g=r(51854);let p=a.createContext({});var f=r(85573),m=r(18184),v=r(45431),h=r(61388);let b=e=>{let{antCls:t,componentCls:r,iconCls:a,avatarBg:n,avatarColor:c,containerSize:l,containerSizeLG:o,containerSizeSM:s,textFontSize:i,textFontSizeLG:u,textFontSizeSM:d,borderRadius:g,borderRadiusLG:p,borderRadiusSM:v,lineWidth:h,lineType:b}=e,y=(e,t,n)=>({width:e,height:e,borderRadius:"50%",["&".concat(r,"-square")]:{borderRadius:n},["&".concat(r,"-icon")]:{fontSize:t,["> ".concat(a)]:{margin:0}}});return{[r]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,m.dF)(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:c,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:n,border:"".concat((0,f.zA)(h)," ").concat(b," transparent"),"&-image":{background:"transparent"},["".concat(t,"-image-img")]:{display:"block"}}),y(l,i,g)),{"&-lg":Object.assign({},y(o,u,p)),"&-sm":Object.assign({},y(s,d,v)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},y=e=>{let{componentCls:t,groupBorderColor:r,groupOverlapping:a,groupSpace:n}=e;return{["".concat(t,"-group")]:{display:"inline-flex",[t]:{borderColor:r},"> *:not(:first-child)":{marginInlineStart:a}},["".concat(t,"-group-popover")]:{["".concat(t," + ").concat(t)]:{marginInlineStart:n}}}},O=(0,v.OF)("Avatar",e=>{let{colorTextLightSolid:t,colorTextPlaceholder:r}=e,a=(0,h.oX)(e,{avatarBg:r,avatarColor:t});return[b(a),y(a)]},e=>{let{controlHeight:t,controlHeightLG:r,controlHeightSM:a,fontSize:n,fontSizeLG:c,fontSizeXL:l,fontSizeHeading3:o,marginXS:s,marginXXS:i,colorBorderBg:u}=e;return{containerSize:t,containerSizeLG:r,containerSizeSM:a,textFontSize:Math.round((c+l)/2),textFontSizeLG:o,textFontSizeSM:n,groupSpace:i,groupOverlapping:-s,groupBorderColor:u}});var z=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)0>t.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r};let S=a.forwardRef((e,t)=>{let r,{prefixCls:n,shape:f,size:m,src:v,srcSet:h,icon:b,className:y,rootClassName:S,style:j,alt:E,draggable:x,children:A,crossOrigin:w,gap:C=4,onError:k}=e,N=z(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[M,R]=a.useState(1),[H,V]=a.useState(!1),[F,L]=a.useState(!0),B=a.useRef(null),I=a.useRef(null),P=(0,o.K4)(t,B),{getPrefixCls:_,avatar:G}=a.useContext(i.QO),W=a.useContext(p),Q=()=>{if(!I.current||!B.current)return;let e=I.current.offsetWidth,t=B.current.offsetWidth;0!==e&&0!==t&&2*C<t&&R(t-2*C<e?(t-2*C)/e:1)};a.useEffect(()=>{V(!0)},[]),a.useEffect(()=>{L(!0),R(1)},[v]),a.useEffect(Q,[C]);let T=(0,d.A)(e=>{var t,r;return null!=(r=null!=(t=null!=m?m:null==W?void 0:W.size)?t:e)?r:"default"}),q=Object.keys("object"==typeof T&&T||{}).some(e=>["xs","sm","md","lg","xl","xxl"].includes(e)),K=(0,g.A)(q),X=a.useMemo(()=>{if("object"!=typeof T)return{};let e=T[s.ye.find(e=>K[e])];return e?{width:e,height:e,fontSize:e&&(b||A)?e/2:18}:{}},[K,T]),D=_("avatar",n),J=(0,u.A)(D),[U,Y,Z]=O(D,J),$=c()({["".concat(D,"-lg")]:"large"===T,["".concat(D,"-sm")]:"small"===T}),ee=a.isValidElement(v),et=f||(null==W?void 0:W.shape)||"circle",er=c()(D,$,null==G?void 0:G.className,"".concat(D,"-").concat(et),{["".concat(D,"-image")]:ee||v&&F,["".concat(D,"-icon")]:!!b},Z,J,y,S,Y),ea="number"==typeof T?{width:T,height:T,fontSize:b?T/2:18}:{};if("string"==typeof v&&F)r=a.createElement("img",{src:v,draggable:x,srcSet:h,onError:()=>{!1!==(null==k?void 0:k())&&L(!1)},alt:E,crossOrigin:w});else if(ee)r=v;else if(b)r=b;else if(H||1!==M){let e="scale(".concat(M,")");r=a.createElement(l.A,{onResize:Q},a.createElement("span",{className:"".concat(D,"-string"),ref:I,style:Object.assign({},{msTransform:e,WebkitTransform:e,transform:e})},A))}else r=a.createElement("span",{className:"".concat(D,"-string"),style:{opacity:0},ref:I},A);return U(a.createElement("span",Object.assign({},N,{style:Object.assign(Object.assign(Object.assign(Object.assign({},ea),X),null==G?void 0:G.style),j),className:er,ref:P}),r))});var j=r(63715),E=r(80163),x=r(56200);let A=e=>{let{size:t,shape:r}=a.useContext(p),n=a.useMemo(()=>({size:e.size||t,shape:e.shape||r}),[e.size,e.shape,t,r]);return a.createElement(p.Provider,{value:n},e.children)};S.Group=e=>{var t,r,n,l;let{getPrefixCls:o,direction:s}=a.useContext(i.QO),{prefixCls:d,className:g,rootClassName:p,style:f,maxCount:m,maxStyle:v,size:h,shape:b,maxPopoverPlacement:y,maxPopoverTrigger:z,children:w,max:C}=e,k=o("avatar",d),N="".concat(k,"-group"),M=(0,u.A)(k),[R,H,V]=O(k,M),F=c()(N,{["".concat(N,"-rtl")]:"rtl"===s},V,M,g,p,H),L=(0,j.A)(w).map((e,t)=>(0,E.Ob)(e,{key:"avatar-key-".concat(t)})),B=(null==C?void 0:C.count)||m,I=L.length;if(B&&B<I){let e=L.slice(0,B),o=L.slice(B,I),s=(null==C?void 0:C.style)||v,i=(null==(t=null==C?void 0:C.popover)?void 0:t.trigger)||z||"hover",u=(null==(r=null==C?void 0:C.popover)?void 0:r.placement)||y||"top",d=Object.assign(Object.assign({content:o},null==C?void 0:C.popover),{classNames:{root:c()("".concat(N,"-popover"),null==(l=null==(n=null==C?void 0:C.popover)?void 0:n.classNames)?void 0:l.root)},placement:u,trigger:i});return e.push(a.createElement(x.A,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},d),a.createElement(S,{style:s},"+".concat(I-B)))),R(a.createElement(A,{shape:b,size:h},a.createElement("div",{className:F,style:f},e)))}return R(a.createElement(A,{shape:b,size:h},a.createElement("div",{className:F,style:f},L)))};let w=S},34095:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(79630),n=r(12115);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 310H732.4c13.6-21.4 21.6-46.8 21.6-74 0-76.1-61.9-138-138-138-41.4 0-78.7 18.4-104 47.4-25.3-29-62.6-47.4-104-47.4-76.1 0-138 61.9-138 138 0 27.2 7.9 52.6 21.6 74H144c-17.7 0-32 14.3-32 32v200c0 4.4 3.6 8 8 8h40v344c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V550h40c4.4 0 8-3.6 8-8V342c0-17.7-14.3-32-32-32zm-334-74c0-38.6 31.4-70 70-70s70 31.4 70 70-31.4 70-70 70h-70v-70zm-138-70c38.6 0 70 31.4 70 70v70h-70c-38.6 0-70-31.4-70-70s31.4-70 70-70zM180 482V378h298v104H180zm48 68h250v308H228V550zm568 308H546V550h250v308zm48-376H546V378h298v104z"}}]},name:"gift",theme:"outlined"};var l=r(62764);let o=n.forwardRef(function(e,t){return n.createElement(l.A,(0,a.A)({},e,{ref:t,icon:c}))})},50274:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(79630),n=r(12115);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};var l=r(62764);let o=n.forwardRef(function(e,t){return n.createElement(l.A,(0,a.A)({},e,{ref:t,icon:c}))})},97550:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(79630),n=r(12115);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M899.6 276.5L705 396.4 518.4 147.5a8.06 8.06 0 00-12.9 0L319 396.4 124.3 276.5c-5.7-3.5-13.1 1.2-12.2 7.9L188.5 865c1.1 7.9 7.9 14 16 14h615.1c8 0 14.9-6 15.9-14l76.4-580.6c.8-6.7-6.5-11.4-12.3-7.9zm-126 534.1H250.3l-53.8-409.4 139.8 86.1L512 252.9l175.7 234.4 139.8-86.1-53.9 409.4zM512 509c-62.1 0-112.6 50.5-112.6 112.6S449.9 734.2 512 734.2s112.6-50.5 112.6-112.6S574.1 509 512 509zm0 160.9c-26.6 0-48.2-21.6-48.2-48.3 0-26.6 21.6-48.3 48.2-48.3s48.2 21.6 48.2 48.3c0 26.6-21.6 48.3-48.2 48.3z"}}]},name:"crown",theme:"outlined"};var l=r(62764);let o=n.forwardRef(function(e,t){return n.createElement(l.A,(0,a.A)({},e,{ref:t,icon:c}))})}}]);