"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9179],{21073:(a,t,e)=>{e.d(t,{Au:()=>i,Io:()=>n,LQ:()=>c,zN:()=>r});var s=e(29799);let i={getAll:async()=>(await s.F.get("/api/v1/thesauruses")).data,getById:async a=>(await s.F.get("/api/v1/thesauruses/".concat(a))).data,create:async a=>(await s.F.post("/api/v1/thesauruses",a)).data,update:async(a,t)=>(await s.F.patch("/api/v1/thesauruses/".concat(a),t)).data,delete:async a=>{await s.F.delete("/api/v1/thesauruses/".concat(a))},addPhrase:async(a,t)=>(await s.F.post("/api/v1/thesauruses/".concat(a,"/phrases"),t)).data,removePhrase:async(a,t)=>(await s.F.delete("/api/v1/thesauruses/".concat(a,"/phrases/").concat(t))).data},c=i.create;i.update,i.delete;let n=i.delete;i.getById,i.getAll;let r=i.getAll},22918:(a,t,e)=>{e.d(t,{f:()=>i});var s=e(29799);let i={getAll:async()=>(await s.F.get("/api/v1/settings")).data,getById:async a=>(await s.F.get("/api/v1/settings/".concat(a))).data,getByKey:async a=>(await s.F.get("/api/v1/settings/key/".concat(a))).data,update:async(a,t)=>(await s.F.patch("/api/v1/settings/".concat(a),t)).data,updateByKey:async(a,t)=>(await s.F.patch("/api/v1/settings/key/".concat(a),{value:t})).data,async initializeDefaults(){await s.F.post("/api/v1/settings/initialize")},async updateAppConfig(a){let t=[];void 0!==a.helpUrl&&t.push(this.updateByKey("help_url",a.helpUrl)),void 0!==a.backgroundMusicUrl&&t.push(this.updateByKey("background_music_url",a.backgroundMusicUrl)),await Promise.all(t)},async getAppConfig(){try{let[a,t]=await Promise.all([this.getByKey("help_url").catch(()=>null),this.getByKey("background_music_url").catch(()=>null)]);return{helpUrl:(null==a?void 0:a.value)||"",backgroundMusicUrl:(null==t?void 0:t.value)||""}}catch(a){return console.error("获取小程序配置失败:",a),{helpUrl:"",backgroundMusicUrl:""}}},async testWeixinAppSettings(){try{return(await s.F.get("/weixin/app-settings")).data}catch(a){throw console.error("测试微信小程序设置接口失败:",a),a}},async testWeixinGlobalConfig(){try{return(await s.F.get("/weixin/global-config")).data}catch(a){throw console.error("测试微信小程序全局配置接口失败:",a),a}}}},29799:(a,t,e)=>{e.d(t,{F:()=>r});var s=e(23464),i=e(19868);let c={BASE_URL:"http://127.0.0.1:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}},n=s.A.create({baseURL:c.BASE_URL,timeout:c.TIMEOUT,headers:{"Content-Type":"application/json"}});n.interceptors.request.use(a=>{let t=localStorage.getItem("admin_token");return t&&(a.headers.Authorization="Bearer ".concat(t)),a},a=>(console.error("请求拦截器错误:",a),Promise.reject(a))),n.interceptors.response.use(a=>a,a=>{if(console.error("响应拦截器错误:",a),a.response){let{status:t,data:e}=a.response;switch(t){case 401:i.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:i.Ay.error("没有权限访问该资源");break;case 404:i.Ay.error("请求的资源不存在");break;case 500:i.Ay.error("服务器内部错误");break;default:i.Ay.error((null==e?void 0:e.message)||"请求失败")}}else a.request?i.Ay.error("网络连接失败，请检查网络"):i.Ay.error("请求配置错误");return Promise.reject(a)});let r={get:(a,t)=>n.get(a,t),post:(a,t,e)=>n.post(a,t,e),put:(a,t,e)=>n.put(a,t,e),patch:(a,t,e)=>n.patch(a,t,e),delete:(a,t)=>n.delete(a,t)}},49179:(a,t,e)=>{e.d(t,{k3:()=>r,Yi:()=>d,LY:()=>c,Au:()=>n.Au,Dv:()=>l,HK:()=>p,mz:()=>g});var s=e(29799),i=e(59959);let c={getAll:async()=>(await s.F.get("/api/v1/phrases")).data,getById:async a=>(await s.F.get("/api/v1/phrases/".concat(a))).data,create:async a=>(await s.F.post("/api/v1/phrases",a)).data,update:async(a,t)=>(await s.F.patch("/api/v1/phrases/".concat(a),t)).data,delete:async a=>{await s.F.delete("/api/v1/phrases/".concat(a))}};var n=e(21073);let r={getAll:async()=>(await s.F.get("/api/v1/levels")).data,getById:async a=>(await s.F.get("/api/v1/levels/".concat(a))).data,create:async a=>(await s.F.post("/api/v1/levels",a)).data,update:async(a,t)=>(await s.F.patch("/api/v1/levels/".concat(a),t)).data,delete:async a=>{await s.F.delete("/api/v1/levels/".concat(a))},addPhrase:async(a,t)=>(await s.F.post("/api/v1/levels/".concat(a,"/phrases"),t)).data,removePhrase:async(a,t)=>(await s.F.delete("/api/v1/levels/".concat(a,"/phrases/").concat(t))).data,getCount:async()=>(await s.F.get("/api/v1/levels/count")).data,getByDifficulty:async a=>(await s.F.get("/api/v1/levels/difficulty/".concat(a))).data,getWithPhrases:async a=>(await s.F.get("/api/v1/levels/".concat(a,"/with-phrases"))).data};r.getAll;let l={getAll:async()=>(await s.F.get("/api/v1/users")).data,getById:async a=>(await s.F.get("/api/v1/users/".concat(a))).data,getByOpenid:async a=>(await s.F.get("/api/v1/users/by-openid?openid=".concat(a))).data,getByPhone:async a=>(await s.F.get("/api/v1/users/by-phone?phone=".concat(a))).data,create:async a=>(await s.F.post("/api/v1/users",a)).data,update:async(a,t)=>(await s.F.patch("/api/v1/users/".concat(a),t)).data,delete:async a=>{await s.F.delete("/api/v1/users/".concat(a))},completeLevel:async(a,t)=>(await s.F.post("/api/v1/users/".concat(a,"/complete-level"),t)).data,startGame:async a=>(await s.F.post("/api/v1/users/".concat(a,"/start-game"))).data,getStats:async a=>(await s.F.get("/api/v1/users/".concat(a,"/stats"))).data,resetProgress:async a=>(await s.F.post("/api/v1/users/".concat(a,"/reset-progress"))).data};var o=e(73884);let p={getList:async()=>(await s.F.get("/api/v1/payment/vip-packages")).data||[],getById:async a=>(await s.F.get("/api/v1/payment/vip-packages/".concat(a))).data,create:async a=>(await s.F.post("/api/v1/payment/vip-packages",a)).data,update:async(a,t)=>(await s.F.put("/api/v1/payment/vip-packages/".concat(a),t)).data,async delete(a){await s.F.delete("/api/v1/payment/vip-packages/".concat(a))},toggleStatus:async(a,t)=>(await s.F.patch("/api/v1/payment/vip-packages/".concat(a,"/status"),{isActive:t})).data},d={getList:async a=>(await s.F.get("/api/v1/payment/orders",{params:a})).data,getStats:async a=>(await s.F.get("/api/v1/payment/orders/stats",{params:a})).data,getById:async a=>(await s.F.get("/api/v1/payment/orders/".concat(a))).data,queryStatus:async a=>(await s.F.get("/api/v1/payment/query/".concat(a))).data,refreshStatus:async a=>(await s.F.post("/api/v1/payment/refresh/".concat(a))).data,async cancel(a,t){await s.F.post("/payment/cancel/".concat(a),{userId:t})}},g={async getList(a){var t,e;let i=await s.F.get("/api/v1/users",{params:a});return{users:(null==(t=i.data)?void 0:t.users)||[],total:(null==(e=i.data)?void 0:e.total)||0}},getById:async a=>(await s.F.get("/api/v1/users/".concat(a))).data,updateVipStatus:async(a,t)=>(await s.F.put("/api/v1/users/".concat(a),{isVip:t,dailyUnlockLimit:t?999999:15})).data};var u=e(22918);i.y,n.Au,o.Dw,u.f},59959:(a,t,e)=>{e.d(t,{y:()=>i});var s=e(29799);let i={login:async a=>(await s.F.post("/api/v1/auth/login",a)).data,logout:()=>{localStorage.removeItem("admin_token"),window.location.href="/login"},getToken:()=>localStorage.getItem("admin_token"),setToken:a=>{localStorage.setItem("admin_token",a)},isLoggedIn:()=>!!localStorage.getItem("admin_token")};i.login},73884:(a,t,e)=>{e.d(t,{Dw:()=>i,WS:()=>c,cm:()=>n});var s=e(29799);class i{static async getAllShareConfigs(){return(await s.F.get("/api/v1/share")).data}static async getActiveShareConfigs(){return(await s.F.get("/api/v1/share/active")).data}static async getDefaultShareConfig(){return(await s.F.get("/api/v1/share/default")).data}static async getShareConfigByType(a){return(await s.F.get("/api/v1/share/type/".concat(a))).data}static async getShareConfigById(a){return(await s.F.get("/api/v1/share/".concat(a))).data}static async createShareConfig(a){return(await s.F.post("/api/v1/share",a)).data}static async updateShareConfig(a,t){return(await s.F.patch("/api/v1/share/".concat(a),t)).data}static async toggleShareConfig(a){return(await s.F.put("/api/v1/share/".concat(a,"/toggle"))).data}static async deleteShareConfig(a){return(await s.F.delete("/api/v1/share/".concat(a))).data}}let c=[{value:"default",label:"默认分享",description:"通用分享，适用于首页、游戏页等"},{value:"result",label:"结果分享",description:"展示用户成绩的分享"},{value:"level",label:"关卡分享",description:"邀请好友挑战特定关卡"},{value:"achievement",label:"成就分享",description:"展示用户获得的成就"},{value:"custom",label:"自定义分享",description:"特殊活动或自定义场景"}],n=[{label:"首页",value:"/pages/index/index",description:"小程序首页"},{label:"游戏页",value:"/pages/game/game",description:"游戏主页面"},{label:"关卡页",value:"/pages/level/level?id={levelId}",description:"特定关卡页面，{levelId}会被替换为实际关卡ID"},{label:"结果页",value:"/pages/result/result?score={score}",description:"结果展示页面，{score}会被替换为实际分数"},{label:"排行榜",value:"/pages/rank/rank",description:"排行榜页面"}]}}]);