"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateVipPackageId = generateVipPackageId;
exports.generateShortVipPackageId = generateShortVipPackageId;
exports.generateSemanticVipPackageId = generateSemanticVipPackageId;
exports.generatePaymentOrderNo = generatePaymentOrderNo;
exports.generateUUID = generateUUID;
exports.generateShortUUID = generateShortUUID;
exports.generateNumericId = generateNumericId;
exports.generateUniqueId = generateUniqueId;
const uuid_1 = require("uuid");
function generateVipPackageId() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    const randomStr = Math.random().toString(36).substring(2, 6);
    return `vip_${year}${month}${day}_${hour}${minute}${second}_${randomStr}`;
}
function generateShortVipPackageId() {
    const randomStr = Math.random().toString(36).substring(2, 10);
    return `vip_${randomStr}`;
}
function generateSemanticVipPackageId(name, duration) {
    let type = 'custom';
    if (duration <= 7) {
        type = 'weekly';
    }
    else if (duration <= 31) {
        type = 'monthly';
    }
    else if (duration <= 93) {
        type = 'quarterly';
    }
    else if (duration <= 366) {
        type = 'yearly';
    }
    const randomStr = Math.random().toString(36).substring(2, 6);
    return `vip_${type}_${duration}d_${randomStr}`;
}
function generatePaymentOrderNo() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    const randomStr = Math.random().toString(36).substring(2, 8);
    return `PAY_${year}${month}${day}${hour}${minute}${second}_${randomStr}`;
}
function generateUUID() {
    return (0, uuid_1.v4)().replace(/-/g, '');
}
function generateShortUUID() {
    return (0, uuid_1.v4)().replace(/-/g, '').substring(0, 12);
}
function generateNumericId() {
    return Math.floor(10000000 + Math.random() * 90000000).toString();
}
async function generateUniqueId(checkFn, generateFn, maxRetries = 10) {
    for (let i = 0; i < maxRetries; i++) {
        const id = generateFn();
        const exists = await checkFn(id);
        if (!exists) {
            return id;
        }
    }
    throw new Error(`无法生成唯一ID，已重试${maxRetries}次`);
}
//# sourceMappingURL=id-generator.js.map