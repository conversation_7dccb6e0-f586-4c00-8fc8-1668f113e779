(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[284],{2735:(e,s,l)=>{Promise.resolve().then(l.bind(l,79094))},79094:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>P});var t=l(95155),i=l(12115),r=l(97605),n=l(19868),a=l(19361),c=l(74947),x=l(505),o=l(44297),d=l(59474),j=l(30662),h=l(50274),A=l(97550),m=l(77133),u=l(73086),v=l(18517),g=l(36020),p=l(46996),f=l(52092),y=l(34095),k=l(35695),L=l(49179);let{Title:C,Paragraph:b}=r.A;function P(){let e=(0,k.useRouter)(),[s,l]=(0,i.useState)(!0),[r,P]=(0,i.useState)({totalUsers:0,totalPhrases:0,totalLevels:0,maxLevels:1e3,remainingLevels:1e3,activeUsers:0,totalGames:0,totalCompletions:0,vipUsers:0,vipRate:0,totalOrders:0,successOrders:0,totalRevenue:0}),O=async()=>{l(!0);try{let[e,s,l]=await Promise.all([L.k3.getCount(),L.LY.getAll(),L.Dv.getAll()]),t=l.users||[],i=new Date,r=new Date(i.getTime()-6048e5),n=t.filter(e=>new Date(e.lastPlayTime)>=r).length,a=t.reduce((e,s)=>e+s.totalGames,0),c=t.reduce((e,s)=>e+s.totalCompletions,0),x=t.filter(e=>e.isVip).length,o=t.length>0?x/t.length*100:0;P({totalUsers:t.length,totalPhrases:s.length,totalLevels:e.total,maxLevels:e.maxLevels,remainingLevels:e.remaining,activeUsers:n,totalGames:a,totalCompletions:c,vipUsers:x,vipRate:o,totalOrders:0,successOrders:0,totalRevenue:0})}catch(e){n.Ay.error("获取统计数据失败")}finally{l(!1)}};(0,i.useEffect)(()=>{O()},[]);let U=r.totalLevels/r.maxLevels*100,R=r.totalGames>0?r.totalCompletions/r.totalGames*100:0;return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{style:{marginBottom:24},children:[(0,t.jsx)(C,{level:2,children:"主控面板"}),(0,t.jsx)(b,{children:"欢迎使用消消乐游戏管理后台！这里展示了系统的核心统计信息和快捷操作入口。"})]}),(0,t.jsxs)(a.A,{gutter:[16,16],style:{marginBottom:24},children:[(0,t.jsx)(c.A,{xs:24,sm:12,lg:6,children:(0,t.jsx)(x.A,{children:(0,t.jsx)(o.A,{title:"总用户数",value:r.totalUsers,prefix:(0,t.jsx)(h.A,{}),loading:s})})}),(0,t.jsx)(c.A,{xs:24,sm:12,lg:6,children:(0,t.jsx)(x.A,{children:(0,t.jsx)(o.A,{title:"VIP用户",value:r.vipUsers,prefix:(0,t.jsx)(A.A,{}),suffix:"/ ".concat(r.totalUsers),valueStyle:{color:"#faad14"},loading:s})})}),(0,t.jsx)(c.A,{xs:24,sm:12,lg:6,children:(0,t.jsx)(x.A,{children:(0,t.jsx)(o.A,{title:"词组总数",value:r.totalPhrases,prefix:(0,t.jsx)(m.A,{}),loading:s})})}),(0,t.jsx)(c.A,{xs:24,sm:12,lg:6,children:(0,t.jsx)(x.A,{children:(0,t.jsx)(o.A,{title:"关卡总数",value:r.totalLevels,prefix:(0,t.jsx)(u.A,{}),suffix:"/ ".concat(r.maxLevels),loading:s})})})]}),(0,t.jsxs)(a.A,{gutter:[16,16],style:{marginBottom:24},children:[(0,t.jsx)(c.A,{xs:24,sm:12,lg:6,children:(0,t.jsx)(x.A,{children:(0,t.jsx)(o.A,{title:"VIP转化率",value:r.vipRate,precision:1,suffix:"%",valueStyle:{color:r.vipRate>=10?"#3f8600":"#cf1322"},loading:s})})}),(0,t.jsx)(c.A,{xs:24,sm:12,lg:6,children:(0,t.jsx)(x.A,{children:(0,t.jsx)(o.A,{title:"活跃用户",value:r.activeUsers,prefix:(0,t.jsx)(v.A,{}),suffix:"(7天)",loading:s})})}),(0,t.jsx)(c.A,{xs:24,sm:12,lg:6,children:(0,t.jsx)(x.A,{children:(0,t.jsx)(o.A,{title:"总收入",value:r.totalRevenue,precision:2,prefix:"\xa5",valueStyle:{color:"#52c41a"},loading:s})})}),(0,t.jsx)(c.A,{xs:24,sm:12,lg:6,children:(0,t.jsx)(x.A,{children:(0,t.jsx)(o.A,{title:"成功订单",value:r.successOrders,prefix:(0,t.jsx)(g.A,{}),suffix:"/ ".concat(r.totalOrders),loading:s})})})]}),(0,t.jsxs)(a.A,{gutter:[16,16],style:{marginBottom:24},children:[(0,t.jsx)(c.A,{xs:24,lg:12,children:(0,t.jsxs)(x.A,{title:"关卡创建进度",loading:s,children:[(0,t.jsx)("div",{style:{marginBottom:16},children:(0,t.jsx)(d.A,{percent:U,status:U>=90?"exception":"active",format:()=>"".concat(r.totalLevels,"/").concat(r.maxLevels)})}),(0,t.jsxs)(b,{children:["已创建 ",r.totalLevels," 个关卡，还可以创建 ",r.remainingLevels," 个关卡"]})]})}),(0,t.jsx)(c.A,{xs:24,lg:12,children:(0,t.jsxs)(x.A,{title:"游戏统计",loading:s,children:[(0,t.jsxs)(a.A,{gutter:16,children:[(0,t.jsx)(c.A,{span:12,children:(0,t.jsx)(o.A,{title:"总游戏次数",value:r.totalGames,precision:0})}),(0,t.jsx)(c.A,{span:12,children:(0,t.jsx)(o.A,{title:"总通关次数",value:r.totalCompletions,precision:0})})]}),(0,t.jsx)("div",{style:{marginTop:16},children:(0,t.jsx)(o.A,{title:"整体通关率",value:R,precision:2,suffix:"%",valueStyle:{color:R>=50?"#3f8600":"#cf1322"}})})]})})]}),(0,t.jsx)(x.A,{title:"快捷操作",children:(0,t.jsxs)(a.A,{gutter:[16,16],children:[(0,t.jsx)(c.A,{xs:24,sm:12,md:8,lg:6,children:(0,t.jsx)(j.Ay,{type:"primary",block:!0,icon:(0,t.jsx)(p.A,{}),onClick:()=>e.push("/levels/create"),disabled:0===r.remainingLevels,children:"创建关卡"})}),(0,t.jsx)(c.A,{xs:24,sm:12,md:8,lg:6,children:(0,t.jsx)(j.Ay,{block:!0,icon:(0,t.jsx)(p.A,{}),onClick:()=>e.push("/phrases"),children:"管理词组"})}),(0,t.jsx)(c.A,{xs:24,sm:12,md:8,lg:6,children:(0,t.jsx)(j.Ay,{block:!0,icon:(0,t.jsx)(f.A,{}),onClick:()=>e.push("/users"),children:"查看用户"})}),(0,t.jsx)(c.A,{xs:24,sm:12,md:8,lg:6,children:(0,t.jsx)(j.Ay,{block:!0,icon:(0,t.jsx)(u.A,{}),onClick:()=>e.push("/levels"),children:"关卡列表"})}),(0,t.jsx)(c.A,{xs:24,sm:12,md:8,lg:6,children:(0,t.jsx)(j.Ay,{block:!0,icon:(0,t.jsx)(y.A,{}),onClick:()=>e.push("/vip-packages"),children:"VIP套餐管理"})}),(0,t.jsx)(c.A,{xs:24,sm:12,md:8,lg:6,children:(0,t.jsx)(j.Ay,{block:!0,icon:(0,t.jsx)(A.A,{}),onClick:()=>e.push("/vip-users"),children:"VIP用户管理"})}),(0,t.jsx)(c.A,{xs:24,sm:12,md:8,lg:6,children:(0,t.jsx)(j.Ay,{block:!0,icon:(0,t.jsx)(g.A,{}),onClick:()=>e.push("/payment-orders"),children:"支付订单"})})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8340,547,7469,7497,7605,6441,9179,8441,1684,7358],()=>s(2735)),_N_E=e.O()}]);