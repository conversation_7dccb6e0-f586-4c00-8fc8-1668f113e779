import { ConfigService } from '@nestjs/config';
export declare class WechatPayService {
    private configService;
    private readonly logger;
    private readonly appId;
    private readonly mchId;
    private readonly apiKey;
    private readonly privateKey;
    private readonly serialNo;
    private readonly notifyUrl;
    constructor(configService: ConfigService);
    getConfigStatus(): any;
    private generateNonceStr;
    private generateTimestamp;
    private buildSignString;
    private generateSignature;
    private buildAuthorizationHeader;
    createOrder(params: {
        description: string;
        out_trade_no: string;
        total: number;
        openid: string;
        detail?: string;
        attach?: string;
        expireMinutes?: number;
    }): Promise<{
        prepay_id: string;
    }>;
    queryOrder(out_trade_no: string): Promise<any>;
    generateMiniProgramPayParams(prepay_id: string): {
        appId: string;
        timeStamp: string;
        nonceStr: string;
        package: string;
        signType: string;
        paySign: string;
    };
    verifyNotifySignature(headers: any, body: string): boolean;
    decryptNotifyData(encryptedData: {
        algorithm: string;
        ciphertext: string;
        associated_data: string;
        nonce: string;
    }): any;
}
