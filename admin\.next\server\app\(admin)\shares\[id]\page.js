(()=>{var e={};e.id=626,e.ids=[626],e.modules={1632:(e,t,n)=>{Promise.resolve().then(n.bind(n,69880))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4691:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});let o=n(7565).A},7565:(e,t,n)=>{"use strict";n.d(t,{A:()=>m});var o=n(43210),r=n(69662),i=n.n(r),l=n(71802),a=n(52604),s=n(76285),c=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};function d(e){return"number"==typeof e?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}let u=["xs","sm","md","lg","xl","xxl"],m=o.forwardRef((e,t)=>{let{getPrefixCls:n,direction:r}=o.useContext(l.QO),{gutter:m,wrap:p}=o.useContext(a.A),{prefixCls:f,span:g,order:b,offset:h,push:v,pull:y,className:x,children:A,flex:w,style:j}=e,C=c(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),O=n("col",f),[S,$,E]=(0,s.xV)(O),k={},I={};u.forEach(t=>{let n={},o=e[t];"number"==typeof o?n.span=o:"object"==typeof o&&(n=o||{}),delete C[t],I=Object.assign(Object.assign({},I),{[`${O}-${t}-${n.span}`]:void 0!==n.span,[`${O}-${t}-order-${n.order}`]:n.order||0===n.order,[`${O}-${t}-offset-${n.offset}`]:n.offset||0===n.offset,[`${O}-${t}-push-${n.push}`]:n.push||0===n.push,[`${O}-${t}-pull-${n.pull}`]:n.pull||0===n.pull,[`${O}-rtl`]:"rtl"===r}),n.flex&&(I[`${O}-${t}-flex`]=!0,k[`--${O}-${t}-flex`]=d(n.flex))});let N=i()(O,{[`${O}-${g}`]:void 0!==g,[`${O}-order-${b}`]:b,[`${O}-offset-${h}`]:h,[`${O}-push-${v}`]:v,[`${O}-pull-${y}`]:y},x,I,$,E),z={};if(m&&m[0]>0){let e=m[0]/2;z.paddingLeft=e,z.paddingRight=e}return w&&(z.flex=d(w),!1!==p||z.minWidth||(z.minWidth=0)),S(o.createElement("div",Object.assign({},C,{style:Object.assign(Object.assign(Object.assign({},z),j),k),className:N,ref:t}),A))})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20775:(e,t,n)=>{"use strict";n.d(t,{A:()=>p});var o=n(43210),r=n(69662),i=n.n(r),l=n(57266),a=n(71802),s=n(54908),c=n(52604),d=n(76285),u=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};function m(e,t){let[n,r]=o.useState("string"==typeof e?e:""),i=()=>{if("string"==typeof e&&r(e),"object"==typeof e)for(let n=0;n<l.ye.length;n++){let o=l.ye[n];if(!t||!t[o])continue;let i=e[o];if(void 0!==i)return void r(i)}};return o.useEffect(()=>{i()},[JSON.stringify(e),t]),n}let p=o.forwardRef((e,t)=>{let{prefixCls:n,justify:r,align:p,className:f,style:g,children:b,gutter:h=0,wrap:v}=e,y=u(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:x,direction:A}=o.useContext(a.QO),w=(0,s.A)(!0,null),j=m(p,w),C=m(r,w),O=x("row",n),[S,$,E]=(0,d.L3)(O),k=function(e,t){let n=[void 0,void 0],o=Array.isArray(e)?e:[e,void 0],r=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return o.forEach((e,t)=>{if("object"==typeof e&&null!==e)for(let o=0;o<l.ye.length;o++){let i=l.ye[o];if(r[i]&&void 0!==e[i]){n[t]=e[i];break}}else n[t]=e}),n}(h,w),I=i()(O,{[`${O}-no-wrap`]:!1===v,[`${O}-${C}`]:C,[`${O}-${j}`]:j,[`${O}-rtl`]:"rtl"===A},f,$,E),N={},z=null!=k[0]&&k[0]>0?-(k[0]/2):void 0;z&&(N.marginLeft=z,N.marginRight=z);let[M,P]=k;N.rowGap=P;let R=o.useMemo(()=>({gutter:[M,P],wrap:v}),[M,P,v]);return S(o.createElement(c.A.Provider,{value:R},o.createElement("div",Object.assign({},y,{className:I,style:Object.assign(Object.assign({},N),g),ref:t}),b)))})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52378:(e,t,n)=>{"use strict";n.d(t,{A:()=>I});var o=n(43210),r=n(69662),i=n.n(r),l=n(11056),a=n(41414),s=n(10313),c=n(56883),d=n(17727),u=n(71802),m=n(42411),p=n(73117),f=n(32476),g=n(60254),b=n(13581);let h=e=>{let{paddingXXS:t,lineWidth:n,tagPaddingHorizontal:o,componentCls:r,calc:i}=e,l=i(o).sub(n).equal(),a=i(t).sub(n).equal();return{[r]:Object.assign(Object.assign({},(0,f.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,m.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${r}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${r}-close-icon`]:{marginInlineStart:a,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${r}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${r}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${r}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},v=e=>{let{lineWidth:t,fontSizeIcon:n,calc:o}=e,r=e.fontSizeSM;return(0,g.oX)(e,{tagFontSize:r,tagLineHeight:(0,m.zA)(o(e.lineHeightSM).mul(r).equal()),tagIconSize:o(n).sub(o(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},y=e=>({defaultBg:new p.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),x=(0,b.OF)("Tag",e=>h(v(e)),y);var A=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let w=o.forwardRef((e,t)=>{let{prefixCls:n,style:r,className:l,checked:a,onChange:s,onClick:c}=e,d=A(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:m,tag:p}=o.useContext(u.QO),f=m("tag",n),[g,b,h]=x(f),v=i()(f,`${f}-checkable`,{[`${f}-checkable-checked`]:a},null==p?void 0:p.className,l,b,h);return g(o.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},r),null==p?void 0:p.style),className:v,onClick:e=>{null==s||s(!a),null==c||c(e)}})))});var j=n(21821);let C=e=>(0,j.A)(e,(t,{textColor:n,lightBorderColor:o,lightColor:r,darkColor:i})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:n,background:r,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:i,borderColor:i},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),O=(0,b.bf)(["Tag","preset"],e=>C(v(e)),y),S=(e,t,n)=>{let o=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(n);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${n}`],background:e[`color${o}Bg`],borderColor:e[`color${o}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},$=(0,b.bf)(["Tag","status"],e=>{let t=v(e);return[S(t,"success","Success"),S(t,"processing","Info"),S(t,"error","Error"),S(t,"warning","Warning")]},y);var E=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let k=o.forwardRef((e,t)=>{let{prefixCls:n,className:r,rootClassName:m,style:p,children:f,icon:g,color:b,onClose:h,bordered:v=!0,visible:y}=e,A=E(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:w,direction:j,tag:C}=o.useContext(u.QO),[S,k]=o.useState(!0),I=(0,l.A)(A,["closeIcon","closable"]);o.useEffect(()=>{void 0!==y&&k(y)},[y]);let N=(0,a.nP)(b),z=(0,a.ZZ)(b),M=N||z,P=Object.assign(Object.assign({backgroundColor:b&&!M?b:void 0},null==C?void 0:C.style),p),R=w("tag",n),[T,L,D]=x(R),B=i()(R,null==C?void 0:C.className,{[`${R}-${b}`]:M,[`${R}-has-color`]:b&&!M,[`${R}-hidden`]:!S,[`${R}-rtl`]:"rtl"===j,[`${R}-borderless`]:!v},r,m,L,D),Y=e=>{e.stopPropagation(),null==h||h(e),e.defaultPrevented||k(!1)},[,H]=(0,s.A)((0,s.d)(e),(0,s.d)(C),{closable:!1,closeIconRender:e=>{let t=o.createElement("span",{className:`${R}-close-icon`,onClick:Y},e);return(0,c.fx)(e,t,e=>({onClick:t=>{var n;null==(n=null==e?void 0:e.onClick)||n.call(e,t),Y(t)},className:i()(null==e?void 0:e.className,`${R}-close-icon`)}))}}),X="function"==typeof A.onClick||f&&"a"===f.type,q=g||null,G=q?o.createElement(o.Fragment,null,q,f&&o.createElement("span",null,f)):f,W=o.createElement("span",Object.assign({},I,{ref:t,className:B,style:P}),G,H,N&&o.createElement(O,{key:"preset",prefixCls:R}),z&&o.createElement($,{key:"status",prefixCls:R}));return T(X?o.createElement(d.A,{component:"Tag"},W):W)});k.CheckableTag=w;let I=k},52604:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});let o=(0,n(43210).createContext)({})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63715:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var o=n(65239),r=n(48088),i=n(88170),l=n.n(i),a=n(30893),s={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>a[e]);n.d(t,s);let c={children:["",{children:["(admin)",{children:["shares",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,69880)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\page.tsx"],u={require:n,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(admin)/shares/[id]/page",pathname:"/shares/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},65184:(e,t,n)=>{Promise.resolve().then(n.bind(n,68192))},68192:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>ti});var o=n(60687),r=n(43210),i=n.n(r),l=n(16189),a=n(99053),s=n(35899),c=n(70553),d=n(29220),u=n(21411),m=n(11585),p=n(96625),f=n(4691),g=n(48111),b=n(69662),h=n.n(b),v=n(57266),y=n(71802),x=n(40908),A=n(54908);let w={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},j=i().createContext({});var C=n(26851),O=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let S=e=>(0,C.A)(e).map(e=>Object.assign(Object.assign({},null==e?void 0:e.props),{key:e.key}));var $=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let E=(e,t)=>{let[n,o]=(0,r.useMemo)(()=>(function(e,t){let n=[],o=[],r=!1,i=0;return e.filter(e=>e).forEach(e=>{let{filled:l}=e,a=$(e,["filled"]);if(l){o.push(a),n.push(o),o=[],i=0;return}let s=t-i;(i+=e.span||1)>=t?(i>t?(r=!0,o.push(Object.assign(Object.assign({},a),{span:s}))):o.push(a),n.push(o),o=[],i=0):o.push(a)}),o.length>0&&n.push(o),[n=n.map(e=>{let n=e.reduce((e,t)=>e+(t.span||1),0);if(n<t){let o=e[e.length-1];o.span=t-(n-(o.span||1))}return e}),r]})(t,e),[t,e]);return n},k=e=>{let{itemPrefixCls:t,component:n,span:o,className:i,style:l,labelStyle:a,contentStyle:s,bordered:c,label:d,content:u,colon:m,type:p,styles:f}=e,{classNames:g}=r.useContext(j);if(c)return r.createElement(n,{className:h()({[`${t}-item-label`]:"label"===p,[`${t}-item-content`]:"content"===p,[`${null==g?void 0:g.label}`]:"label"===p,[`${null==g?void 0:g.content}`]:"content"===p},i),style:l,colSpan:o},null!=d&&r.createElement("span",{style:Object.assign(Object.assign({},a),null==f?void 0:f.label)},d),null!=u&&r.createElement("span",{style:Object.assign(Object.assign({},a),null==f?void 0:f.content)},u));return r.createElement(n,{className:h()(`${t}-item`,i),style:l,colSpan:o},r.createElement("div",{className:`${t}-item-container`},(d||0===d)&&r.createElement("span",{className:h()(`${t}-item-label`,null==g?void 0:g.label,{[`${t}-item-no-colon`]:!m}),style:Object.assign(Object.assign({},a),null==f?void 0:f.label)},d),(u||0===u)&&r.createElement("span",{className:h()(`${t}-item-content`,null==g?void 0:g.content),style:Object.assign(Object.assign({},s),null==f?void 0:f.content)},u)))};function I(e,{colon:t,prefixCls:n,bordered:o},{component:i,type:l,showLabel:a,showContent:s,labelStyle:c,contentStyle:d,styles:u}){return e.map(({label:e,children:m,prefixCls:p=n,className:f,style:g,labelStyle:b,contentStyle:h,span:v=1,key:y,styles:x},A)=>"string"==typeof i?r.createElement(k,{key:`${l}-${y||A}`,className:f,style:g,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},c),null==u?void 0:u.label),b),null==x?void 0:x.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},d),null==u?void 0:u.content),h),null==x?void 0:x.content)},span:v,colon:t,component:i,itemPrefixCls:p,bordered:o,label:a?e:null,content:s?m:null,type:l}):[r.createElement(k,{key:`label-${y||A}`,className:f,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},c),null==u?void 0:u.label),g),b),null==x?void 0:x.label),span:1,colon:t,component:i[0],itemPrefixCls:p,bordered:o,label:e,type:"label"}),r.createElement(k,{key:`content-${y||A}`,className:f,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},d),null==u?void 0:u.content),g),h),null==x?void 0:x.content),span:2*v-1,component:i[1],itemPrefixCls:p,bordered:o,content:m,type:"content"})])}let N=e=>{let t=r.useContext(j),{prefixCls:n,vertical:o,row:i,index:l,bordered:a}=e;return o?r.createElement(r.Fragment,null,r.createElement("tr",{key:`label-${l}`,className:`${n}-row`},I(i,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),r.createElement("tr",{key:`content-${l}`,className:`${n}-row`},I(i,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):r.createElement("tr",{key:l,className:`${n}-row`},I(i,e,Object.assign({component:a?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))};var z=n(42411),M=n(32476),P=n(13581),R=n(60254);let T=e=>{let{componentCls:t,labelBg:n}=e;return{[`&${t}-bordered`]:{[`> ${t}-view`]:{border:`${(0,z.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto"},[`${t}-row`]:{borderBottom:`${(0,z.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:first-child":{"> th:first-child, > td:first-child":{borderStartStartRadius:e.borderRadiusLG}},"&:last-child":{borderBottom:"none","> th:first-child, > td:first-child":{borderEndStartRadius:e.borderRadiusLG}},[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,z.zA)(e.padding)} ${(0,z.zA)(e.paddingLG)}`,borderInlineEnd:`${(0,z.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${t}-item-label`]:{color:e.colorTextSecondary,backgroundColor:n,"&::after":{display:"none"}}}},[`&${t}-middle`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,z.zA)(e.paddingSM)} ${(0,z.zA)(e.paddingLG)}`}}},[`&${t}-small`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,z.zA)(e.paddingXS)} ${(0,z.zA)(e.padding)}`}}}}}},L=e=>{let{componentCls:t,extraColor:n,itemPaddingBottom:o,itemPaddingEnd:r,colonMarginRight:i,colonMarginLeft:l,titleMarginBottom:a}=e;return{[t]:Object.assign(Object.assign(Object.assign({},(0,M.dF)(e)),T(e)),{"&-rtl":{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:a},[`${t}-title`]:Object.assign(Object.assign({},M.L9),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:n,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${t}-row`]:{"> th, > td":{paddingBottom:o,paddingInlineEnd:r},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${t}-item-label`]:{color:e.labelColor,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${(0,z.zA)(l)} ${(0,z.zA)(i)}`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},D=(0,P.OF)("Descriptions",e=>L((0,R.oX)(e,{})),e=>({labelBg:e.colorFillAlter,labelColor:e.colorTextTertiary,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText}));var B=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let Y=e=>{let{prefixCls:t,title:n,extra:o,column:i,colon:l=!0,bordered:a,layout:s,children:c,className:d,rootClassName:u,style:m,size:p,labelStyle:f,contentStyle:g,styles:b,items:C,classNames:$}=e,k=B(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:I,direction:z,className:M,style:P,classNames:R,styles:T}=(0,y.TP)("descriptions"),L=I("descriptions",t),Y=(0,A.A)(),H=r.useMemo(()=>{var e;return"number"==typeof i?i:null!=(e=(0,v.ko)(Y,Object.assign(Object.assign({},w),i)))?e:3},[Y,i]),X=function(e,t,n){let o=r.useMemo(()=>t||S(n),[t,n]);return r.useMemo(()=>o.map(t=>{var{span:n}=t,o=O(t,["span"]);return"filled"===n?Object.assign(Object.assign({},o),{filled:!0}):Object.assign(Object.assign({},o),{span:"number"==typeof n?n:(0,v.ko)(e,n)})}),[o,e])}(Y,C,c),q=(0,x.A)(p),G=E(H,X),[W,Q,F]=D(L),U=r.useMemo(()=>({labelStyle:f,contentStyle:g,styles:{content:Object.assign(Object.assign({},T.content),null==b?void 0:b.content),label:Object.assign(Object.assign({},T.label),null==b?void 0:b.label)},classNames:{label:h()(R.label,null==$?void 0:$.label),content:h()(R.content,null==$?void 0:$.content)}}),[f,g,b,$,R,T]);return W(r.createElement(j.Provider,{value:U},r.createElement("div",Object.assign({className:h()(L,M,R.root,null==$?void 0:$.root,{[`${L}-${q}`]:q&&"default"!==q,[`${L}-bordered`]:!!a,[`${L}-rtl`]:"rtl"===z},d,u,Q,F),style:Object.assign(Object.assign(Object.assign(Object.assign({},P),T.root),null==b?void 0:b.root),m)},k),(n||o)&&r.createElement("div",{className:h()(`${L}-header`,R.header,null==$?void 0:$.header),style:Object.assign(Object.assign({},T.header),null==b?void 0:b.header)},n&&r.createElement("div",{className:h()(`${L}-title`,R.title,null==$?void 0:$.title),style:Object.assign(Object.assign({},T.title),null==b?void 0:b.title)},n),o&&r.createElement("div",{className:h()(`${L}-extra`,R.extra,null==$?void 0:$.extra),style:Object.assign(Object.assign({},T.extra),null==b?void 0:b.extra)},o)),r.createElement("div",{className:`${L}-view`},r.createElement("table",null,r.createElement("tbody",null,G.map((e,t)=>r.createElement(N,{key:t,index:t,colon:l,prefixCls:L,vertical:"vertical"===s,bordered:a,row:e}))))))))};Y.Item=({children:e})=>e;var H=n(52378),X=n(5948),q=n(79505),G=n(80828),W=n(219),Q=n(95243),F=n(82853),U=n(83192),Z=n(78135);function _(){return{width:document.documentElement.clientWidth,height:window.innerHeight||document.documentElement.clientHeight}}var V=n(28344),J=n(16286),K=n(88849),ee=n(2291),et=n(37427),en=n(13934),eo=r.createContext(null);let er=function(e){var t=e.visible,n=e.maskTransitionName,o=e.getContainer,i=e.prefixCls,l=e.rootClassName,a=e.icons,s=e.countRender,c=e.showSwitch,d=e.showProgress,u=e.current,m=e.transform,p=e.count,f=e.scale,g=e.minScale,b=e.maxScale,v=e.closeIcon,y=e.onActive,x=e.onClose,A=e.onZoomIn,w=e.onZoomOut,j=e.onRotateRight,C=e.onRotateLeft,O=e.onFlipX,S=e.onFlipY,$=e.onReset,E=e.toolbarRender,k=e.zIndex,I=e.image,N=(0,r.useContext)(eo),z=a.rotateLeft,M=a.rotateRight,P=a.zoomIn,R=a.zoomOut,T=a.close,L=a.left,D=a.right,B=a.flipX,Y=a.flipY,H="".concat(i,"-operations-operation");r.useEffect(function(){var e=function(e){e.keyCode===ee.A.ESC&&x()};return t&&window.addEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}},[t]);var X=function(e,t){e.preventDefault(),e.stopPropagation(),y(t)},q=r.useCallback(function(e){var t=e.type,n=e.disabled,o=e.onClick,l=e.icon;return r.createElement("div",{key:t,className:h()(H,"".concat(i,"-operations-operation-").concat(t),(0,Q.A)({},"".concat(i,"-operations-operation-disabled"),!!n)),onClick:o},l)},[H,i]),G=c?q({icon:L,onClick:function(e){return X(e,-1)},type:"prev",disabled:0===u}):void 0,F=c?q({icon:D,onClick:function(e){return X(e,1)},type:"next",disabled:u===p-1}):void 0,U=q({icon:Y,onClick:S,type:"flipY"}),Z=q({icon:B,onClick:O,type:"flipX"}),_=q({icon:z,onClick:C,type:"rotateLeft"}),V=q({icon:M,onClick:j,type:"rotateRight"}),J=q({icon:R,onClick:w,type:"zoomOut",disabled:f<=g}),K=q({icon:P,onClick:A,type:"zoomIn",disabled:f===b}),er=r.createElement("div",{className:"".concat(i,"-operations")},U,Z,_,V,J,K);return r.createElement(en.Ay,{visible:t,motionName:n},function(e){var t=e.className,n=e.style;return r.createElement(et.A,{open:!0,getContainer:null!=o?o:document.body},r.createElement("div",{className:h()("".concat(i,"-operations-wrapper"),t,l),style:(0,W.A)((0,W.A)({},n),{},{zIndex:k})},null===v?null:r.createElement("button",{className:"".concat(i,"-close"),onClick:x},v||T),c&&r.createElement(r.Fragment,null,r.createElement("div",{className:h()("".concat(i,"-switch-left"),(0,Q.A)({},"".concat(i,"-switch-left-disabled"),0===u)),onClick:function(e){return X(e,-1)}},L),r.createElement("div",{className:h()("".concat(i,"-switch-right"),(0,Q.A)({},"".concat(i,"-switch-right-disabled"),u===p-1)),onClick:function(e){return X(e,1)}},D)),r.createElement("div",{className:"".concat(i,"-footer")},d&&r.createElement("div",{className:"".concat(i,"-progress")},s?s(u+1,p):r.createElement("bdi",null,"".concat(u+1," / ").concat(p))),E?E(er,(0,W.A)((0,W.A)({icons:{prevIcon:G,nextIcon:F,flipYIcon:U,flipXIcon:Z,rotateLeftIcon:_,rotateRightIcon:V,zoomOutIcon:J,zoomInIcon:K},actions:{onActive:y,onFlipY:S,onFlipX:O,onRotateLeft:C,onRotateRight:j,onZoomOut:w,onZoomIn:A,onReset:$,onClose:x},transform:m},N?{current:u,total:p}:{}),{},{image:I})):er)))})};var ei=n(25725),el=n(53428),ea={x:0,y:0,rotate:0,scale:1,flipX:!1,flipY:!1},es=n(70393);function ec(e,t,n,o){var r=t+n,i=(n-o)/2;if(n>o){if(t>0)return(0,Q.A)({},e,i);if(t<0&&r<o)return(0,Q.A)({},e,-i)}else if(t<0||r>o)return(0,Q.A)({},e,t<0?i:-i);return{}}function ed(e,t,n,o){var r=_(),i=r.width,l=r.height,a=null;return e<=i&&t<=l?a={x:0,y:0}:(e>i||t>l)&&(a=(0,W.A)((0,W.A)({},ec("x",n,e,i)),ec("y",o,t,l))),a}function eu(e){var t=e.src,n=e.isCustomPlaceholder,o=e.fallback,i=(0,r.useState)(n?"loading":"normal"),l=(0,F.A)(i,2),a=l[0],s=l[1],c=(0,r.useRef)(!1),d="error"===a;(0,r.useEffect)(function(){var e=!0;return new Promise(function(e){if(!t)return void e(!1);var n=document.createElement("img");n.onerror=function(){return e(!1)},n.onload=function(){return e(!0)},n.src=t}).then(function(t){!t&&e&&s("error")}),function(){e=!1}},[t]),(0,r.useEffect)(function(){n&&!c.current?s("loading"):d&&s("normal")},[t]);var u=function(){s("normal")};return[function(e){c.current=!1,"loading"===a&&null!=e&&e.complete&&(e.naturalWidth||e.naturalHeight)&&(c.current=!0,u())},d&&o?{src:o}:{onLoad:u,src:t},a]}function em(e,t){return Math.hypot(e.x-t.x,e.y-t.y)}var ep=["fallback","src","imgRef"],ef=["prefixCls","src","alt","imageInfo","fallback","movable","onClose","visible","icons","rootClassName","closeIcon","getContainer","current","count","countRender","scaleStep","minScale","maxScale","transitionName","maskTransitionName","imageRender","imgCommonProps","toolbarRender","onTransform","onChange"],eg=function(e){var t=e.fallback,n=e.src,o=e.imgRef,r=(0,Z.A)(e,ep),l=eu({src:n,fallback:t}),a=(0,F.A)(l,2),s=a[0],c=a[1];return i().createElement("img",(0,G.A)({ref:function(e){o.current=e,s(e)}},r,c))};let eb=function(e){var t,n,o,l,a,s,c,d,u,m,p,f,g,b,v,y,x,A,w,j,C,O,S,$,E,k,I,N,z=e.prefixCls,M=e.src,P=e.alt,R=e.imageInfo,T=e.fallback,L=e.movable,D=void 0===L||L,B=e.onClose,Y=e.visible,H=e.icons,X=e.rootClassName,q=e.closeIcon,U=e.getContainer,V=e.current,et=void 0===V?0:V,en=e.count,ec=void 0===en?1:en,eu=e.countRender,ep=e.scaleStep,eb=void 0===ep?.5:ep,eh=e.minScale,ev=void 0===eh?1:eh,ey=e.maxScale,ex=void 0===ey?50:ey,eA=e.transitionName,ew=e.maskTransitionName,ej=void 0===ew?"fade":ew,eC=e.imageRender,eO=e.imgCommonProps,eS=e.toolbarRender,e$=e.onTransform,eE=e.onChange,ek=(0,Z.A)(e,ef),eI=(0,r.useRef)(),eN=(0,r.useContext)(eo),ez=eN&&ec>1,eM=eN&&ec>=1,eP=(0,r.useState)(!0),eR=(0,F.A)(eP,2),eT=eR[0],eL=eR[1],eD=(t=(0,r.useRef)(null),n=(0,r.useRef)([]),o=(0,r.useState)(ea),a=(l=(0,F.A)(o,2))[0],s=l[1],c=function(e,o){null===t.current&&(n.current=[],t.current=(0,el.A)(function(){s(function(e){var r=e;return n.current.forEach(function(e){r=(0,W.A)((0,W.A)({},r),e)}),t.current=null,null==e$||e$({transform:r,action:o}),r})})),n.current.push((0,W.A)((0,W.A)({},a),e))},{transform:a,resetTransform:function(e){s(ea),(0,ei.A)(ea,a)||null==e$||e$({transform:ea,action:e})},updateTransform:c,dispatchZoomChange:function(e,t,n,o,r){var i=eI.current,l=i.width,s=i.height,d=i.offsetWidth,u=i.offsetHeight,m=i.offsetLeft,p=i.offsetTop,f=e,g=a.scale*e;g>ex?(g=ex,f=ex/a.scale):g<ev&&(f=(g=r?g:ev)/a.scale);var b=null!=o?o:innerHeight/2,h=f-1,v=h*((null!=n?n:innerWidth/2)-a.x-m),y=h*(b-a.y-p),x=a.x-(v-h*l*.5),A=a.y-(y-h*s*.5);if(e<1&&1===g){var w=d*g,j=u*g,C=_(),O=C.width,S=C.height;w<=O&&j<=S&&(x=0,A=0)}c({x:x,y:A,scale:g},t)}}),eB=eD.transform,eY=eD.resetTransform,eH=eD.updateTransform,eX=eD.dispatchZoomChange,eq=(d=eB.rotate,u=eB.scale,m=eB.x,p=eB.y,f=(0,r.useState)(!1),b=(g=(0,F.A)(f,2))[0],v=g[1],y=(0,r.useRef)({diffX:0,diffY:0,transformX:0,transformY:0}),x=function(e){Y&&b&&eH({x:e.pageX-y.current.diffX,y:e.pageY-y.current.diffY},"move")},A=function(){if(Y&&b){v(!1);var e=y.current,t=e.transformX,n=e.transformY;if(m!==t&&p!==n){var o=eI.current.offsetWidth*u,r=eI.current.offsetHeight*u,i=eI.current.getBoundingClientRect(),l=i.left,a=i.top,s=d%180!=0,c=ed(s?r:o,s?o:r,l,a);c&&eH((0,W.A)({},c),"dragRebound")}}},(0,r.useEffect)(function(){var e,t,n,o;if(D){n=(0,K.A)(window,"mouseup",A,!1),o=(0,K.A)(window,"mousemove",x,!1);try{window.top!==window.self&&(e=(0,K.A)(window.top,"mouseup",A,!1),t=(0,K.A)(window.top,"mousemove",x,!1))}catch(e){(0,es.$e)(!1,"[rc-image] ".concat(e))}}return function(){var r,i,l,a;null==(r=n)||r.remove(),null==(i=o)||i.remove(),null==(l=e)||l.remove(),null==(a=t)||a.remove()}},[Y,b,m,p,d,D]),{isMoving:b,onMouseDown:function(e){D&&0===e.button&&(e.preventDefault(),e.stopPropagation(),y.current={diffX:e.pageX-m,diffY:e.pageY-p,transformX:m,transformY:p},v(!0))},onMouseMove:x,onMouseUp:A,onWheel:function(e){if(Y&&0!=e.deltaY){var t=1+Math.min(Math.abs(e.deltaY/100),1)*eb;e.deltaY>0&&(t=1/t),eX(t,"wheel",e.clientX,e.clientY)}}}),eG=eq.isMoving,eW=eq.onMouseDown,eQ=eq.onWheel,eF=(w=eB.rotate,j=eB.scale,C=eB.x,O=eB.y,S=(0,r.useState)(!1),E=($=(0,F.A)(S,2))[0],k=$[1],I=(0,r.useRef)({point1:{x:0,y:0},point2:{x:0,y:0},eventType:"none"}),N=function(e){I.current=(0,W.A)((0,W.A)({},I.current),e)},(0,r.useEffect)(function(){var e;return Y&&D&&(e=(0,K.A)(window,"touchmove",function(e){return e.preventDefault()},{passive:!1})),function(){var t;null==(t=e)||t.remove()}},[Y,D]),{isTouching:E,onTouchStart:function(e){if(D){e.stopPropagation(),k(!0);var t=e.touches,n=void 0===t?[]:t;n.length>1?N({point1:{x:n[0].clientX,y:n[0].clientY},point2:{x:n[1].clientX,y:n[1].clientY},eventType:"touchZoom"}):N({point1:{x:n[0].clientX-C,y:n[0].clientY-O},eventType:"move"})}},onTouchMove:function(e){var t=e.touches,n=void 0===t?[]:t,o=I.current,r=o.point1,i=o.point2,l=o.eventType;if(n.length>1&&"touchZoom"===l){var a={x:n[0].clientX,y:n[0].clientY},s={x:n[1].clientX,y:n[1].clientY},c=function(e,t,n,o){var r=em(e,n),i=em(t,o);if(0===r&&0===i)return[e.x,e.y];var l=r/(r+i);return[e.x+l*(t.x-e.x),e.y+l*(t.y-e.y)]}(r,i,a,s),d=(0,F.A)(c,2),u=d[0],m=d[1];eX(em(a,s)/em(r,i),"touchZoom",u,m,!0),N({point1:a,point2:s,eventType:"touchZoom"})}else"move"===l&&(eH({x:n[0].clientX-r.x,y:n[0].clientY-r.y},"move"),N({eventType:"move"}))},onTouchEnd:function(){if(Y){if(E&&k(!1),N({eventType:"none"}),ev>j)return eH({x:0,y:0,scale:ev},"touchZoom");var e=eI.current.offsetWidth*j,t=eI.current.offsetHeight*j,n=eI.current.getBoundingClientRect(),o=n.left,r=n.top,i=w%180!=0,l=ed(i?t:e,i?e:t,o,r);l&&eH((0,W.A)({},l),"dragRebound")}}}),eU=eF.isTouching,eZ=eF.onTouchStart,e_=eF.onTouchMove,eV=eF.onTouchEnd,eJ=eB.rotate,eK=eB.scale,e0=h()((0,Q.A)({},"".concat(z,"-moving"),eG));(0,r.useEffect)(function(){eT||eL(!0)},[eT]);var e1=function(e){var t=et+e;!Number.isInteger(t)||t<0||t>ec-1||(eL(!1),eY(e<0?"prev":"next"),null==eE||eE(t,et))},e2=function(e){Y&&ez&&(e.keyCode===ee.A.LEFT?e1(-1):e.keyCode===ee.A.RIGHT&&e1(1))};(0,r.useEffect)(function(){var e=(0,K.A)(window,"keydown",e2,!1);return function(){e.remove()}},[Y,ez,et]);var e8=i().createElement(eg,(0,G.A)({},eO,{width:e.width,height:e.height,imgRef:eI,className:"".concat(z,"-img"),alt:P,style:{transform:"translate3d(".concat(eB.x,"px, ").concat(eB.y,"px, 0) scale3d(").concat(eB.flipX?"-":"").concat(eK,", ").concat(eB.flipY?"-":"").concat(eK,", 1) rotate(").concat(eJ,"deg)"),transitionDuration:(!eT||eU)&&"0s"},fallback:T,src:M,onWheel:eQ,onMouseDown:eW,onDoubleClick:function(e){Y&&(1!==eK?eH({x:0,y:0,scale:1},"doubleClick"):eX(1+eb,"doubleClick",e.clientX,e.clientY))},onTouchStart:eZ,onTouchMove:e_,onTouchEnd:eV,onTouchCancel:eV})),e4=(0,W.A)({url:M,alt:P},R);return i().createElement(i().Fragment,null,i().createElement(J.A,(0,G.A)({transitionName:void 0===eA?"zoom":eA,maskTransitionName:ej,closable:!1,keyboard:!0,prefixCls:z,onClose:B,visible:Y,classNames:{wrapper:e0},rootClassName:X,getContainer:U},ek,{afterClose:function(){eY("close")}}),i().createElement("div",{className:"".concat(z,"-img-wrapper")},eC?eC(e8,(0,W.A)({transform:eB,image:e4},eN?{current:et}:{})):e8)),i().createElement(er,{visible:Y,transform:eB,maskTransitionName:ej,closeIcon:q,getContainer:U,prefixCls:z,rootClassName:X,icons:void 0===H?{}:H,countRender:eu,showSwitch:ez,showProgress:eM,current:et,count:ec,scale:eK,minScale:ev,maxScale:ex,toolbarRender:eS,onActive:e1,onZoomIn:function(){eX(1+eb,"zoomIn")},onZoomOut:function(){eX(1/(1+eb),"zoomOut")},onRotateRight:function(){eH({rotate:eJ+90},"rotateRight")},onRotateLeft:function(){eH({rotate:eJ-90},"rotateLeft")},onFlipX:function(){eH({flipX:!eB.flipX},"flipX")},onFlipY:function(){eH({flipY:!eB.flipY},"flipY")},onClose:B,onReset:function(){eY("reset")},zIndex:void 0!==ek.zIndex?ek.zIndex+1:void 0,image:e4}))};var eh=n(78651),ev=["crossOrigin","decoding","draggable","loading","referrerPolicy","sizes","srcSet","useMap","alt"],ey=["visible","onVisibleChange","getContainer","current","movable","minScale","maxScale","countRender","closeIcon","onChange","onTransform","toolbarRender","imageRender"],ex=["src"],eA=0,ew=["src","alt","onPreviewClose","prefixCls","previewPrefixCls","placeholder","fallback","width","height","style","preview","className","onClick","onError","wrapperClassName","wrapperStyle","rootClassName"],ej=["src","visible","onVisibleChange","getContainer","mask","maskClassName","movable","icons","scaleStep","minScale","maxScale","imageRender","toolbarRender"],eC=function(e){var t,n,o,i,l=e.src,a=e.alt,s=e.onPreviewClose,c=e.prefixCls,d=void 0===c?"rc-image":c,u=e.previewPrefixCls,m=void 0===u?"".concat(d,"-preview"):u,p=e.placeholder,f=e.fallback,g=e.width,b=e.height,v=e.style,y=e.preview,x=void 0===y||y,A=e.className,w=e.onClick,j=e.onError,C=e.wrapperClassName,O=e.wrapperStyle,S=e.rootClassName,$=(0,Z.A)(e,ew),E="object"===(0,U.A)(x)?x:{},k=E.src,I=E.visible,N=void 0===I?void 0:I,z=E.onVisibleChange,M=E.getContainer,P=E.mask,R=E.maskClassName,T=E.movable,L=E.icons,D=E.scaleStep,B=E.minScale,Y=E.maxScale,H=E.imageRender,X=E.toolbarRender,q=(0,Z.A)(E,ej),_=null!=k?k:l,J=(0,V.A)(!!N,{value:N,onChange:void 0===z?s:z}),K=(0,F.A)(J,2),ee=K[0],et=K[1],en=eu({src:l,isCustomPlaceholder:p&&!0!==p,fallback:f}),er=(0,F.A)(en,3),ei=er[0],el=er[1],ea=er[2],es=(0,r.useState)(null),ec=(0,F.A)(es,2),ed=ec[0],em=ec[1],ep=(0,r.useContext)(eo),ef=!!x,eg=h()(d,C,S,(0,Q.A)({},"".concat(d,"-error"),"error"===ea)),eh=(0,r.useMemo)(function(){var t={};return ev.forEach(function(n){void 0!==e[n]&&(t[n]=e[n])}),t},ev.map(function(t){return e[t]})),ey=(0,r.useMemo)(function(){return(0,W.A)((0,W.A)({},eh),{},{src:_})},[_,eh]),ex=(t=r.useState(function(){return String(eA+=1)}),n=(0,F.A)(t,1)[0],o=r.useContext(eo),i={data:ey,canPreview:ef},r.useEffect(function(){if(o)return o.register(n,i)},[]),r.useEffect(function(){o&&o.register(n,i)},[ef,ey]),n);return r.createElement(r.Fragment,null,r.createElement("div",(0,G.A)({},$,{className:eg,onClick:ef?function(e){var t,n,o=(t=e.target.getBoundingClientRect(),n=document.documentElement,{left:t.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:t.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}),r=o.left,i=o.top;ep?ep.onPreview(ex,_,r,i):(em({x:r,y:i}),et(!0)),null==w||w(e)}:w,style:(0,W.A)({width:g,height:b},O)}),r.createElement("img",(0,G.A)({},eh,{className:h()("".concat(d,"-img"),(0,Q.A)({},"".concat(d,"-img-placeholder"),!0===p),A),style:(0,W.A)({height:b},v),ref:ei},el,{width:g,height:b,onError:j})),"loading"===ea&&r.createElement("div",{"aria-hidden":"true",className:"".concat(d,"-placeholder")},p),P&&ef&&r.createElement("div",{className:h()("".concat(d,"-mask"),R),style:{display:(null==v?void 0:v.display)==="none"?"none":void 0}},P)),!ep&&ef&&r.createElement(eb,(0,G.A)({"aria-hidden":!ee,visible:ee,prefixCls:m,onClose:function(){et(!1),em(null)},mousePosition:ed,src:_,alt:a,imageInfo:{width:g,height:b},fallback:f,getContainer:void 0===M?void 0:M,icons:L,movable:T,scaleStep:D,minScale:B,maxScale:Y,rootClassName:S,imageRender:H,imgCommonProps:eh,toolbarRender:X},q)))};eC.PreviewGroup=function(e){var t,n,o,i,l,a,s=e.previewPrefixCls,c=e.children,d=e.icons,u=e.items,m=e.preview,p=e.fallback,f="object"===(0,U.A)(m)?m:{},g=f.visible,b=f.onVisibleChange,h=f.getContainer,v=f.current,y=f.movable,x=f.minScale,A=f.maxScale,w=f.countRender,j=f.closeIcon,C=f.onChange,O=f.onTransform,S=f.toolbarRender,$=f.imageRender,E=(0,Z.A)(f,ey),k=(t=r.useState({}),o=(n=(0,F.A)(t,2))[0],i=n[1],l=r.useCallback(function(e,t){return i(function(n){return(0,W.A)((0,W.A)({},n),{},(0,Q.A)({},e,t))}),function(){i(function(t){var n=(0,W.A)({},t);return delete n[e],n})}},[]),[r.useMemo(function(){return u?u.map(function(e){if("string"==typeof e)return{data:{src:e}};var t={};return Object.keys(e).forEach(function(n){["src"].concat((0,eh.A)(ev)).includes(n)&&(t[n]=e[n])}),{data:t}}):Object.keys(o).reduce(function(e,t){var n=o[t],r=n.canPreview,i=n.data;return r&&e.push({data:i,id:t}),e},[])},[u,o]),l,!!u]),I=(0,F.A)(k,3),N=I[0],z=I[1],M=I[2],P=(0,V.A)(0,{value:v}),R=(0,F.A)(P,2),T=R[0],L=R[1],D=(0,r.useState)(!1),B=(0,F.A)(D,2),Y=B[0],H=B[1],X=(null==(a=N[T])?void 0:a.data)||{},q=X.src,_=(0,Z.A)(X,ex),J=(0,V.A)(!!g,{value:g,onChange:function(e,t){null==b||b(e,t,T)}}),K=(0,F.A)(J,2),ee=K[0],et=K[1],en=(0,r.useState)(null),er=(0,F.A)(en,2),ei=er[0],el=er[1],ea=r.useCallback(function(e,t,n,o){var r=M?N.findIndex(function(e){return e.data.src===t}):N.findIndex(function(t){return t.id===e});L(r<0?0:r),et(!0),el({x:n,y:o}),H(!0)},[N,M]);r.useEffect(function(){ee?Y||L(0):H(!1)},[ee]);var es=r.useMemo(function(){return{register:z,onPreview:ea}},[z,ea]);return r.createElement(eo.Provider,{value:es},c,r.createElement(eb,(0,G.A)({"aria-hidden":!ee,movable:y,visible:ee,prefixCls:void 0===s?"rc-image-preview":s,closeIcon:j,onClose:function(){et(!1),el(null)},mousePosition:ei,imgCommonProps:_,src:q,fallback:p,icons:void 0===d?{}:d,minScale:x,maxScale:A,getContainer:h,current:T,count:N.length,countRender:w,onTransform:O,toolbarRender:S,imageRender:$,onChange:function(e,t){L(e),null==C||C(e,t)}},E)))};var eO=n(18130),eS=n(50604),e$=n(59897),eE=n(48232),ek=n(15693),eI=n(92799),eN=n(57314),ez={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z"}},{tag:"path",attrs:{d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z"}}]},name:"rotate-left",theme:"outlined"},eM=n(21898),eP=r.forwardRef(function(e,t){return r.createElement(eM.A,(0,G.A)({},e,{ref:t,icon:ez}))}),eR={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"}},{tag:"path",attrs:{d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z"}}]},name:"rotate-right",theme:"outlined"},eT=r.forwardRef(function(e,t){return r.createElement(eM.A,(0,G.A)({},e,{ref:t,icon:eR}))});let eL={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};var eD=r.forwardRef(function(e,t){return r.createElement(eM.A,(0,G.A)({},e,{ref:t,icon:eL}))});let eB={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-in",theme:"outlined"};var eY=r.forwardRef(function(e,t){return r.createElement(eM.A,(0,G.A)({},e,{ref:t,icon:eB}))});let eH={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-out",theme:"outlined"};var eX=r.forwardRef(function(e,t){return r.createElement(eM.A,(0,G.A)({},e,{ref:t,icon:eH}))}),eq=n(73117),eG=n(55354),eW=n(11908),eQ=n(31549);let eF=e=>({position:e||"absolute",inset:0}),eU=e=>{let{iconCls:t,motionDurationSlow:n,paddingXXS:o,marginXXS:r,prefixCls:i,colorTextLightSolid:l}=e;return{position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:l,background:new eq.Y("#000").setA(.5).toRgbString(),cursor:"pointer",opacity:0,transition:`opacity ${n}`,[`.${i}-mask-info`]:Object.assign(Object.assign({},M.L9),{padding:`0 ${(0,z.zA)(o)}`,[t]:{marginInlineEnd:r,svg:{verticalAlign:"baseline"}}})}},eZ=e=>{let{previewCls:t,modalMaskBg:n,paddingSM:o,marginXL:r,margin:i,paddingLG:l,previewOperationColorDisabled:a,previewOperationHoverColor:s,motionDurationSlow:c,iconCls:d,colorTextLightSolid:u}=e,m=new eq.Y(n).setA(.1),p=m.clone().setA(.2);return{[`${t}-footer`]:{position:"fixed",bottom:r,left:{_skip_check_:!0,value:"50%"},display:"flex",flexDirection:"column",alignItems:"center",color:e.previewOperationColor,transform:"translateX(-50%)"},[`${t}-progress`]:{marginBottom:i},[`${t}-close`]:{position:"fixed",top:r,right:{_skip_check_:!0,value:r},display:"flex",color:u,backgroundColor:m.toRgbString(),borderRadius:"50%",padding:o,outline:0,border:0,cursor:"pointer",transition:`all ${c}`,"&:hover":{backgroundColor:p.toRgbString()},[`& > ${d}`]:{fontSize:e.previewOperationSize}},[`${t}-operations`]:{display:"flex",alignItems:"center",padding:`0 ${(0,z.zA)(l)}`,backgroundColor:m.toRgbString(),borderRadius:100,"&-operation":{marginInlineStart:o,padding:o,cursor:"pointer",transition:`all ${c}`,userSelect:"none",[`&:not(${t}-operations-operation-disabled):hover > ${d}`]:{color:s},"&-disabled":{color:a,cursor:"not-allowed"},"&:first-of-type":{marginInlineStart:0},[`& > ${d}`]:{fontSize:e.previewOperationSize}}}}},e_=e=>{let{modalMaskBg:t,iconCls:n,previewOperationColorDisabled:o,previewCls:r,zIndexPopup:i,motionDurationSlow:l}=e,a=new eq.Y(t).setA(.1),s=a.clone().setA(.2);return{[`${r}-switch-left, ${r}-switch-right`]:{position:"fixed",insetBlockStart:"50%",zIndex:e.calc(i).add(1).equal(),display:"flex",alignItems:"center",justifyContent:"center",width:e.imagePreviewSwitchSize,height:e.imagePreviewSwitchSize,marginTop:e.calc(e.imagePreviewSwitchSize).mul(-1).div(2).equal(),color:e.previewOperationColor,background:a.toRgbString(),borderRadius:"50%",transform:"translateY(-50%)",cursor:"pointer",transition:`all ${l}`,userSelect:"none","&:hover":{background:s.toRgbString()},"&-disabled":{"&, &:hover":{color:o,background:"transparent",cursor:"not-allowed",[`> ${n}`]:{cursor:"not-allowed"}}},[`> ${n}`]:{fontSize:e.previewOperationSize}},[`${r}-switch-left`]:{insetInlineStart:e.marginSM},[`${r}-switch-right`]:{insetInlineEnd:e.marginSM}}},eV=e=>{let{motionEaseOut:t,previewCls:n,motionDurationSlow:o,componentCls:r}=e;return[{[`${r}-preview-root`]:{[n]:{height:"100%",textAlign:"center",pointerEvents:"none"},[`${n}-body`]:Object.assign(Object.assign({},eF()),{overflow:"hidden"}),[`${n}-img`]:{maxWidth:"100%",maxHeight:"70%",verticalAlign:"middle",transform:"scale3d(1, 1, 1)",cursor:"grab",transition:`transform ${o} ${t} 0s`,userSelect:"none","&-wrapper":Object.assign(Object.assign({},eF()),{transition:`transform ${o} ${t} 0s`,display:"flex",justifyContent:"center",alignItems:"center","& > *":{pointerEvents:"auto"},"&::before":{display:"inline-block",width:1,height:"50%",marginInlineEnd:-1,content:'""'}})},[`${n}-moving`]:{[`${n}-preview-img`]:{cursor:"grabbing","&-wrapper":{transitionDuration:"0s"}}}}},{[`${r}-preview-root`]:{[`${n}-wrap`]:{zIndex:e.zIndexPopup}}},{[`${r}-preview-operations-wrapper`]:{position:"fixed",zIndex:e.calc(e.zIndexPopup).add(1).equal()},"&":[eZ(e),e_(e)]}]},eJ=e=>{let{componentCls:t}=e;return{[t]:{position:"relative",display:"inline-block",[`${t}-img`]:{width:"100%",height:"auto",verticalAlign:"middle"},[`${t}-img-placeholder`]:{backgroundColor:e.colorBgContainerDisabled,backgroundImage:"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"30%"},[`${t}-mask`]:Object.assign({},eU(e)),[`${t}-mask:hover`]:{opacity:1},[`${t}-placeholder`]:Object.assign({},eF())}}},eK=e=>{let{previewCls:t}=e;return{[`${t}-root`]:(0,eW.aB)(e,"zoom"),"&":(0,eQ.p9)(e,!0)}},e0=(0,P.OF)("Image",e=>{let t=`${e.componentCls}-preview`,n=(0,R.oX)(e,{previewCls:t,modalMaskBg:new eq.Y("#000").setA(.45).toRgbString(),imagePreviewSwitchSize:e.controlHeightLG});return[eJ(n),eV(n),(0,eG.Dk)((0,R.oX)(n,{componentCls:t})),eK(n)]},e=>({zIndexPopup:e.zIndexPopupBase+80,previewOperationColor:new eq.Y(e.colorTextLightSolid).setA(.65).toRgbString(),previewOperationHoverColor:new eq.Y(e.colorTextLightSolid).setA(.85).toRgbString(),previewOperationColorDisabled:new eq.Y(e.colorTextLightSolid).setA(.25).toRgbString(),previewOperationSize:1.5*e.fontSizeIcon}));var e1=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let e2={rotateLeft:r.createElement(eP,null),rotateRight:r.createElement(eT,null),zoomIn:r.createElement(eY,null),zoomOut:r.createElement(eX,null),close:r.createElement(ek.A,null),left:r.createElement(eI.A,null),right:r.createElement(eN.A,null),flipX:r.createElement(eD,null),flipY:r.createElement(eD,{rotate:90})};var e8=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)0>t.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let e4=e=>{let{prefixCls:t,preview:n,className:o,rootClassName:i,style:l}=e,a=e8(e,["prefixCls","preview","className","rootClassName","style"]),{getPrefixCls:s,getPopupContainer:c,className:d,style:u,preview:m}=(0,y.TP)("image"),[p]=(0,eE.A)("Image"),f=s("image",t),g=s(),b=(0,e$.A)(f),[v,x,A]=e0(f,b),w=h()(i,x,A,b),j=h()(o,x,d),[C]=(0,eO.YK)("ImagePreview","object"==typeof n?n.zIndex:void 0),O=r.useMemo(()=>{if(!1===n)return n;let e="object"==typeof n?n:{},{getContainer:t,closeIcon:o,rootClassName:i,destroyOnClose:l,destroyOnHidden:a}=e,s=e8(e,["getContainer","closeIcon","rootClassName","destroyOnClose","destroyOnHidden"]);return Object.assign(Object.assign({mask:r.createElement("div",{className:`${f}-mask-info`},r.createElement(q.A,null),null==p?void 0:p.preview),icons:e2},s),{destroyOnClose:null!=a?a:l,rootClassName:h()(w,i),getContainer:null!=t?t:c,transitionName:(0,eS.b)(g,"zoom",e.transitionName),maskTransitionName:(0,eS.b)(g,"fade",e.maskTransitionName),zIndex:C,closeIcon:null!=o?o:null==m?void 0:m.closeIcon})},[n,p,null==m?void 0:m.closeIcon]),S=Object.assign(Object.assign({},u),l);return v(r.createElement(eC,Object.assign({prefixCls:f,preview:O,rootClassName:w,className:j,style:S},a)))};e4.PreviewGroup=e=>{var{previewPrefixCls:t,preview:n}=e,o=e1(e,["previewPrefixCls","preview"]);let{getPrefixCls:i,direction:l}=r.useContext(y.QO),a=i("image",t),s=`${a}-preview`,c=i(),d=(0,e$.A)(a),[u,m,p]=e0(a,d),[f]=(0,eO.YK)("ImagePreview","object"==typeof n?n.zIndex:void 0),g=r.useMemo(()=>Object.assign(Object.assign({},e2),{left:"rtl"===l?r.createElement(eN.A,null):r.createElement(eI.A,null),right:"rtl"===l?r.createElement(eI.A,null):r.createElement(eN.A,null)}),[l]),b=r.useMemo(()=>{var e;if(!1===n)return n;let t="object"==typeof n?n:{},o=h()(m,p,d,null!=(e=t.rootClassName)?e:"");return Object.assign(Object.assign({},t),{transitionName:(0,eS.b)(c,"zoom",t.transitionName),maskTransitionName:(0,eS.b)(c,"fade",t.maskTransitionName),rootClassName:o,zIndex:f})},[n]);return u(r.createElement(eC.PreviewGroup,Object.assign({preview:b,previewPrefixCls:s,icons:g},o)))};var e3=n(85975),e6=n(3788),e5=n(26323),e7=n(56306),e9=n(34308),te=n(80282),tt=n(43910);let{Title:tn,Text:to,Paragraph:tr}=a.A;function ti(){let e=(0,l.useParams)(),t=(0,l.useRouter)(),[n,i]=(0,r.useState)(null),[a,b]=(0,r.useState)(!0),h=e.id,v=async()=>{if(h){b(!0);try{let e=await tt.Dw.getShareConfigById(h);i(e)}catch(e){s.Ay.error("获取分享配置详情失败"),console.error("获取分享配置详情失败:",e)}finally{b(!1)}}},y=()=>{t.push("/shares")},x=async()=>{if(n)try{await tt.Dw.toggleShareConfig(n.id),s.Ay.success(`${n.isActive?"禁用":"启用"}成功`),v()}catch(t){let e=t&&"object"==typeof t&&"message"in t?t.message:"操作失败";s.Ay.error(e)}},A=e=>{navigator.clipboard.writeText(e).then(()=>{s.Ay.success("已复制到剪贴板")}).catch(()=>{s.Ay.error("复制失败")})};return a?(0,o.jsx)("div",{style:{padding:"24px",textAlign:"center"},children:(0,o.jsx)(c.A,{size:"large"})}):n?(0,o.jsx)("div",{style:{padding:"24px"},children:(0,o.jsxs)(m.A,{children:[(0,o.jsx)("div",{style:{marginBottom:"24px"},children:(0,o.jsxs)(p.A,{justify:"space-between",align:"middle",children:[(0,o.jsx)(f.A,{children:(0,o.jsxs)(g.A,{children:[(0,o.jsx)(u.Ay,{icon:(0,o.jsx)(e3.A,{}),onClick:y,children:"返回"}),(0,o.jsxs)(tn,{level:3,style:{margin:0},children:[(0,o.jsx)(e6.A,{style:{marginRight:"8px"}}),"分享配置详情"]})]})}),(0,o.jsx)(f.A,{children:(0,o.jsxs)(g.A,{children:[(0,o.jsx)(u.Ay,{type:n.isActive?"default":"primary",icon:n.isActive?(0,o.jsx)(e5.A,{}):(0,o.jsx)(e7.A,{}),onClick:x,children:n.isActive?"禁用":"启用"}),(0,o.jsx)(u.Ay,{type:"primary",icon:(0,o.jsx)(e9.A,{}),onClick:()=>{t.push(`/shares/${h}/edit`)},children:"编辑"})]})})]})}),(0,o.jsxs)(Y,{title:"基本信息",bordered:!0,column:2,size:"middle",children:[(0,o.jsx)(Y.Item,{label:"配置ID",span:2,children:(0,o.jsxs)(g.A,{children:[(0,o.jsx)(to,{code:!0,children:n.id}),(0,o.jsx)(u.Ay,{type:"text",size:"small",icon:(0,o.jsx)(te.A,{}),onClick:()=>A(n.id)})]})}),(0,o.jsx)(Y.Item,{label:"配置名称",children:n.name}),(0,o.jsx)(Y.Item,{label:"分享类型",children:(0,o.jsx)(H.A,{color:{default:"blue",result:"green",level:"orange",achievement:"purple",custom:"gray"}[n.type]||"gray",children:(e=>{let t=tt.WS.find(t=>t.value===e);return t?.label||e})(n.type)})}),(0,o.jsx)(Y.Item,{label:"启用状态",children:(0,o.jsx)(H.A,{color:n.isActive?"success":"default",icon:n.isActive?(0,o.jsx)(e7.A,{}):(0,o.jsx)(e5.A,{}),children:n.isActive?"启用":"禁用"})}),(0,o.jsx)(Y.Item,{label:"排序权重",children:n.sortOrder}),(0,o.jsx)(Y.Item,{label:"创建时间",children:new Date(n.createdAt).toLocaleString()}),(0,o.jsx)(Y.Item,{label:"更新时间",children:new Date(n.updatedAt).toLocaleString()})]}),(0,o.jsx)(X.A,{}),(0,o.jsx)(tn,{level:4,children:"分享内容"}),(0,o.jsxs)(Y,{bordered:!0,column:1,size:"middle",children:[(0,o.jsx)(Y.Item,{label:"分享标题",children:(0,o.jsxs)(g.A,{children:[(0,o.jsx)(to,{strong:!0,children:n.title}),(0,o.jsx)(u.Ay,{type:"text",size:"small",icon:(0,o.jsx)(te.A,{}),onClick:()=>A(n.title)})]})}),(0,o.jsx)(Y.Item,{label:"分享路径",children:(0,o.jsxs)(g.A,{children:[(0,o.jsx)(to,{code:!0,children:n.path}),(0,o.jsx)(u.Ay,{type:"text",size:"small",icon:(0,o.jsx)(te.A,{}),onClick:()=>A(n.path)})]})}),n.description&&(0,o.jsx)(Y.Item,{label:"分享描述",children:(0,o.jsx)(tr,{children:n.description})}),n.imageUrl&&(0,o.jsx)(Y.Item,{label:"分享图片",children:(0,o.jsxs)(g.A,{direction:"vertical",children:[(0,o.jsxs)(g.A,{children:[(0,o.jsx)(to,{code:!0,children:n.imageUrl}),(0,o.jsx)(u.Ay,{type:"text",size:"small",icon:(0,o.jsx)(te.A,{}),onClick:()=>A(n.imageUrl)})]}),(0,o.jsx)(e4,{src:n.imageUrl,alt:"分享图片",style:{maxWidth:"300px",maxHeight:"200px"},fallback:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"})]})})]}),(0,o.jsx)(X.A,{}),(0,o.jsx)(tn,{level:4,children:"使用说明"}),(0,o.jsx)(d.A,{message:"微信小程序分享配置",description:(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{children:"该配置可用于微信小程序的分享功能，小程序端可通过以下API获取："}),(0,o.jsxs)("ul",{children:[(0,o.jsxs)("li",{children:[(0,o.jsx)(to,{code:!0,children:"GET /api/v1/weixin/share-config"})," - 获取所有分享配置"]}),(0,o.jsxs)("li",{children:[(0,o.jsxs)(to,{code:!0,children:["GET /api/v1/weixin/share-config/",n.type]})," - 获取指定类型的分享配置"]})]}),(0,o.jsxs)("p",{children:["在小程序中使用时，可以在页面的 ",(0,o.jsx)(to,{code:!0,children:"onShareAppMessage"})," 方法中返回这些配置。"]})]}),type:"info",showIcon:!0})]})}):(0,o.jsx)("div",{style:{padding:"24px"},children:(0,o.jsx)(d.A,{message:"分享配置不存在",description:"请检查URL是否正确，或者该配置已被删除。",type:"error",showIcon:!0,action:(0,o.jsx)(u.Ay,{size:"small",onClick:y,children:"返回列表"})})})}},69880:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o});let o=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\shares\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\shares\\[id]\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79505:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var o=n(80828),r=n(43210);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"eye",theme:"outlined"};var l=n(21898);let a=r.forwardRef(function(e,t){return r.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85975:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var o=n(80828),r=n(43210);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"};var l=n(21898);let a=r.forwardRef(function(e,t){return r.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))})},94735:e=>{"use strict";e.exports=require("events")},96625:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});let o=n(20775).A}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),o=t.X(0,[447,433,658,816,204,553,579,405,756],()=>n(63715));module.exports=o})();