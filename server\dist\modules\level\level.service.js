"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LevelService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const level_entity_1 = require("./entities/level.entity");
const uuid_1 = require("uuid");
const date_formatter_1 = require("../../common/utils/date-formatter");
const thesaurus_service_1 = require("../thesaurus/thesaurus.service");
const phrase_service_1 = require("../phrase/phrase.service");
let LevelService = class LevelService {
    levelModel;
    thesaurusService;
    phraseService;
    MAX_LEVELS = 1000;
    constructor(levelModel, thesaurusService, phraseService) {
        this.levelModel = levelModel;
        this.thesaurusService = thesaurusService;
        this.phraseService = phraseService;
    }
    async getLevelEntity(id) {
        const level = await this.levelModel.findOne({ id }).exec();
        if (!level) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的关卡`);
        }
        return level;
    }
    _mapToLevelResponseDto(level) {
        return {
            id: level.id,
            name: level.name,
            difficulty: level.difficulty,
            description: level.description,
            thesaurusIds: level.thesaurusIds,
            phraseIds: level.phraseIds,
            createdAt: (0, date_formatter_1.formatDate)(level.createdAt),
            updatedAt: (0, date_formatter_1.formatDate)(level.updatedAt),
        };
    }
    async create(createLevelDto) {
        const currentCount = await this.levelModel.countDocuments().exec();
        if (currentCount >= this.MAX_LEVELS) {
            throw new common_1.BadRequestException(`关卡数量已达到上限 ${this.MAX_LEVELS} 个`);
        }
        if ((!createLevelDto.thesaurusIds || createLevelDto.thesaurusIds.length === 0) &&
            (!createLevelDto.phraseIds || createLevelDto.phraseIds.length === 0)) {
            throw new common_1.BadRequestException('必须至少提供词库ID或词组ID中的一种');
        }
        const thesaurusIds = createLevelDto.thesaurusIds || [];
        const phraseIds = createLevelDto.phraseIds || [];
        for (const thesaurusId of thesaurusIds) {
            await this.thesaurusService.getThesaurusEntity(thesaurusId);
        }
        for (const phraseId of phraseIds) {
            await this.phraseService.findOne(phraseId);
        }
        const newLevel = new this.levelModel({
            id: (0, uuid_1.v4)(),
            name: createLevelDto.name,
            difficulty: createLevelDto.difficulty,
            description: createLevelDto.description,
            thesaurusIds: thesaurusIds,
            phraseIds: phraseIds,
        });
        const savedLevel = await newLevel.save();
        return this._mapToLevelResponseDto(savedLevel);
    }
    async findAll() {
        const levels = await this.levelModel.find().exec();
        return levels.map(level => this._mapToLevelResponseDto(level));
    }
    async findOne(id) {
        const level = await this.getLevelEntity(id);
        return this._mapToLevelResponseDto(level);
    }
    async update(id, updateLevelDto) {
        const existingLevel = await this.getLevelEntity(id);
        const updateData = {};
        if (updateLevelDto.name !== undefined) {
            updateData.name = updateLevelDto.name;
        }
        if (updateLevelDto.difficulty !== undefined) {
            updateData.difficulty = updateLevelDto.difficulty;
        }
        if (updateLevelDto.description !== undefined) {
            updateData.description = updateLevelDto.description;
        }
        if (updateLevelDto.thesaurusIds !== undefined) {
            if (updateLevelDto.thesaurusIds && updateLevelDto.thesaurusIds.length > 0) {
                for (const thesaurusId of updateLevelDto.thesaurusIds) {
                    await this.thesaurusService.getThesaurusEntity(thesaurusId);
                }
            }
            updateData.thesaurusIds = updateLevelDto.thesaurusIds || [];
        }
        if (updateLevelDto.phraseIds !== undefined) {
            if (updateLevelDto.phraseIds && updateLevelDto.phraseIds.length > 0) {
                for (const phraseId of updateLevelDto.phraseIds) {
                    await this.phraseService.findOne(phraseId);
                }
            }
            updateData.phraseIds = updateLevelDto.phraseIds || [];
        }
        const finalThesaurusIds = updateData.thesaurusIds !== undefined ? updateData.thesaurusIds : existingLevel.thesaurusIds;
        const finalPhraseIds = updateData.phraseIds !== undefined ? updateData.phraseIds : existingLevel.phraseIds;
        if ((!finalThesaurusIds || finalThesaurusIds.length === 0) &&
            (!finalPhraseIds || finalPhraseIds.length === 0)) {
            throw new common_1.BadRequestException('关卡必须至少包含词库或词组中的一种');
        }
        const updatedLevel = await this.levelModel.findOneAndUpdate({ id }, { ...updateData, updatedAt: new Date() }, { new: true }).exec();
        if (!updatedLevel) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的关卡`);
        }
        return this._mapToLevelResponseDto(updatedLevel);
    }
    async remove(id) {
        const result = await this.levelModel.deleteOne({ id }).exec();
        if (result.deletedCount === 0) {
            throw new common_1.NotFoundException(`未找到 ID 为 "${id}" 的关卡`);
        }
    }
    async addPhrase(levelId, phraseId) {
        const level = await this.getLevelEntity(levelId);
        await this.phraseService.findOne(phraseId);
        const isPhraseInAssociatedThesaurus = await this.thesaurusService.isPhraseInAnyOfThesauruses(phraseId, level.thesaurusIds);
        if (!isPhraseInAssociatedThesaurus) {
            throw new common_1.BadRequestException(`词组 ID "${phraseId}" 不属于此关卡所关联的任何词库。`);
        }
        if (level.phraseIds.includes(phraseId)) {
            throw new common_1.BadRequestException(`词组 ID "${phraseId}" 已存在于关卡 "${levelId}" 中。`);
        }
        level.phraseIds.push(phraseId);
        const updatedLevel = await level.save();
        return this._mapToLevelResponseDto(updatedLevel);
    }
    async removePhrase(levelId, phraseId) {
        const level = await this.getLevelEntity(levelId);
        const phraseIndexInLevel = level.phraseIds.indexOf(phraseId);
        if (phraseIndexInLevel === -1) {
            throw new common_1.NotFoundException(`词组 ID "${phraseId}" 不在关卡 "${levelId}" 中。`);
        }
        level.phraseIds.splice(phraseIndexInLevel, 1);
        const updatedLevel = await level.save();
        return this._mapToLevelResponseDto(updatedLevel);
    }
    async getLevelCount() {
        const total = await this.levelModel.countDocuments().exec();
        return {
            total,
            maxLevels: this.MAX_LEVELS,
            remaining: this.MAX_LEVELS - total,
        };
    }
    async findByDifficulty(difficulty) {
        const filteredLevels = await this.levelModel.find({ difficulty }).exec();
        return filteredLevels.map(level => this._mapToLevelResponseDto(level));
    }
    async getLevelWithPhrases(levelId) {
        const level = await this.getLevelEntity(levelId);
        const phrases = [];
        for (const phraseId of level.phraseIds) {
            try {
                const phrase = await this.phraseService.findOne(phraseId);
                phrases.push(phrase);
            }
            catch (error) {
                console.warn(`词组 ${phraseId} 不存在，已跳过`);
            }
        }
        return {
            ...this._mapToLevelResponseDto(level),
            phrases,
        };
    }
};
exports.LevelService = LevelService;
exports.LevelService = LevelService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(level_entity_1.Level.name)),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => thesaurus_service_1.ThesaurusService))),
    __param(2, (0, common_1.Inject)((0, common_1.forwardRef)(() => phrase_service_1.PhraseService))),
    __metadata("design:paramtypes", [mongoose_2.Model,
        thesaurus_service_1.ThesaurusService,
        phrase_service_1.PhraseService])
], LevelService);
//# sourceMappingURL=level.service.js.map