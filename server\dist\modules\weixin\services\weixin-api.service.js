"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WeixinApiService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinApiService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("axios");
let WeixinApiService = WeixinApiService_1 = class WeixinApiService {
    configService;
    logger = new common_1.Logger(WeixinApiService_1.name);
    httpClient;
    appId;
    appSecret;
    constructor(configService) {
        this.configService = configService;
        this.appId = this.configService.get('weixin.appId') || '';
        this.appSecret = this.configService.get('weixin.appSecret') || '';
        if (!this.appId || !this.appSecret) {
            this.logger.warn('⚠️  微信小程序配置缺失，请在环境变量中配置 WEIXIN_APPID 和 WEIXIN_APP_SECRET');
            this.logger.warn(`   当前配置: appId=${this.appId ? '已配置' : '未配置'}, appSecret=${this.appSecret ? '已配置' : '未配置'}`);
        }
        else {
            this.logger.log(`✅ 微信小程序配置已加载: appId=${this.appId.substring(0, 8)}...`);
        }
        this.httpClient = axios_1.default.create({
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json',
            },
        });
        this.httpClient.interceptors.request.use((config) => {
            this.logger.debug(`🔗 微信API请求: ${config.method?.toUpperCase()} ${config.url}`);
            return config;
        }, (error) => {
            this.logger.error('❌ 微信API请求失败:', error);
            return Promise.reject(error);
        });
        this.httpClient.interceptors.response.use((response) => {
            this.logger.debug(`✅ 微信API响应: ${response.status} ${response.config.url}`);
            return response;
        }, (error) => {
            this.logger.error('❌ 微信API响应错误:', error.response?.data || error.message);
            return Promise.reject(error);
        });
    }
    async code2Session(code, retryCount = 0) {
        const maxRetries = 2;
        const retryDelay = 1000;
        try {
            if (!code || typeof code !== 'string' || code.trim().length === 0) {
                this.logger.error('❌ 微信登录失败: code参数无效');
                throw new common_1.BadRequestException('微信登录失败: 登录凭证无效');
            }
            if (!this.isConfigured()) {
                this.logger.error('❌ 微信登录失败: 微信小程序配置不完整');
                throw new common_1.BadRequestException('微信登录失败: 服务配置错误');
            }
            const url = 'https://api.weixin.qq.com/sns/jscode2session';
            const params = {
                appid: this.appId,
                secret: this.appSecret,
                js_code: code.trim(),
                grant_type: 'authorization_code',
            };
            this.logger.log(`🔐 调用微信登录接口: code=${code.substring(0, 8)}... (尝试 ${retryCount + 1}/${maxRetries + 1})`);
            this.logger.debug(`📋 请求参数: appid=${this.appId.substring(0, 8)}..., grant_type=${params.grant_type}`);
            const response = await this.httpClient.get(url, {
                params,
                timeout: 8000,
            });
            const data = response.data;
            this.logger.debug(`📥 微信API响应: ${JSON.stringify(data)}`);
            if (data.errcode) {
                const errorMsg = this.getErrorMessage(data.errcode);
                this.logger.error(`❌ 微信登录失败: ${data.errcode} - ${data.errmsg || 'Unknown error'}`);
                this.logger.error(`🔍 错误详情: code=${code.substring(0, 8)}..., rid=${data.rid || 'N/A'}`);
                const noRetryErrors = [40029, 40013, 40125, 40226];
                if (noRetryErrors.includes(data.errcode)) {
                    throw new common_1.BadRequestException(`微信登录失败: ${errorMsg} (错误码: ${data.errcode})`);
                }
                if (retryCount < maxRetries) {
                    this.logger.warn(`⏳ 微信登录重试中... (${retryCount + 1}/${maxRetries})`);
                    await this.delay(retryDelay * (retryCount + 1));
                    return this.code2Session(code, retryCount + 1);
                }
                throw new common_1.BadRequestException(`微信登录失败: ${errorMsg} (错误码: ${data.errcode})`);
            }
            if (!data.openid || !data.session_key) {
                this.logger.error('❌ 微信登录响应数据不完整:', data);
                throw new common_1.BadRequestException('微信登录失败: 响应数据不完整');
            }
            this.logger.log(`✅ 微信登录成功: openid=${data.openid.substring(0, 8)}..., session_key=${data.session_key.substring(0, 8)}...`);
            return data;
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            if (retryCount < maxRetries) {
                this.logger.warn(`⏳ 网络异常，微信登录重试中... (${retryCount + 1}/${maxRetries}): ${error.message}`);
                await this.delay(retryDelay * (retryCount + 1));
                return this.code2Session(code, retryCount + 1);
            }
            this.logger.error('❌ 调用微信登录接口异常:', error);
            throw new common_1.BadRequestException('微信登录服务暂时不可用，请稍后重试');
        }
    }
    delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }
    async getAccessToken() {
        try {
            const url = 'https://api.weixin.qq.com/cgi-bin/token';
            const params = {
                grant_type: 'client_credential',
                appid: this.appId,
                secret: this.appSecret,
            };
            this.logger.log('🔑 获取微信 access_token...');
            const response = await this.httpClient.get(url, {
                params,
            });
            const data = response.data;
            if (data.errcode) {
                this.logger.error(`❌ 获取 access_token 失败: ${data.errcode} - ${data.errmsg}`);
                throw new common_1.BadRequestException(`获取访问令牌失败: ${this.getErrorMessage(data.errcode)}`);
            }
            this.logger.log('✅ 获取 access_token 成功');
            return data;
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error('❌ 获取 access_token 异常:', error);
            throw new common_1.BadRequestException('获取访问令牌失败，请稍后重试');
        }
    }
    async checkSessionKey(openid, sessionKey) {
        try {
            const accessToken = await this.getAccessToken();
            const url = 'https://api.weixin.qq.com/wxa/checksession';
            const params = {
                access_token: accessToken.access_token,
                signature: this.generateSignature(sessionKey),
                openid: openid,
                sig_method: 'hmac_sha256',
            };
            const response = await this.httpClient.get(url, { params });
            const data = response.data;
            if (data.errcode === 0) {
                this.logger.log(`✅ 登录态有效: openid=${openid.substring(0, 8)}...`);
                return true;
            }
            else {
                this.logger.log(`❌ 登录态无效: openid=${openid.substring(0, 8)}... errcode=${data.errcode}`);
                return false;
            }
        }
        catch (error) {
            this.logger.error('❌ 检验登录态异常:', error);
            return false;
        }
    }
    generateSignature(sessionKey) {
        return Buffer.from(sessionKey).toString('base64');
    }
    getErrorMessage(errcode) {
        const errorMessages = {
            40029: '登录凭证无效或已过期，请重新登录',
            40013: 'AppID无效，请检查小程序配置',
            40125: 'AppSecret无效，请检查小程序配置',
            45011: 'API调用太频繁，请稍候再试',
            40226: '用户被拦截，请联系客服',
            [-1]: '系统繁忙，请稍后重试',
            40001: '获取access_token时AppSecret错误，或者access_token无效',
            40002: '不合法的凭证类型',
            40003: '不合法的OpenID',
            40004: '不合法的媒体文件类型',
            40005: '不合法的文件类型',
            40006: '不合法的文件大小',
            40007: '不合法的媒体文件id',
            40008: '不合法的消息类型',
            40009: '不合法的图片文件大小',
            40010: '不合法的语音文件大小',
            40011: '不合法的视频文件大小',
            40012: '不合法的缩略图文件大小',
            50001: '用户未授权该api',
            50002: '用户受限，可能是违规后接口被封禁',
            41001: '缺少access_token参数',
            41002: '缺少appid参数',
            41003: '缺少refresh_token参数',
            41004: '缺少secret参数',
            41005: '缺少多媒体文件数据',
            41006: '缺少media_id参数',
            41007: '缺少子菜单数据',
            41008: '缺少oauth code',
            41009: '缺少openid',
        };
        const message = errorMessages[errcode];
        if (message) {
            return message;
        }
        if (errcode >= 40000 && errcode < 41000) {
            return `请求参数错误 (${errcode})`;
        }
        else if (errcode >= 41000 && errcode < 42000) {
            return `缺少必要参数 (${errcode})`;
        }
        else if (errcode >= 42000 && errcode < 43000) {
            return `参数值无效 (${errcode})`;
        }
        else if (errcode >= 43000 && errcode < 44000) {
            return `请求过于频繁 (${errcode})`;
        }
        else if (errcode >= 44000 && errcode < 45000) {
            return `多媒体文件为空 (${errcode})`;
        }
        else if (errcode >= 45000 && errcode < 46000) {
            return `多媒体文件大小超过限制 (${errcode})`;
        }
        else if (errcode >= 46000 && errcode < 47000) {
            return `控制流异常 (${errcode})`;
        }
        else if (errcode >= 47000 && errcode < 48000) {
            return `JSON/XML内容异常 (${errcode})`;
        }
        else if (errcode >= 48000 && errcode < 49000) {
            return `url异常 (${errcode})`;
        }
        else if (errcode >= 50000 && errcode < 51000) {
            return `用户未授权 (${errcode})`;
        }
        return `未知错误 (${errcode})`;
    }
    isConfigured() {
        return !!(this.appId && this.appSecret);
    }
    getConfig() {
        return {
            appId: this.appId ? `${this.appId.substring(0, 8)}...` : '未配置',
            appSecret: this.appSecret ? '已配置' : '未配置',
            isConfigured: this.isConfigured(),
        };
    }
};
exports.WeixinApiService = WeixinApiService;
exports.WeixinApiService = WeixinApiService = WeixinApiService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], WeixinApiService);
//# sourceMappingURL=weixin-api.service.js.map