"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LevelController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const level_service_1 = require("./level.service");
const create_level_dto_1 = require("./dto/create-level.dto");
const update_level_dto_1 = require("./dto/update-level.dto");
const level_response_dto_1 = require("./dto/level-response.dto");
const add_phrase_to_level_dto_1 = require("./dto/add-phrase-to-level.dto");
let LevelController = class LevelController {
    levelService;
    constructor(levelService) {
        this.levelService = levelService;
    }
    create(createLevelDto) {
        return this.levelService.create(createLevelDto);
    }
    findAll() {
        return this.levelService.findAll();
    }
    getLevelCount() {
        return this.levelService.getLevelCount();
    }
    findByDifficulty(difficulty) {
        const difficultyNum = parseInt(difficulty, 10);
        if (isNaN(difficultyNum) || difficultyNum < 1 || difficultyNum > 5) {
            throw new common_1.BadRequestException('难度必须是1-5之间的数字');
        }
        return this.levelService.findByDifficulty(difficultyNum);
    }
    findOne(id) {
        return this.levelService.findOne(id);
    }
    findOneWithPhrases(id) {
        return this.levelService.getLevelWithPhrases(id);
    }
    update(id, updateLevelDto) {
        return this.levelService.update(id, updateLevelDto);
    }
    remove(id) {
        return this.levelService.remove(id);
    }
    addPhraseToLevel(levelId, addPhraseDto) {
        return this.levelService.addPhrase(levelId, addPhraseDto.phraseId);
    }
    removePhraseFromLevel(levelId, phraseId) {
        return this.levelService.removePhrase(levelId, phraseId);
    }
};
exports.LevelController = LevelController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建新关卡' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '关卡创建成功（兼容旧版，实际应为201）', type: level_response_dto_1.LevelResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '关卡创建成功', type: level_response_dto_1.LevelResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_level_dto_1.CreateLevelDto]),
    __metadata("design:returntype", void 0)
], LevelController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有关卡列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取关卡列表', type: [level_response_dto_1.LevelResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], LevelController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('count'),
    (0, swagger_1.ApiOperation)({ summary: '获取关卡总数' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功获取关卡总数',
        schema: {
            type: 'object',
            properties: {
                total: { type: 'number', description: '关卡总数' },
                maxLevels: { type: 'number', description: '最大关卡数限制' },
                remaining: { type: 'number', description: '剩余可创建关卡数' },
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], LevelController.prototype, "getLevelCount", null);
__decorate([
    (0, common_1.Get)('difficulty/:difficulty'),
    (0, swagger_1.ApiOperation)({ summary: '根据难度获取关卡列表' }),
    (0, swagger_1.ApiParam)({ name: 'difficulty', description: '关卡难度 (1-5)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取指定难度的关卡列表', type: [level_response_dto_1.LevelResponseDto] }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '难度参数无效' }),
    __param(0, (0, common_1.Param)('difficulty')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LevelController.prototype, "findByDifficulty", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取单个关卡' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关卡的UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取关卡', type: level_response_dto_1.LevelResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关卡未找到' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LevelController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/with-phrases'),
    (0, swagger_1.ApiOperation)({ summary: '获取关卡详细信息（包含词组详情）' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关卡的UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功获取关卡详细信息',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                difficulty: { type: 'number' },
                description: { type: 'string' },
                thesaurusIds: { type: 'array', items: { type: 'string' } },
                phraseIds: { type: 'array', items: { type: 'string' } },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' },
                phrases: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            text: { type: 'string' },
                            meaning: { type: 'string' },
                            example: { type: 'string' },
                            tags: { type: 'array', items: { type: 'string' } },
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关卡未找到' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LevelController.prototype, "findOneWithPhrases", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新指定ID的关卡' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关卡的UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '关卡更新成功', type: level_response_dto_1.LevelResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关卡未找到' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_level_dto_1.UpdateLevelDto]),
    __metadata("design:returntype", void 0)
], LevelController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '删除指定ID的关卡' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关卡的UUID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '关卡删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关卡未找到' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LevelController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/phrases'),
    (0, swagger_1.ApiOperation)({ summary: '向指定关卡添加词组' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关卡的UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '词组添加成功', type: level_response_dto_1.LevelResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关卡或词组未找到' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误、词组不属于关联词库或词组已存在于关卡中' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, add_phrase_to_level_dto_1.AddPhraseToLevelDto]),
    __metadata("design:returntype", void 0)
], LevelController.prototype, "addPhraseToLevel", null);
__decorate([
    (0, common_1.Delete)(':id/phrases/:phraseId'),
    (0, swagger_1.ApiOperation)({ summary: '从指定关卡移除词组' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '关卡的UUID' }),
    (0, swagger_1.ApiParam)({ name: 'phraseId', description: '词组的UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '词组移除成功', type: level_response_dto_1.LevelResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '关卡或词组未在关卡中找到' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('phraseId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], LevelController.prototype, "removePhraseFromLevel", null);
exports.LevelController = LevelController = __decorate([
    (0, swagger_1.ApiTags)('levels'),
    (0, common_1.Controller)('levels'),
    __metadata("design:paramtypes", [level_service_1.LevelService])
], LevelController);
//# sourceMappingURL=level.controller.js.map