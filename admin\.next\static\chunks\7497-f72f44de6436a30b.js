"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7497],{37497:(e,t,n)=>{n.d(t,{A:()=>_});var a,o=n(12115),r=n(29300),i=n.n(r),l=n(79630),s=n(40419),u=n(27061),c=n(85757),d=n(21858),f=n(52673),p=n(11261),m=n(52032),g=n(43717),x=n(48804),v=n(86608),h=n(32417),b=n(49172),w=n(16962),A=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","font-variant","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing","word-break","white-space"],y={},S=["prefixCls","defaultValue","value","autoSize","onResize","className","style","disabled","onChange","onInternalAutoSize"],z=o.forwardRef(function(e,t){var n=e.prefixCls,r=e.defaultValue,c=e.value,p=e.autoSize,m=e.onResize,g=e.className,z=e.style,C=e.disabled,E=e.onChange,N=(e.onInternalAutoSize,(0,f.A)(e,S)),R=(0,x.A)(r,{value:c,postState:function(e){return null!=e?e:""}}),O=(0,d.A)(R,2),I=O[0],F=O[1],j=o.useRef();o.useImperativeHandle(t,function(){return{textArea:j.current}});var H=o.useMemo(function(){return p&&"object"===(0,v.A)(p)?[p.minRows,p.maxRows]:[]},[p]),P=(0,d.A)(H,2),V=P[0],T=P[1],k=!!p,D=function(){try{if(document.activeElement===j.current){var e=j.current,t=e.selectionStart,n=e.selectionEnd,a=e.scrollTop;j.current.setSelectionRange(t,n),j.current.scrollTop=a}}catch(e){}},L=o.useState(2),M=(0,d.A)(L,2),W=M[0],B=M[1],_=o.useState(),q=(0,d.A)(_,2),K=q[0],X=q[1],Y=function(){B(0)};(0,b.A)(function(){k&&Y()},[c,V,T,k]),(0,b.A)(function(){if(0===W)B(1);else if(1===W){var e=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;a||((a=document.createElement("textarea")).setAttribute("tab-index","-1"),a.setAttribute("aria-hidden","true"),a.setAttribute("name","hiddenTextarea"),document.body.appendChild(a)),e.getAttribute("wrap")?a.setAttribute("wrap",e.getAttribute("wrap")):a.removeAttribute("wrap");var i=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.getAttribute("id")||e.getAttribute("data-reactid")||e.getAttribute("name");if(t&&y[n])return y[n];var a=window.getComputedStyle(e),o=a.getPropertyValue("box-sizing")||a.getPropertyValue("-moz-box-sizing")||a.getPropertyValue("-webkit-box-sizing"),r=parseFloat(a.getPropertyValue("padding-bottom"))+parseFloat(a.getPropertyValue("padding-top")),i=parseFloat(a.getPropertyValue("border-bottom-width"))+parseFloat(a.getPropertyValue("border-top-width")),l={sizingStyle:A.map(function(e){return"".concat(e,":").concat(a.getPropertyValue(e))}).join(";"),paddingSize:r,borderSize:i,boxSizing:o};return t&&n&&(y[n]=l),l}(e,n),l=i.paddingSize,s=i.borderSize,u=i.boxSizing,c=i.sizingStyle;a.setAttribute("style","".concat(c,";").concat("\n  min-height:0 !important;\n  max-height:none !important;\n  height:0 !important;\n  visibility:hidden !important;\n  overflow:hidden !important;\n  position:absolute !important;\n  z-index:-1000 !important;\n  top:0 !important;\n  right:0 !important;\n  pointer-events: none !important;\n")),a.value=e.value||e.placeholder||"";var d=void 0,f=void 0,p=a.scrollHeight;if("border-box"===u?p+=s:"content-box"===u&&(p-=l),null!==o||null!==r){a.value=" ";var m=a.scrollHeight-l;null!==o&&(d=m*o,"border-box"===u&&(d=d+l+s),p=Math.max(d,p)),null!==r&&(f=m*r,"border-box"===u&&(f=f+l+s),t=p>f?"":"hidden",p=Math.min(f,p))}var g={height:p,overflowY:t,resize:"none"};return d&&(g.minHeight=d),f&&(g.maxHeight=f),g}(j.current,!1,V,T);B(2),X(e)}else D()},[W]);var G=o.useRef(),Q=function(){w.A.cancel(G.current)};o.useEffect(function(){return Q},[]);var $=(0,u.A)((0,u.A)({},z),k?K:null);return(0===W||1===W)&&($.overflowY="hidden",$.overflowX="hidden"),o.createElement(h.A,{onResize:function(e){2===W&&(null==m||m(e),p&&(Q(),G.current=(0,w.A)(function(){Y()})))},disabled:!(p||m)},o.createElement("textarea",(0,l.A)({},N,{ref:j,style:$,className:i()(n,g,(0,s.A)({},"".concat(n,"-disabled"),C)),disabled:C,value:I,onChange:function(e){F(e.target.value),null==E||E(e)}})))}),C=["defaultValue","value","onFocus","onBlur","onChange","allowClear","maxLength","onCompositionStart","onCompositionEnd","suffix","prefixCls","showCount","count","className","style","disabled","hidden","classNames","styles","onResize","onClear","onPressEnter","readOnly","autoSize","onKeyDown"],E=o.forwardRef(function(e,t){var n,a,r=e.defaultValue,v=e.value,h=e.onFocus,b=e.onBlur,w=e.onChange,A=e.allowClear,y=e.maxLength,S=e.onCompositionStart,E=e.onCompositionEnd,N=e.suffix,R=e.prefixCls,O=void 0===R?"rc-textarea":R,I=e.showCount,F=e.count,j=e.className,H=e.style,P=e.disabled,V=e.hidden,T=e.classNames,k=e.styles,D=e.onResize,L=e.onClear,M=e.onPressEnter,W=e.readOnly,B=e.autoSize,_=e.onKeyDown,q=(0,f.A)(e,C),K=(0,x.A)(r,{value:v,defaultValue:r}),X=(0,d.A)(K,2),Y=X[0],G=X[1],Q=null==Y?"":String(Y),$=o.useState(!1),J=(0,d.A)($,2),U=J[0],Z=J[1],ee=o.useRef(!1),et=o.useState(null),en=(0,d.A)(et,2),ea=en[0],eo=en[1],er=(0,o.useRef)(null),ei=(0,o.useRef)(null),el=function(){var e;return null==(e=ei.current)?void 0:e.textArea},es=function(){el().focus()};(0,o.useImperativeHandle)(t,function(){var e;return{resizableTextArea:ei.current,focus:es,blur:function(){el().blur()},nativeElement:(null==(e=er.current)?void 0:e.nativeElement)||el()}}),(0,o.useEffect)(function(){Z(function(e){return!P&&e})},[P]);var eu=o.useState(null),ec=(0,d.A)(eu,2),ed=ec[0],ef=ec[1];o.useEffect(function(){if(ed){var e;(e=el()).setSelectionRange.apply(e,(0,c.A)(ed))}},[ed]);var ep=(0,m.A)(F,I),em=null!=(n=ep.max)?n:y,eg=Number(em)>0,ex=ep.strategy(Q),ev=!!em&&ex>em,eh=function(e,t){var n=t;!ee.current&&ep.exceedFormatter&&ep.max&&ep.strategy(t)>ep.max&&(n=ep.exceedFormatter(t,{max:ep.max}),t!==n&&ef([el().selectionStart||0,el().selectionEnd||0])),G(n),(0,g.gS)(e.currentTarget,e,w,n)},eb=N;ep.show&&(a=ep.showFormatter?ep.showFormatter({value:Q,count:ex,maxLength:em}):"".concat(ex).concat(eg?" / ".concat(em):""),eb=o.createElement(o.Fragment,null,eb,o.createElement("span",{className:i()("".concat(O,"-data-count"),null==T?void 0:T.count),style:null==k?void 0:k.count},a)));var ew=!B&&!I&&!A;return o.createElement(p.a,{ref:er,value:Q,allowClear:A,handleReset:function(e){G(""),es(),(0,g.gS)(el(),e,w)},suffix:eb,prefixCls:O,classNames:(0,u.A)((0,u.A)({},T),{},{affixWrapper:i()(null==T?void 0:T.affixWrapper,(0,s.A)((0,s.A)({},"".concat(O,"-show-count"),I),"".concat(O,"-textarea-allow-clear"),A))}),disabled:P,focused:U,className:i()(j,ev&&"".concat(O,"-out-of-range")),style:(0,u.A)((0,u.A)({},H),ea&&!ew?{height:"auto"}:{}),dataAttrs:{affixWrapper:{"data-count":"string"==typeof a?a:void 0}},hidden:V,readOnly:W,onClear:L},o.createElement(z,(0,l.A)({},q,{autoSize:B,maxLength:y,onKeyDown:function(e){"Enter"===e.key&&M&&M(e),null==_||_(e)},onChange:function(e){eh(e,e.target.value)},onFocus:function(e){Z(!0),null==h||h(e)},onBlur:function(e){Z(!1),null==b||b(e)},onCompositionStart:function(e){ee.current=!0,null==S||S(e)},onCompositionEnd:function(e){ee.current=!1,eh(e,e.currentTarget.value),null==E||E(e)},className:i()(null==T?void 0:T.textarea),style:(0,u.A)((0,u.A)({},null==k?void 0:k.textarea),{},{resize:null==H?void 0:H.resize}),disabled:P,prefixCls:O,onResize:function(e){var t;null==D||D(e),null!=(t=el())&&t.style.height&&eo(!0)},ref:ei,readOnly:W})))}),N=n(53014),R=n(79007),O=n(15982),I=n(44494),F=n(68151),j=n(9836),H=n(63568),P=n(63893),V=n(18574),T=n(30611),k=n(45431),D=n(61388),L=n(19086);let M=e=>{let{componentCls:t,paddingLG:n}=e,a="".concat(t,"-textarea");return{["textarea".concat(t)]:{maxWidth:"100%",height:"auto",minHeight:e.controlHeight,lineHeight:e.lineHeight,verticalAlign:"bottom",transition:"all ".concat(e.motionDurationSlow),resize:"vertical",["&".concat(t,"-mouse-active")]:{transition:"all ".concat(e.motionDurationSlow,", height 0s, width 0s")}},["".concat(t,"-textarea-affix-wrapper-resize-dirty")]:{width:"auto"},[a]:{position:"relative","&-show-count":{["".concat(t,"-data-count")]:{position:"absolute",bottom:e.calc(e.fontSize).mul(e.lineHeight).mul(-1).equal(),insetInlineEnd:0,color:e.colorTextDescription,whiteSpace:"nowrap",pointerEvents:"none"}},["\n        &-allow-clear > ".concat(t,",\n        &-affix-wrapper").concat(a,"-has-feedback ").concat(t,"\n      ")]:{paddingInlineEnd:n},["&-affix-wrapper".concat(t,"-affix-wrapper")]:{padding:0,["> textarea".concat(t)]:{fontSize:"inherit",border:"none",outline:"none",background:"transparent",minHeight:e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),"&:focus":{boxShadow:"none !important"}},["".concat(t,"-suffix")]:{margin:0,"> *:not(:last-child)":{marginInline:0},["".concat(t,"-clear-icon")]:{position:"absolute",insetInlineEnd:e.paddingInline,insetBlockStart:e.paddingXS},["".concat(a,"-suffix")]:{position:"absolute",top:0,insetInlineEnd:e.paddingInline,bottom:0,zIndex:1,display:"inline-flex",alignItems:"center",margin:"auto",pointerEvents:"none"}}},["&-affix-wrapper".concat(t,"-affix-wrapper-rtl")]:{["".concat(t,"-suffix")]:{["".concat(t,"-data-count")]:{direction:"ltr",insetInlineStart:0}}},["&-affix-wrapper".concat(t,"-affix-wrapper-sm")]:{["".concat(t,"-suffix")]:{["".concat(t,"-clear-icon")]:{insetInlineEnd:e.paddingInlineSM}}}}}},W=(0,k.OF)(["Input","TextArea"],e=>[M((0,D.oX)(e,(0,L.C)(e)))],L.b,{resetFont:!1});var B=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};let _=(0,o.forwardRef)((e,t)=>{var n;let{prefixCls:a,bordered:r=!0,size:l,disabled:s,status:u,allowClear:c,classNames:d,rootClassName:f,className:p,style:m,styles:x,variant:v,showCount:h,onMouseDown:b,onResize:w}=e,A=B(e,["prefixCls","bordered","size","disabled","status","allowClear","classNames","rootClassName","className","style","styles","variant","showCount","onMouseDown","onResize"]),{getPrefixCls:y,direction:S,allowClear:z,autoComplete:C,className:k,style:D,classNames:L,styles:M}=(0,O.TP)("textArea"),_=o.useContext(I.A),{status:q,hasFeedback:K,feedbackIcon:X}=o.useContext(H.$W),Y=(0,R.v)(q,u),G=o.useRef(null);o.useImperativeHandle(t,()=>{var e;return{resizableTextArea:null==(e=G.current)?void 0:e.resizableTextArea,focus:e=>{var t,n;(0,g.F4)(null==(n=null==(t=G.current)?void 0:t.resizableTextArea)?void 0:n.textArea,e)},blur:()=>{var e;return null==(e=G.current)?void 0:e.blur()}}});let Q=y("input",a),$=(0,F.A)(Q),[J,U,Z]=(0,T.MG)(Q,f),[ee]=W(Q,$),{compactSize:et,compactItemClassnames:en}=(0,V.RQ)(Q,S),ea=(0,j.A)(e=>{var t;return null!=(t=null!=l?l:et)?t:e}),[eo,er]=(0,P.A)("textArea",v,r),ei=(0,N.A)(null!=c?c:z),[el,es]=o.useState(!1),[eu,ec]=o.useState(!1);return J(ee(o.createElement(E,Object.assign({autoComplete:C},A,{style:Object.assign(Object.assign({},D),m),styles:Object.assign(Object.assign({},M),x),disabled:null!=s?s:_,allowClear:ei,className:i()(Z,$,p,f,en,k,eu&&"".concat(Q,"-textarea-affix-wrapper-resize-dirty")),classNames:Object.assign(Object.assign(Object.assign({},d),L),{textarea:i()({["".concat(Q,"-sm")]:"small"===ea,["".concat(Q,"-lg")]:"large"===ea},U,null==d?void 0:d.textarea,L.textarea,el&&"".concat(Q,"-mouse-active")),variant:i()({["".concat(Q,"-").concat(eo)]:er},(0,R.L)(Q,Y)),affixWrapper:i()("".concat(Q,"-textarea-affix-wrapper"),{["".concat(Q,"-affix-wrapper-rtl")]:"rtl"===S,["".concat(Q,"-affix-wrapper-sm")]:"small"===ea,["".concat(Q,"-affix-wrapper-lg")]:"large"===ea,["".concat(Q,"-textarea-show-count")]:h||(null==(n=e.count)?void 0:n.show)},U)}),prefixCls:Q,suffix:K&&o.createElement("span",{className:"".concat(Q,"-textarea-suffix")},X),showCount:h,ref:G,onResize:e=>{var t,n;if(null==w||w(e),el&&"function"==typeof getComputedStyle){let e=null==(n=null==(t=G.current)?void 0:t.nativeElement)?void 0:n.querySelector("textarea");e&&"both"===getComputedStyle(e).resize&&ec(!0)}},onMouseDown:e=>{es(!0),null==b||b(e);let t=()=>{es(!1),document.removeEventListener("mouseup",t)};document.addEventListener("mouseup",t)}}))))})}}]);