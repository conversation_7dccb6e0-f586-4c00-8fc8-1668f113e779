import { ConfigService } from '@nestjs/config';
export interface Code2SessionResponse {
    openid: string;
    session_key: string;
    unionid?: string;
    errcode?: number;
    errmsg?: string;
    rid?: string;
}
export interface AccessTokenResponse {
    access_token: string;
    expires_in: number;
    errcode?: number;
    errmsg?: string;
}
export declare class WeixinApiService {
    private configService;
    private readonly logger;
    private readonly httpClient;
    private readonly appId;
    private readonly appSecret;
    constructor(configService: ConfigService);
    code2Session(code: string, retryCount?: number): Promise<Code2SessionResponse>;
    private delay;
    getAccessToken(): Promise<AccessTokenResponse>;
    checkSessionKey(openid: string, sessionKey: string): Promise<boolean>;
    private generateSignature;
    private getErrorMessage;
    isConfigured(): boolean;
    getConfig(): {
        appId: string;
        appSecret: string;
        isConfigured: boolean;
    };
}
