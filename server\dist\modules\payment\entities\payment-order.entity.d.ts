import { Document } from 'mongoose';
export type PaymentOrderDocument = PaymentOrder & Document;
export declare enum PaymentStatus {
    PENDING = "PENDING",
    SUCCESS = "SUCCESS",
    FAILED = "FAILED",
    CANCELLED = "CANCELLED",
    REFUNDED = "REFUNDED"
}
export declare class PaymentOrder {
    id: string;
    userId: string;
    openid: string;
    out_trade_no: string;
    transaction_id?: string;
    description: string;
    total: number;
    status: PaymentStatus;
    vip_package_id: string;
    prepay_id?: string;
    detail?: string;
    attach?: string;
    paid_at?: Date;
    expires_at: Date;
    createdAt: Date;
    updatedAt: Date;
}
export declare const PaymentOrderSchema: import("mongoose").Schema<PaymentOrder, import("mongoose").Model<PaymentOrder, any, any, any, Document<unknown, any, PaymentOrder, any> & PaymentOrder & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, PaymentOrder, Document<unknown, {}, import("mongoose").FlatRecord<PaymentOrder>, {}> & import("mongoose").FlatRecord<PaymentOrder> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
export declare class VipPackage {
    id: string;
    name: string;
    description: string;
    price: number;
    duration: number;
    sortOrder: number;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare const VipPackageSchema: import("mongoose").Schema<VipPackage, import("mongoose").Model<VipPackage, any, any, any, Document<unknown, any, VipPackage, any> & VipPackage & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, VipPackage, Document<unknown, {}, import("mongoose").FlatRecord<VipPackage>, {}> & import("mongoose").FlatRecord<VipPackage> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
export type VipPackageDocument = VipPackage & Document;
