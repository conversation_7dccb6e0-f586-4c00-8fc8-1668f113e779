export declare class CreatePaymentDto {
    openid: string;
    description: string;
    out_trade_no: string;
    total: number;
    detail?: string;
    attach?: string;
}
export declare class WechatPayOrderResponse {
    prepay_id: string;
}
export declare class MiniProgramPaymentResponse {
    appId: string;
    timeStamp: string;
    nonceStr: string;
    package: string;
    signType: string;
    paySign: string;
}
export declare class PaymentNotifyDto {
    id: string;
    create_time: string;
    event_type: string;
    resource_type: string;
    resource: {
        original_type: string;
        algorithm: string;
        ciphertext: string;
        associated_data: string;
        nonce: string;
    };
    summary: string;
}
export declare class PaymentQueryResponse {
    appid: string;
    mchid: string;
    out_trade_no: string;
    transaction_id: string;
    trade_type: string;
    trade_state: string;
    trade_state_desc: string;
    bank_type: string;
    success_time: string;
    payer: {
        openid: string;
    };
    amount: {
        total: number;
        payer_total: number;
        currency: string;
        payer_currency: string;
    };
}
export declare class VipPackageDto {
    id: string;
    name: string;
    description: string;
    price: number;
    duration: number;
    sortOrder: number;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
}
export declare class CreateVipPackageDto {
    name: string;
    description: string;
    price: number;
    duration: number;
    sortOrder?: number;
    isActive?: boolean;
}
export declare class UpdateVipPackageDto {
    name?: string;
    description?: string;
    price?: number;
    duration?: number;
    sortOrder?: number;
    isActive?: boolean;
}
export declare enum PaymentStatus {
    PENDING = "PENDING",
    SUCCESS = "SUCCESS",
    FAILED = "FAILED",
    CANCELLED = "CANCELLED",
    REFUNDED = "REFUNDED"
}
export declare class PaymentOrderDto {
    id: string;
    userId: string;
    openid: string;
    out_trade_no: string;
    transaction_id?: string;
    description: string;
    total: number;
    status: PaymentStatus;
    vip_package_id: string;
    prepay_id?: string;
    paid_at?: string;
    created_at: string;
    updated_at: string;
}
