(()=>{var e={};e.id=987,e.ids=[987],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4691:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=r(7565).A},7565:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(43210),s=r(69662),l=r.n(s),a=r(71802),i=r(52604),o=r(76285),c=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)0>t.indexOf(n[s])&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(r[n[s]]=e[n[s]]);return r};function u(e){return"number"==typeof e?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}let d=["xs","sm","md","lg","xl","xxl"],p=n.forwardRef((e,t)=>{let{getPrefixCls:r,direction:s}=n.useContext(a.QO),{gutter:p,wrap:f}=n.useContext(i.A),{prefixCls:m,span:h,order:x,offset:y,push:g,pull:v,className:b,children:j,flex:w,style:A}=e,$=c(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),O=r("col",m),[C,E,P]=(0,o.xV)(O),S={},k={};d.forEach(t=>{let r={},n=e[t];"number"==typeof n?r.span=n:"object"==typeof n&&(r=n||{}),delete $[t],k=Object.assign(Object.assign({},k),{[`${O}-${t}-${r.span}`]:void 0!==r.span,[`${O}-${t}-order-${r.order}`]:r.order||0===r.order,[`${O}-${t}-offset-${r.offset}`]:r.offset||0===r.offset,[`${O}-${t}-push-${r.push}`]:r.push||0===r.push,[`${O}-${t}-pull-${r.pull}`]:r.pull||0===r.pull,[`${O}-rtl`]:"rtl"===s}),r.flex&&(k[`${O}-${t}-flex`]=!0,S[`--${O}-${t}-flex`]=u(r.flex))});let z=l()(O,{[`${O}-${h}`]:void 0!==h,[`${O}-order-${x}`]:x,[`${O}-offset-${y}`]:y,[`${O}-push-${g}`]:g,[`${O}-pull-${v}`]:v},b,k,E,P),N={};if(p&&p[0]>0){let e=p[0]/2;N.paddingLeft=e,N.paddingRight=e}return w&&(N.flex=u(w),!1!==f||N.minWidth||(N.minWidth=0)),C(n.createElement("div",Object.assign({},$,{style:Object.assign(Object.assign(Object.assign({},N),A),S),className:z,ref:t}),j))})},8662:(e,t,r)=>{"use strict";r.d(t,{A:()=>$});var n=r(43210),s=r(96201),l=r(53428),a=r(56883),i=r(69662),o=r.n(i),c=r(44666),u=r(71802),d=r(37510);let p=e=>{let t,{value:r,formatter:s,precision:l,decimalSeparator:a,groupSeparator:i="",prefixCls:o}=e;if("function"==typeof s)t=s(r);else{let e=String(r),s=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(s&&"-"!==e){let e=s[1],r=s[2]||"0",c=s[4]||"";r=r.replace(/\B(?=(\d{3})+(?!\d))/g,i),"number"==typeof l&&(c=c.padEnd(l,"0").slice(0,l>0?l:0)),c&&(c=`${a}${c}`),t=[n.createElement("span",{key:"int",className:`${o}-content-value-int`},e,r),c&&n.createElement("span",{key:"decimal",className:`${o}-content-value-decimal`},c)]}else t=e}return n.createElement("span",{className:`${o}-content-value`},t)};var f=r(32476),m=r(13581),h=r(60254);let x=e=>{let{componentCls:t,marginXXS:r,padding:n,colorTextDescription:s,titleFontSize:l,colorTextHeading:a,contentFontSize:i,fontFamily:o}=e;return{[t]:Object.assign(Object.assign({},(0,f.dF)(e)),{[`${t}-title`]:{marginBottom:r,color:s,fontSize:l},[`${t}-skeleton`]:{paddingTop:n},[`${t}-content`]:{color:a,fontSize:i,fontFamily:o,[`${t}-content-value`]:{display:"inline-block",direction:"ltr"},[`${t}-content-prefix, ${t}-content-suffix`]:{display:"inline-block"},[`${t}-content-prefix`]:{marginInlineEnd:r},[`${t}-content-suffix`]:{marginInlineStart:r}}})}},y=(0,m.OF)("Statistic",e=>[x((0,h.oX)(e,{}))],e=>{let{fontSizeHeading3:t,fontSize:r}=e;return{titleFontSize:r,contentFontSize:t}});var g=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)0>t.indexOf(n[s])&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(r[n[s]]=e[n[s]]);return r};let v=n.forwardRef((e,t)=>{let{prefixCls:r,className:s,rootClassName:l,style:a,valueStyle:i,value:f=0,title:m,valueRender:h,prefix:x,suffix:v,loading:b=!1,formatter:j,precision:w,decimalSeparator:A=".",groupSeparator:$=",",onMouseEnter:O,onMouseLeave:C}=e,E=g(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:P,direction:S,className:k,style:z}=(0,u.TP)("statistic"),N=P("statistic",r),[q,R,I]=y(N),M=n.createElement(p,{decimalSeparator:A,groupSeparator:$,prefixCls:N,formatter:j,precision:w,value:f}),_=o()(N,{[`${N}-rtl`]:"rtl"===S},k,s,l,R,I),B=n.useRef(null);n.useImperativeHandle(t,()=>({nativeElement:B.current}));let D=(0,c.A)(E,{aria:!0,data:!0});return q(n.createElement("div",Object.assign({},D,{ref:B,className:_,style:Object.assign(Object.assign({},z),a),onMouseEnter:O,onMouseLeave:C}),m&&n.createElement("div",{className:`${N}-title`},m),n.createElement(d.A,{paragraph:!1,loading:b,className:`${N}-skeleton`},n.createElement("div",{style:i,className:`${N}-content`},x&&n.createElement("span",{className:`${N}-content-prefix`},x),h?h(M):M,v&&n.createElement("span",{className:`${N}-content-suffix`},v)))))}),b=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var j=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)0>t.indexOf(n[s])&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(r[n[s]]=e[n[s]]);return r};let w=e=>{let{value:t,format:r="HH:mm:ss",onChange:i,onFinish:o,type:c}=e,u=j(e,["value","format","onChange","onFinish","type"]),d="countdown"===c,[p,f]=n.useState(null),m=(0,s._q)(()=>{let e=Date.now(),r=new Date(t).getTime();return f({}),null==i||i(d?r-e:e-r),!d||!(r<e)||(null==o||o(),!1)});return n.useEffect(()=>{let e,t=()=>{e=(0,l.A)(()=>{m()&&t()})};return t(),()=>l.A.cancel(e)},[t,d]),n.useEffect(()=>{f({})},[]),n.createElement(v,Object.assign({},u,{value:t,valueRender:e=>(0,a.Ob)(e,{title:void 0}),formatter:(e,t)=>p?function(e,t,r){let{format:n=""}=t,s=new Date(e).getTime(),l=Date.now();return function(e,t){let r=e,n=/\[[^\]]*]/g,s=(t.match(n)||[]).map(e=>e.slice(1,-1)),l=t.replace(n,"[]"),a=b.reduce((e,[t,n])=>{if(e.includes(t)){let s=Math.floor(r/n);return r-=s*n,e.replace(RegExp(`${t}+`,"g"),e=>{let t=e.length;return s.toString().padStart(t,"0")})}return e},l),i=0;return a.replace(n,()=>{let e=s[i];return i+=1,e})}(r?Math.max(s-l,0):Math.max(l-s,0),n)}(e,Object.assign(Object.assign({},t),{format:r}),d):"-"}))},A=n.memo(e=>n.createElement(w,Object.assign({},e,{type:"countdown"})));v.Timer=w,v.Countdown=A;let $=v},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13605:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);function s(e,t){let r=(0,n.useRef)([]);return()=>{r.current.push(setTimeout(()=>{var t,r,n,s;(null==(t=e.current)?void 0:t.input)&&(null==(r=e.current)?void 0:r.input.getAttribute("type"))==="password"&&(null==(n=e.current)?void 0:n.input.hasAttribute("value"))&&(null==(s=e.current)||s.input.removeAttribute("value"))}))}}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20775:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(43210),s=r(69662),l=r.n(s),a=r(57266),i=r(71802),o=r(54908),c=r(52604),u=r(76285),d=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)0>t.indexOf(n[s])&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(r[n[s]]=e[n[s]]);return r};function p(e,t){let[r,s]=n.useState("string"==typeof e?e:""),l=()=>{if("string"==typeof e&&s(e),"object"==typeof e)for(let r=0;r<a.ye.length;r++){let n=a.ye[r];if(!t||!t[n])continue;let l=e[n];if(void 0!==l)return void s(l)}};return n.useEffect(()=>{l()},[JSON.stringify(e),t]),r}let f=n.forwardRef((e,t)=>{let{prefixCls:r,justify:s,align:f,className:m,style:h,children:x,gutter:y=0,wrap:g}=e,v=d(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:b,direction:j}=n.useContext(i.QO),w=(0,o.A)(!0,null),A=p(f,w),$=p(s,w),O=b("row",r),[C,E,P]=(0,u.L3)(O),S=function(e,t){let r=[void 0,void 0],n=Array.isArray(e)?e:[e,void 0],s=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return n.forEach((e,t)=>{if("object"==typeof e&&null!==e)for(let n=0;n<a.ye.length;n++){let l=a.ye[n];if(s[l]&&void 0!==e[l]){r[t]=e[l];break}}else r[t]=e}),r}(y,w),k=l()(O,{[`${O}-no-wrap`]:!1===g,[`${O}-${$}`]:$,[`${O}-${A}`]:A,[`${O}-rtl`]:"rtl"===j},m,E,P),z={},N=null!=S[0]&&S[0]>0?-(S[0]/2):void 0;N&&(z.marginLeft=N,z.marginRight=N);let[q,R]=S;z.rowGap=R;let I=n.useMemo(()=>({gutter:[q,R],wrap:g}),[q,R,g]);return C(n.createElement(c.A.Provider,{value:I},n.createElement("div",Object.assign({},v,{className:k,style:Object.assign(Object.assign({},z),h),ref:t}),x)))})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28360:(e,t,r)=>{Promise.resolve().then(r.bind(r,63403))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52604:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(43210).createContext)({})},53854:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var n=r(60687),s=r(43210),l=r(35899),a=r(7872),i=r(52378),o=r(48111),c=r(21411),u=r(94132),d=r(96625),p=r(4691),f=r(11585),m=r(8662),h=r(70084),x=r(27783),y=r(79505),g=r(34308),v=r(23575),b=r(80828);let j={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"};var w=r(21898),A=s.forwardRef(function(e,t){return s.createElement(w.A,(0,b.A)({},e,{ref:t,icon:j}))}),$=r(53082),O=r(16189),C=r(23870);function E(){let[e,t]=(0,s.useState)([]),[r,b]=(0,s.useState)(!1),[j,w]=(0,s.useState)(null),[E,P]=(0,s.useState)(void 0),S=(0,O.useRouter)(),k=async()=>{b(!0);try{let e;e=E?await C.k3.getByDifficulty(E):await C.k3.getAll(),t(e)}catch(e){l.Ay.error("获取关卡列表失败")}finally{b(!1)}},z=async()=>{try{let e=await C.k3.getCount();w(e)}catch(e){l.Ay.error("获取关卡统计失败")}},N=async e=>{try{await C.k3.delete(e),l.Ay.success("关卡删除成功"),k(),z()}catch(e){l.Ay.error("删除关卡失败")}},q=async e=>{try{let t=await C.k3.getWithPhrases(e);a.A.info({title:`关卡详情 - ${t.name}`,width:800,content:(0,n.jsxs)("div",{children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"难度:"})," ",t.difficulty]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"描述:"})," ",t.description||"无"]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"词库数量:"})," ",t.thesaurusIds.length]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"词组数量:"})," ",t.phrases.length]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"包含的词组:"}),(0,n.jsx)("div",{style:{marginTop:8,maxHeight:300,overflow:"auto"},children:t.phrases.map(e=>(0,n.jsx)(i.A,{style:{margin:4},children:e.text},e.id))})]})]})})}catch(e){l.Ay.error("获取关卡详情失败")}},R=e=>["","green","blue","orange","red","purple"][e]||"default",I=[{title:"关卡名称",dataIndex:"name",key:"name",width:200},{title:"难度",dataIndex:"difficulty",key:"difficulty",width:100,render:e=>(0,n.jsxs)(i.A,{color:R(e),children:[e," 级"]})},{title:"描述",dataIndex:"description",key:"description",width:250,render:e=>e||"-"},{title:"词库数量",dataIndex:"thesaurusIds",key:"thesaurusCount",width:100,render:e=>e.length},{title:"词组数量",dataIndex:"phraseIds",key:"phraseCount",width:100,render:e=>e.length},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:180},{title:"操作",key:"action",width:200,render:(e,t)=>(0,n.jsxs)(o.A,{size:"small",wrap:!0,children:[(0,n.jsx)(c.Ay,{type:"link",size:"small",icon:(0,n.jsx)(y.A,{}),onClick:()=>q(t.id),children:"详情"}),(0,n.jsx)(c.Ay,{type:"link",size:"small",icon:(0,n.jsx)(g.A,{}),onClick:()=>S.push(`/levels/${t.id}/edit`),children:"编辑"}),(0,n.jsx)(u.A,{title:"确定要删除这个关卡吗？",onConfirm:()=>N(t.id),okText:"确定",cancelText:"取消",children:(0,n.jsx)(c.Ay,{type:"link",danger:!0,size:"small",icon:(0,n.jsx)(v.A,{}),children:"删除"})})]})}];return(0,n.jsxs)("div",{children:[j&&(0,n.jsxs)(d.A,{gutter:16,style:{marginBottom:16},children:[(0,n.jsx)(p.A,{span:8,children:(0,n.jsx)(f.A,{children:(0,n.jsx)(m.A,{title:"当前关卡总数",value:j.total,prefix:(0,n.jsx)(A,{})})})}),(0,n.jsx)(p.A,{span:8,children:(0,n.jsx)(f.A,{children:(0,n.jsx)(m.A,{title:"最大关卡限制",value:j.maxLevels,prefix:(0,n.jsx)(A,{})})})}),(0,n.jsx)(p.A,{span:8,children:(0,n.jsx)(f.A,{children:(0,n.jsx)(m.A,{title:"剩余可创建",value:j.remaining,prefix:(0,n.jsx)(A,{}),valueStyle:{color:j.remaining>0?"#3f8600":"#cf1322"}})})})]}),(0,n.jsxs)(f.A,{children:[(0,n.jsxs)("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,n.jsx)("h2",{children:"关卡管理"}),(0,n.jsxs)(o.A,{children:[(0,n.jsx)(h.A,{placeholder:"按难度筛选",allowClear:!0,style:{width:120},value:E,onChange:P,options:[{label:"1级",value:1},{label:"2级",value:2},{label:"3级",value:3},{label:"4级",value:4},{label:"5级",value:5}]}),(0,n.jsx)(c.Ay,{type:"primary",icon:(0,n.jsx)($.A,{}),onClick:()=>S.push("/levels/create"),disabled:j?.remaining===0,children:"创建关卡"})]})]}),(0,n.jsx)(x.A,{columns:I,dataSource:e,rowKey:"id",loading:r,pagination:{total:e.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`}})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59389:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(80828),s=r(43210);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"}}]},name:"search",theme:"outlined"};var a=r(21898);let i=s.forwardRef(function(e,t){return s.createElement(a.A,(0,n.A)({},e,{ref:t,icon:l}))})},62977:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var n=r(65239),s=r(48088),l=r(88170),a=r.n(l),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let c={children:["",{children:["(admin)",{children:["levels",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,63403)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10814)),"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\web\\other\\yyddp\\admin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(admin)/levels/page",pathname:"/levels",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63403:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\web\\\\other\\\\yyddp\\\\admin\\\\src\\\\app\\\\(admin)\\\\levels\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\web\\other\\yyddp\\admin\\src\\app\\(admin)\\levels\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79505:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(80828),s=r(43210);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"eye",theme:"outlined"};var a=r(21898);let i=s.forwardRef(function(e,t){return s.createElement(a.A,(0,n.A)({},e,{ref:t,icon:l}))})},79551:e=>{"use strict";e.exports=require("url")},81441:(e,t,r)=>{"use strict";r.d(t,{A:()=>w});var n=r(43210),s=r.n(n),l=r(69662),a=r.n(l),i=r(65610),o=r(7224),c=r(62028),u=r(47994),d=r(65539),p=r(71802),f=r(57026),m=r(59897),h=r(40908),x=r(38770),y=r(11503),g=r(72202),v=r(13605),b=r(18599),j=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)0>t.indexOf(n[s])&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(r[n[s]]=e[n[s]]);return r};let w=(0,n.forwardRef)((e,t)=>{let{prefixCls:r,bordered:l=!0,status:w,size:A,disabled:$,onBlur:O,onFocus:C,suffix:E,allowClear:P,addonAfter:S,addonBefore:k,className:z,style:N,styles:q,rootClassName:R,onChange:I,classNames:M,variant:_}=e,B=j(e,["prefixCls","bordered","status","size","disabled","onBlur","onFocus","suffix","allowClear","addonAfter","addonBefore","className","style","styles","rootClassName","onChange","classNames","variant"]),{getPrefixCls:D,direction:T,allowClear:L,autoComplete:F,className:G,style:H,classNames:V,styles:W}=(0,p.TP)("input"),Q=D("input",r),K=(0,n.useRef)(null),J=(0,m.A)(Q),[X,Y,U]=(0,b.MG)(Q,R),[Z]=(0,b.Ay)(Q,J),{compactSize:ee,compactItemClassnames:et}=(0,g.RQ)(Q,T),er=(0,h.A)(e=>{var t;return null!=(t=null!=A?A:ee)?t:e}),en=s().useContext(f.A),{status:es,hasFeedback:el,feedbackIcon:ea}=(0,n.useContext)(x.$W),ei=(0,d.v)(es,w),eo=function(e){return!!(e.prefix||e.suffix||e.allowClear||e.showCount)}(e)||!!el;(0,n.useRef)(eo);let ec=(0,v.A)(K,!0),eu=(el||E)&&s().createElement(s().Fragment,null,E,el&&ea),ed=(0,u.A)(null!=P?P:L),[ep,ef]=(0,y.A)("input",_,l);return X(Z(s().createElement(i.A,Object.assign({ref:(0,o.K4)(t,K),prefixCls:Q,autoComplete:F},B,{disabled:null!=$?$:en,onBlur:e=>{ec(),null==O||O(e)},onFocus:e=>{ec(),null==C||C(e)},style:Object.assign(Object.assign({},H),N),styles:Object.assign(Object.assign({},W),q),suffix:eu,allowClear:ed,className:a()(z,R,U,J,et,G),onChange:e=>{ec(),null==I||I(e)},addonBefore:k&&s().createElement(c.A,{form:!0,space:!0},k),addonAfter:S&&s().createElement(c.A,{form:!0,space:!0},S),classNames:Object.assign(Object.assign(Object.assign({},M),V),{input:a()({[`${Q}-sm`]:"small"===er,[`${Q}-lg`]:"large"===er,[`${Q}-rtl`]:"rtl"===T},null==M?void 0:M.input,V.input,Y),variant:a()({[`${Q}-${ep}`]:ef},(0,d.L)(Q,ei)),affixWrapper:a()({[`${Q}-affix-wrapper-sm`]:"small"===er,[`${Q}-affix-wrapper-lg`]:"large"===er,[`${Q}-affix-wrapper-rtl`]:"rtl"===T},Y),wrapper:a()({[`${Q}-group-rtl`]:"rtl"===T},Y),groupWrapper:a()({[`${Q}-group-wrapper-sm`]:"small"===er,[`${Q}-group-wrapper-lg`]:"large"===er,[`${Q}-group-wrapper-rtl`]:"rtl"===T,[`${Q}-group-wrapper-${ep}`]:ef},(0,d.L)(`${Q}-group-wrapper`,ei,el),Y)})}))))})},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91912:(e,t,r)=>{Promise.resolve().then(r.bind(r,53854))},94735:e=>{"use strict";e.exports=require("events")},96625:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=r(20775).A}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,433,658,816,204,553,84,579,542,145,451],()=>r(62977));module.exports=n})();