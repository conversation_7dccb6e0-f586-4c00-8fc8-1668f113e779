"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class UserResponseDto {
    id;
    phone;
    openid;
    nickname;
    avatarUrl;
    unlockedLevels;
    completedLevelIds;
    totalGames;
    totalCompletions;
    lastPlayTime;
    isVip;
    vipExpiresAt;
    dailyUnlockLimit;
    dailyUnlockCount;
    dailyShared;
    lastPlayDate;
    totalShares;
    createdAt;
    updatedAt;
}
exports.UserResponseDto = UserResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户的唯一ID（8位随机数字）', example: '12345678' }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户手机号', example: '13800138000', required: false }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信用户的openid', required: false }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "openid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户昵称', required: false }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户头像URL', required: false }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户当前已开启的关卡数', example: 5 }),
    __metadata("design:type", Number)
], UserResponseDto.prototype, "unlockedLevels", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户已通关的关卡ID列表', type: [String] }),
    __metadata("design:type", Array)
], UserResponseDto.prototype, "completedLevelIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户总游戏次数' }),
    __metadata("design:type", Number)
], UserResponseDto.prototype, "totalGames", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户总通关次数' }),
    __metadata("design:type", Number)
], UserResponseDto.prototype, "totalCompletions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后游戏时间', example: '2024-01-01 12:00:00' }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "lastPlayTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP状态', example: false }),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "isVip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP过期时间', example: '2024-01-31 23:59:59', required: false }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "vipExpiresAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日解锁限制次数', example: 15 }),
    __metadata("design:type", Number)
], UserResponseDto.prototype, "dailyUnlockLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当日解锁次数', example: 3 }),
    __metadata("design:type", Number)
], UserResponseDto.prototype, "dailyUnlockCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当日是否已分享', example: false }),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "dailyShared", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后游戏日期（YYYY-MM-DD）', example: '2025-06-19' }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "lastPlayDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总分享次数', example: 5 }),
    __metadata("design:type", Number)
], UserResponseDto.prototype, "totalShares", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户注册时间', example: '2024-01-01 12:00:00' }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户信息最后更新时间', example: '2024-01-01 12:00:00' }),
    __metadata("design:type", String)
], UserResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=user-response.dto.js.map