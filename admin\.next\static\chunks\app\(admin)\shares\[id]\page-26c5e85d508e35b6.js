(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9626],{29799:(e,t,s)=>{"use strict";s.d(t,{F:()=>c});var a=s(23464),i=s(19868);let r={BASE_URL:"http://127.0.0.1:3001",TIMEOUT:1e4,API_PREFIX:"/api/v1",get FULL_BASE_URL(){return"".concat(this.BASE_URL).concat(this.API_PREFIX)}},l=a.A.create({baseURL:r.BASE_URL,timeout:r.TIMEOUT,headers:{"Content-Type":"application/json"}});l.interceptors.request.use(e=>{let t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),l.interceptors.response.use(e=>e,e=>{if(console.error("响应拦截器错误:",e),e.response){let{status:t,data:s}=e.response;switch(t){case 401:i.Ay.error("登录已过期，请重新登录"),localStorage.removeItem("admin_token"),window.location.href="/login";break;case 403:i.Ay.error("没有权限访问该资源");break;case 404:i.Ay.error("请求的资源不存在");break;case 500:i.Ay.error("服务器内部错误");break;default:i.Ay.error((null==s?void 0:s.message)||"请求失败")}}else e.request?i.Ay.error("网络连接失败，请检查网络"):i.Ay.error("请求配置错误");return Promise.reject(e)});let c={get:(e,t)=>l.get(e,t),post:(e,t,s)=>l.post(e,t,s),put:(e,t,s)=>l.put(e,t,s),patch:(e,t,s)=>l.patch(e,t,s),delete:(e,t)=>l.delete(e,t)}},37048:(e,t,s)=>{Promise.resolve().then(s.bind(s,59492))},59492:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>B});var a=s(95155),i=s(12115),r=s(35695),l=s(97605),c=s(19868),n=s(16467),A=s(95108),o=s(30662),d=s(505),h=s(19361),p=s(74947),x=s(12320),g=s(89631),j=s(37974),u=s(94600),y=s(98617),m=s(81730),v=s(44318),b=s(63625),f=s(90765),w=s(79659),I=s(70129),S=s(73884);let{Title:k,Text:C,Paragraph:E}=l.A;function B(){let e=(0,r.useParams)(),t=(0,r.useRouter)(),[s,l]=(0,i.useState)(null),[B,D]=(0,i.useState)(!0),U=e.id,F=async()=>{if(U){D(!0);try{let e=await S.Dw.getShareConfigById(U);l(e)}catch(e){c.Ay.error("获取分享配置详情失败"),console.error("获取分享配置详情失败:",e)}finally{D(!1)}}};(0,i.useEffect)(()=>{F()},[U]);let Q=()=>{t.push("/shares")},L=async()=>{if(s)try{await S.Dw.toggleShareConfig(s.id),c.Ay.success("".concat(s.isActive?"禁用":"启用","成功")),F()}catch(t){let e=t&&"object"==typeof t&&"message"in t?t.message:"操作失败";c.Ay.error(e)}},R=e=>{navigator.clipboard.writeText(e).then(()=>{c.Ay.success("已复制到剪贴板")}).catch(()=>{c.Ay.error("复制失败")})};return B?(0,a.jsx)("div",{style:{padding:"24px",textAlign:"center"},children:(0,a.jsx)(n.A,{size:"large"})}):s?(0,a.jsx)("div",{style:{padding:"24px"},children:(0,a.jsxs)(d.A,{children:[(0,a.jsx)("div",{style:{marginBottom:"24px"},children:(0,a.jsxs)(h.A,{justify:"space-between",align:"middle",children:[(0,a.jsx)(p.A,{children:(0,a.jsxs)(x.A,{children:[(0,a.jsx)(o.Ay,{icon:(0,a.jsx)(m.A,{}),onClick:Q,children:"返回"}),(0,a.jsxs)(k,{level:3,style:{margin:0},children:[(0,a.jsx)(v.A,{style:{marginRight:"8px"}}),"分享配置详情"]})]})}),(0,a.jsx)(p.A,{children:(0,a.jsxs)(x.A,{children:[(0,a.jsx)(o.Ay,{type:s.isActive?"default":"primary",icon:s.isActive?(0,a.jsx)(b.A,{}):(0,a.jsx)(f.A,{}),onClick:L,children:s.isActive?"禁用":"启用"}),(0,a.jsx)(o.Ay,{type:"primary",icon:(0,a.jsx)(w.A,{}),onClick:()=>{t.push("/shares/".concat(U,"/edit"))},children:"编辑"})]})})]})}),(0,a.jsxs)(g.A,{title:"基本信息",bordered:!0,column:2,size:"middle",children:[(0,a.jsx)(g.A.Item,{label:"配置ID",span:2,children:(0,a.jsxs)(x.A,{children:[(0,a.jsx)(C,{code:!0,children:s.id}),(0,a.jsx)(o.Ay,{type:"text",size:"small",icon:(0,a.jsx)(I.A,{}),onClick:()=>R(s.id)})]})}),(0,a.jsx)(g.A.Item,{label:"配置名称",children:s.name}),(0,a.jsx)(g.A.Item,{label:"分享类型",children:(0,a.jsx)(j.A,{color:{default:"blue",result:"green",level:"orange",achievement:"purple",custom:"gray"}[s.type]||"gray",children:(e=>{let t=S.WS.find(t=>t.value===e);return(null==t?void 0:t.label)||e})(s.type)})}),(0,a.jsx)(g.A.Item,{label:"启用状态",children:(0,a.jsx)(j.A,{color:s.isActive?"success":"default",icon:s.isActive?(0,a.jsx)(f.A,{}):(0,a.jsx)(b.A,{}),children:s.isActive?"启用":"禁用"})}),(0,a.jsx)(g.A.Item,{label:"排序权重",children:s.sortOrder}),(0,a.jsx)(g.A.Item,{label:"创建时间",children:new Date(s.createdAt).toLocaleString()}),(0,a.jsx)(g.A.Item,{label:"更新时间",children:new Date(s.updatedAt).toLocaleString()})]}),(0,a.jsx)(u.A,{}),(0,a.jsx)(k,{level:4,children:"分享内容"}),(0,a.jsxs)(g.A,{bordered:!0,column:1,size:"middle",children:[(0,a.jsx)(g.A.Item,{label:"分享标题",children:(0,a.jsxs)(x.A,{children:[(0,a.jsx)(C,{strong:!0,children:s.title}),(0,a.jsx)(o.Ay,{type:"text",size:"small",icon:(0,a.jsx)(I.A,{}),onClick:()=>R(s.title)})]})}),(0,a.jsx)(g.A.Item,{label:"分享路径",children:(0,a.jsxs)(x.A,{children:[(0,a.jsx)(C,{code:!0,children:s.path}),(0,a.jsx)(o.Ay,{type:"text",size:"small",icon:(0,a.jsx)(I.A,{}),onClick:()=>R(s.path)})]})}),s.description&&(0,a.jsx)(g.A.Item,{label:"分享描述",children:(0,a.jsx)(E,{children:s.description})}),s.imageUrl&&(0,a.jsx)(g.A.Item,{label:"分享图片",children:(0,a.jsxs)(x.A,{direction:"vertical",children:[(0,a.jsxs)(x.A,{children:[(0,a.jsx)(C,{code:!0,children:s.imageUrl}),(0,a.jsx)(o.Ay,{type:"text",size:"small",icon:(0,a.jsx)(I.A,{}),onClick:()=>R(s.imageUrl)})]}),(0,a.jsx)(y.A,{src:s.imageUrl,alt:"分享图片",style:{maxWidth:"300px",maxHeight:"200px"},fallback:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"})]})})]}),(0,a.jsx)(u.A,{}),(0,a.jsx)(k,{level:4,children:"使用说明"}),(0,a.jsx)(A.A,{message:"微信小程序分享配置",description:(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{children:"该配置可用于微信小程序的分享功能，小程序端可通过以下API获取："}),(0,a.jsxs)("ul",{children:[(0,a.jsxs)("li",{children:[(0,a.jsx)(C,{code:!0,children:"GET /api/v1/weixin/share-config"})," - 获取所有分享配置"]}),(0,a.jsxs)("li",{children:[(0,a.jsxs)(C,{code:!0,children:["GET /api/v1/weixin/share-config/",s.type]})," - 获取指定类型的分享配置"]})]}),(0,a.jsxs)("p",{children:["在小程序中使用时，可以在页面的 ",(0,a.jsx)(C,{code:!0,children:"onShareAppMessage"})," 方法中返回这些配置。"]})]}),type:"info",showIcon:!0})]})}):(0,a.jsx)("div",{style:{padding:"24px"},children:(0,a.jsx)(A.A,{message:"分享配置不存在",description:"请检查URL是否正确，或者该配置已被删除。",type:"error",showIcon:!0,action:(0,a.jsx)(o.Ay,{size:"small",onClick:Q,children:"返回列表"})})})}},73884:(e,t,s)=>{"use strict";s.d(t,{Dw:()=>i,WS:()=>r,cm:()=>l});var a=s(29799);class i{static async getAllShareConfigs(){return(await a.F.get("/api/v1/share")).data}static async getActiveShareConfigs(){return(await a.F.get("/api/v1/share/active")).data}static async getDefaultShareConfig(){return(await a.F.get("/api/v1/share/default")).data}static async getShareConfigByType(e){return(await a.F.get("/api/v1/share/type/".concat(e))).data}static async getShareConfigById(e){return(await a.F.get("/api/v1/share/".concat(e))).data}static async createShareConfig(e){return(await a.F.post("/api/v1/share",e)).data}static async updateShareConfig(e,t){return(await a.F.patch("/api/v1/share/".concat(e),t)).data}static async toggleShareConfig(e){return(await a.F.put("/api/v1/share/".concat(e,"/toggle"))).data}static async deleteShareConfig(e){return(await a.F.delete("/api/v1/share/".concat(e))).data}}let r=[{value:"default",label:"默认分享",description:"通用分享，适用于首页、游戏页等"},{value:"result",label:"结果分享",description:"展示用户成绩的分享"},{value:"level",label:"关卡分享",description:"邀请好友挑战特定关卡"},{value:"achievement",label:"成就分享",description:"展示用户获得的成就"},{value:"custom",label:"自定义分享",description:"特殊活动或自定义场景"}],l=[{label:"首页",value:"/pages/index/index",description:"小程序首页"},{label:"游戏页",value:"/pages/game/game",description:"游戏主页面"},{label:"关卡页",value:"/pages/level/level?id={levelId}",description:"特定关卡页面，{levelId}会被替换为实际关卡ID"},{label:"结果页",value:"/pages/result/result?score={score}",description:"结果展示页面，{score}会被替换为实际分数"},{label:"排行榜",value:"/pages/rank/rank",description:"排行榜页面"}]}},e=>{var t=t=>e(e.s=t);e.O(0,[8340,547,7469,7497,6312,7605,3726,2689,9706,8441,1684,7358],()=>t(37048)),_N_E=e.O()}]);