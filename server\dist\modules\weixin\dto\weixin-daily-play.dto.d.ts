export declare class WeixinShareDto {
    openid: string;
}
export declare class WeixinShareResponseDto {
    status: 'success' | 'already_shared';
    message: string;
    userId: string;
    dailyUnlockCount: number;
    dailyUnlockLimit: number;
    remainingUnlocks: number;
    isVip: boolean;
    totalShares: number;
}
export declare class WeixinDailyStatusDto {
    id: string;
    dailyUnlockCount: number;
    dailyUnlockLimit: number;
    remainingUnlocks: number;
    dailyShared: boolean;
    isVip: boolean;
    lastPlayDate: string;
    totalShares: number;
    canUnlock: boolean;
    limitReason?: string;
}
