"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinLevelDto = exports.WeixinUserInfoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class WeixinUserInfoDto {
    id;
    maskedPhone;
    nickname;
    avatarUrl;
    unlockedLevels;
    completedLevelIds;
    totalGames;
    totalCompletions;
    lastPlayTime;
    createdAt;
}
exports.WeixinUserInfoDto = WeixinUserInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: '12345678' }),
    __metadata("design:type", String)
], WeixinUserInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '加密的手机号（中间4位用*代替）', example: '138****8000', required: false }),
    __metadata("design:type", String)
], WeixinUserInfoDto.prototype, "maskedPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户昵称', required: false, example: '微信用户' }),
    __metadata("design:type", String)
], WeixinUserInfoDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户头像URL', required: false, example: 'https://wx.qlogo.cn/mmopen/...' }),
    __metadata("design:type", String)
], WeixinUserInfoDto.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '已解锁关卡数', example: 5 }),
    __metadata("design:type", Number)
], WeixinUserInfoDto.prototype, "unlockedLevels", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '已完成关卡ID列表', type: [String], example: ['1', '2', '3'] }),
    __metadata("design:type", Array)
], WeixinUserInfoDto.prototype, "completedLevelIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总游戏次数', example: 20 }),
    __metadata("design:type", Number)
], WeixinUserInfoDto.prototype, "totalGames", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总通关次数', example: 15 }),
    __metadata("design:type", Number)
], WeixinUserInfoDto.prototype, "totalCompletions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后游戏时间', example: '2025-06-18T12:00:00.000Z' }),
    __metadata("design:type", String)
], WeixinUserInfoDto.prototype, "lastPlayTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '注册时间', example: '2025-06-18T10:00:00.000Z' }),
    __metadata("design:type", String)
], WeixinUserInfoDto.prototype, "createdAt", void 0);
class WeixinLevelDto {
    id;
    name;
    difficulty;
    description;
    isUnlocked;
    isCompleted;
    createdAt;
}
exports.WeixinLevelDto = WeixinLevelDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡ID', example: 'level-uuid-123' }),
    __metadata("design:type", String)
], WeixinLevelDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡名称', example: '第1关 - 基础词汇' }),
    __metadata("design:type", String)
], WeixinLevelDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡难度（1-5）', example: 1 }),
    __metadata("design:type", Number)
], WeixinLevelDto.prototype, "difficulty", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关卡描述', required: false, example: '这是第一关，包含基础词汇' }),
    __metadata("design:type", String)
], WeixinLevelDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否已解锁', example: true }),
    __metadata("design:type", Boolean)
], WeixinLevelDto.prototype, "isUnlocked", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否已完成', example: false }),
    __metadata("design:type", Boolean)
], WeixinLevelDto.prototype, "isCompleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2025-06-18T10:00:00.000Z' }),
    __metadata("design:type", String)
], WeixinLevelDto.prototype, "createdAt", void 0);
//# sourceMappingURL=weixin-user-info.dto.js.map