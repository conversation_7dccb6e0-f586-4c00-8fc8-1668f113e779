"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentOrderDto = exports.PaymentStatus = exports.UpdateVipPackageDto = exports.CreateVipPackageDto = exports.VipPackageDto = exports.PaymentQueryResponse = exports.PaymentNotifyDto = exports.MiniProgramPaymentResponse = exports.WechatPayOrderResponse = exports.CreatePaymentDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreatePaymentDto {
    openid;
    description;
    out_trade_no;
    total;
    detail;
    attach;
}
exports.CreatePaymentDto = CreatePaymentDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户openid', example: 'wx_openid_123456' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: 'openid不能为空' }),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "openid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品描述', example: 'VIP会员-月卡' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '商品描述不能为空' }),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商户订单号', example: 'ORDER_20231201_001' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '商户订单号不能为空' }),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "out_trade_no", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单金额（分）', example: 100 }),
    (0, class_validator_1.IsNumber)({}, { message: '订单金额必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '订单金额必须大于0' }),
    __metadata("design:type", Number)
], CreatePaymentDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品详情', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "detail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '附加数据', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "attach", void 0);
class WechatPayOrderResponse {
    prepay_id;
}
exports.WechatPayOrderResponse = WechatPayOrderResponse;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '预支付交易会话标识' }),
    __metadata("design:type", String)
], WechatPayOrderResponse.prototype, "prepay_id", void 0);
class MiniProgramPaymentResponse {
    appId;
    timeStamp;
    nonceStr;
    package;
    signType;
    paySign;
}
exports.MiniProgramPaymentResponse = MiniProgramPaymentResponse;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '小程序ID' }),
    __metadata("design:type", String)
], MiniProgramPaymentResponse.prototype, "appId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '时间戳' }),
    __metadata("design:type", String)
], MiniProgramPaymentResponse.prototype, "timeStamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '随机字符串' }),
    __metadata("design:type", String)
], MiniProgramPaymentResponse.prototype, "nonceStr", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单详情扩展字符串' }),
    __metadata("design:type", String)
], MiniProgramPaymentResponse.prototype, "package", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '签名方式' }),
    __metadata("design:type", String)
], MiniProgramPaymentResponse.prototype, "signType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '签名' }),
    __metadata("design:type", String)
], MiniProgramPaymentResponse.prototype, "paySign", void 0);
class PaymentNotifyDto {
    id;
    create_time;
    event_type;
    resource_type;
    resource;
    summary;
}
exports.PaymentNotifyDto = PaymentNotifyDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知ID' }),
    __metadata("design:type", String)
], PaymentNotifyDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知创建时间' }),
    __metadata("design:type", String)
], PaymentNotifyDto.prototype, "create_time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知类型' }),
    __metadata("design:type", String)
], PaymentNotifyDto.prototype, "event_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知数据类型' }),
    __metadata("design:type", String)
], PaymentNotifyDto.prototype, "resource_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知资源数据' }),
    __metadata("design:type", Object)
], PaymentNotifyDto.prototype, "resource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '回调摘要' }),
    __metadata("design:type", String)
], PaymentNotifyDto.prototype, "summary", void 0);
class PaymentQueryResponse {
    appid;
    mchid;
    out_trade_no;
    transaction_id;
    trade_type;
    trade_state;
    trade_state_desc;
    bank_type;
    success_time;
    payer;
    amount;
}
exports.PaymentQueryResponse = PaymentQueryResponse;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '应用ID' }),
    __metadata("design:type", String)
], PaymentQueryResponse.prototype, "appid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商户号' }),
    __metadata("design:type", String)
], PaymentQueryResponse.prototype, "mchid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商户订单号' }),
    __metadata("design:type", String)
], PaymentQueryResponse.prototype, "out_trade_no", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信支付订单号' }),
    __metadata("design:type", String)
], PaymentQueryResponse.prototype, "transaction_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易类型' }),
    __metadata("design:type", String)
], PaymentQueryResponse.prototype, "trade_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易状态' }),
    __metadata("design:type", String)
], PaymentQueryResponse.prototype, "trade_state", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易状态描述' }),
    __metadata("design:type", String)
], PaymentQueryResponse.prototype, "trade_state_desc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '付款银行' }),
    __metadata("design:type", String)
], PaymentQueryResponse.prototype, "bank_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付完成时间' }),
    __metadata("design:type", String)
], PaymentQueryResponse.prototype, "success_time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户标识' }),
    __metadata("design:type", Object)
], PaymentQueryResponse.prototype, "payer", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单金额' }),
    __metadata("design:type", Object)
], PaymentQueryResponse.prototype, "amount", void 0);
class VipPackageDto {
    id;
    name;
    description;
    price;
    duration;
    sortOrder;
    isActive;
    createdAt;
    updatedAt;
}
exports.VipPackageDto = VipPackageDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '套餐ID', example: 'vip_monthly' }),
    __metadata("design:type", String)
], VipPackageDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '套餐名称', example: 'VIP月卡' }),
    __metadata("design:type", String)
], VipPackageDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '套餐描述', example: '30天VIP特权，无限制解锁关卡' }),
    __metadata("design:type", String)
], VipPackageDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '价格（分）', example: 2900 }),
    __metadata("design:type", Number)
], VipPackageDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '有效期（天）', example: 30 }),
    __metadata("design:type", Number)
], VipPackageDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序权重', example: 1 }),
    __metadata("design:type", Number)
], VipPackageDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否启用', example: true }),
    __metadata("design:type", Boolean)
], VipPackageDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", String)
], VipPackageDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", String)
], VipPackageDto.prototype, "updatedAt", void 0);
class CreateVipPackageDto {
    name;
    description;
    price;
    duration;
    sortOrder;
    isActive;
}
exports.CreateVipPackageDto = CreateVipPackageDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '套餐名称', example: 'VIP月卡' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '套餐名称不能为空' }),
    __metadata("design:type", String)
], CreateVipPackageDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '套餐描述', example: '30天VIP特权，无限制解锁关卡' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '套餐描述不能为空' }),
    __metadata("design:type", String)
], CreateVipPackageDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '价格（分）', example: 2900 }),
    (0, class_validator_1.IsNumber)({}, { message: '价格必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '价格必须大于0' }),
    __metadata("design:type", Number)
], CreateVipPackageDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '有效期（天）', example: 30 }),
    (0, class_validator_1.IsNumber)({}, { message: '有效期必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '有效期必须大于0' }),
    __metadata("design:type", Number)
], CreateVipPackageDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序权重', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序权重必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '排序权重必须大于0' }),
    __metadata("design:type", Number)
], CreateVipPackageDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否启用', example: true, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: '启用状态必须是布尔值' }),
    __metadata("design:type", Boolean)
], CreateVipPackageDto.prototype, "isActive", void 0);
class UpdateVipPackageDto {
    name;
    description;
    price;
    duration;
    sortOrder;
    isActive;
}
exports.UpdateVipPackageDto = UpdateVipPackageDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '套餐名称', example: 'VIP月卡', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '套餐名称不能为空' }),
    __metadata("design:type", String)
], UpdateVipPackageDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '套餐描述', example: '30天VIP特权，无限制解锁关卡', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '套餐描述不能为空' }),
    __metadata("design:type", String)
], UpdateVipPackageDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '价格（分）', example: 2900, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '价格必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '价格必须大于0' }),
    __metadata("design:type", Number)
], UpdateVipPackageDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '有效期（天）', example: 30, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '有效期必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '有效期必须大于0' }),
    __metadata("design:type", Number)
], UpdateVipPackageDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序权重', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序权重必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '排序权重必须大于0' }),
    __metadata("design:type", Number)
], UpdateVipPackageDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否启用', example: true, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: '启用状态必须是布尔值' }),
    __metadata("design:type", Boolean)
], UpdateVipPackageDto.prototype, "isActive", void 0);
var PaymentStatus;
(function (PaymentStatus) {
    PaymentStatus["PENDING"] = "PENDING";
    PaymentStatus["SUCCESS"] = "SUCCESS";
    PaymentStatus["FAILED"] = "FAILED";
    PaymentStatus["CANCELLED"] = "CANCELLED";
    PaymentStatus["REFUNDED"] = "REFUNDED";
})(PaymentStatus || (exports.PaymentStatus = PaymentStatus = {}));
class PaymentOrderDto {
    id;
    userId;
    openid;
    out_trade_no;
    transaction_id;
    description;
    total;
    status;
    vip_package_id;
    prepay_id;
    paid_at;
    created_at;
    updated_at;
}
exports.PaymentOrderDto = PaymentOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户openid' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "openid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商户订单号' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "out_trade_no", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信支付订单号' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "transaction_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品描述' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单金额（分）' }),
    __metadata("design:type", Number)
], PaymentOrderDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付状态', enum: PaymentStatus }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP套餐ID' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "vip_package_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '预支付ID' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "prepay_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付完成时间' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "paid_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "updated_at", void 0);
//# sourceMappingURL=payment.dto.js.map