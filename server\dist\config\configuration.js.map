{"version": 3, "file": "configuration.js", "sourceRoot": "", "sources": ["../../src/config/configuration.ts"], "names": [], "mappings": ";;;AAAA,2BAA2B;AAoCd,QAAA,sBAAsB,GAAG,GAAG,CAAC,MAAM,CAAC;IAC/C,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE;SACnB,KAAK,CAAC,aAAa,EAAE,SAAS,EAAE,YAAY,CAAC;SAC7C,OAAO,CAAC,aAAa,CAAC;IACzB,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IAChC,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACpC,eAAe,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC;IACnD,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACnC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAC3C,KAAK,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAEnC,YAAY,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACrC,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC1C,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACtC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACvC,gBAAgB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACzC,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC1C,uBAAuB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACjD,CAAC,CAAC;AAEH,kBAAe,GAAc,EAAE,CAAC,CAAC;IAC/B,QAAQ,EAAE;QACR,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,WAAY;QAC7B,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,YAAY;KAClD;IACD,MAAM,EAAE;QACN,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,EAAE,EAAE,CAAC;QAC9C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;KACnD;IACD,GAAG,EAAE;QACH,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAW;QAC/B,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK;KAC/C;IACD,MAAM,EAAE;QACN,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE;QACrC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;QAC9C,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE;QACtC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE;QACxC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;QAC5C,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;QAC9C,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,EAAE;KAC1D;IACD,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,MAAM;CACpC,CAAC,CAAC"}