"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VipPackageSchema = exports.VipPackage = exports.PaymentOrderSchema = exports.PaymentOrder = exports.PaymentStatus = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const swagger_1 = require("@nestjs/swagger");
var PaymentStatus;
(function (PaymentStatus) {
    PaymentStatus["PENDING"] = "PENDING";
    PaymentStatus["SUCCESS"] = "SUCCESS";
    PaymentStatus["FAILED"] = "FAILED";
    PaymentStatus["CANCELLED"] = "CANCELLED";
    PaymentStatus["REFUNDED"] = "REFUNDED";
})(PaymentStatus || (exports.PaymentStatus = PaymentStatus = {}));
let PaymentOrder = class PaymentOrder {
    id;
    userId;
    openid;
    out_trade_no;
    transaction_id;
    description;
    total;
    status;
    vip_package_id;
    prepay_id;
    detail;
    attach;
    paid_at;
    expires_at;
    createdAt;
    updatedAt;
};
exports.PaymentOrder = PaymentOrder;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单唯一ID', example: 'pay-order-001' }),
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], PaymentOrder.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: '12345678' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], PaymentOrder.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户openid', example: 'wx_openid_123456' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], PaymentOrder.prototype, "openid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商户订单号', example: 'ORDER_20231201_001' }),
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], PaymentOrder.prototype, "out_trade_no", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信支付订单号', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PaymentOrder.prototype, "transaction_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品描述', example: 'VIP会员-月卡' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], PaymentOrder.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单金额（分）', example: 2900 }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], PaymentOrder.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付状态', enum: PaymentStatus }),
    (0, mongoose_1.Prop)({ required: true, enum: PaymentStatus, default: PaymentStatus.PENDING }),
    __metadata("design:type", String)
], PaymentOrder.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP套餐ID', example: 'vip_monthly' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], PaymentOrder.prototype, "vip_package_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '预支付ID', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PaymentOrder.prototype, "prepay_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品详情', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PaymentOrder.prototype, "detail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '附加数据', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PaymentOrder.prototype, "attach", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付完成时间', required: false }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], PaymentOrder.prototype, "paid_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '过期时间' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Date)
], PaymentOrder.prototype, "expires_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], PaymentOrder.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], PaymentOrder.prototype, "updatedAt", void 0);
exports.PaymentOrder = PaymentOrder = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], PaymentOrder);
exports.PaymentOrderSchema = mongoose_1.SchemaFactory.createForClass(PaymentOrder);
exports.PaymentOrderSchema.index({ userId: 1 });
exports.PaymentOrderSchema.index({ openid: 1 });
exports.PaymentOrderSchema.index({ out_trade_no: 1 }, { unique: true });
exports.PaymentOrderSchema.index({ transaction_id: 1 });
exports.PaymentOrderSchema.index({ status: 1 });
exports.PaymentOrderSchema.index({ expires_at: 1 });
let VipPackage = class VipPackage {
    id;
    name;
    description;
    price;
    duration;
    sortOrder;
    isActive;
    createdAt;
    updatedAt;
};
exports.VipPackage = VipPackage;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '套餐ID', example: 'vip_monthly' }),
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], VipPackage.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '套餐名称', example: 'VIP月卡' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], VipPackage.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '套餐描述', example: '30天VIP特权，无限制解锁关卡' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], VipPackage.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '价格（分）', example: 2900 }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], VipPackage.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '有效期（天）', example: 30 }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], VipPackage.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序权重', example: 1 }),
    (0, mongoose_1.Prop)({ required: true, default: 1 }),
    __metadata("design:type", Number)
], VipPackage.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否启用', example: true }),
    (0, mongoose_1.Prop)({ required: true, default: true }),
    __metadata("design:type", Boolean)
], VipPackage.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], VipPackage.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], VipPackage.prototype, "updatedAt", void 0);
exports.VipPackage = VipPackage = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], VipPackage);
exports.VipPackageSchema = mongoose_1.SchemaFactory.createForClass(VipPackage);
//# sourceMappingURL=payment-order.entity.js.map