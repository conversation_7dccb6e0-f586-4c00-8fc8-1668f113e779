(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1368],{4438:(e,t,l)=>{Promise.resolve().then(l.bind(l,41842))},12320:(e,t,l)=>{"use strict";l.d(t,{A:()=>x});var a=l(12115),s=l(29300),r=l.n(s),n=l(63715);function c(e){return["small","middle","large"].includes(e)}function i(e){return!!e&&"number"==typeof e&&!Number.isNaN(e)}var o=l(15982),u=l(18574);let m=a.createContext({latestIndex:0}),d=m.Provider,p=e=>{let{className:t,index:l,children:s,split:r,style:n}=e,{latestIndex:c}=a.useContext(m);return null==s?null:a.createElement(a.Fragment,null,a.createElement("div",{className:t,style:n},s),l<c&&r&&a.createElement("span",{className:"".concat(t,"-split")},r))};var y=l(93355),h=function(e,t){var l={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(l[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,a=Object.getOwnPropertySymbols(e);s<a.length;s++)0>t.indexOf(a[s])&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(l[a[s]]=e[a[s]]);return l};let f=a.forwardRef((e,t)=>{var l;let{getPrefixCls:s,direction:u,size:m,className:f,style:x,classNames:A,styles:g}=(0,o.TP)("space"),{size:v=null!=m?m:"small",align:j,className:b,rootClassName:w,children:N,direction:O="horizontal",prefixCls:I,split:C,style:k,wrap:E=!1,classNames:P,styles:_}=e,S=h(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[z,L]=Array.isArray(v)?v:[v,v],F=c(L),q=c(z),B=i(L),G=i(z),R=(0,n.A)(N,{keepEmpty:!0}),T=void 0===j&&"horizontal"===O?"center":j,M=s("space",I),[V,W,Y]=(0,y.A)(M),D=r()(M,f,W,"".concat(M,"-").concat(O),{["".concat(M,"-rtl")]:"rtl"===u,["".concat(M,"-align-").concat(T)]:T,["".concat(M,"-gap-row-").concat(L)]:F,["".concat(M,"-gap-col-").concat(z)]:q},b,w,Y),H=r()("".concat(M,"-item"),null!=(l=null==P?void 0:P.item)?l:A.item),J=0,K=R.map((e,t)=>{var l;null!=e&&(J=t);let s=(null==e?void 0:e.key)||"".concat(H,"-").concat(t);return a.createElement(p,{className:H,key:s,index:t,split:C,style:null!=(l=null==_?void 0:_.item)?l:g.item},e)}),Q=a.useMemo(()=>({latestIndex:J}),[J]);if(0===R.length)return null;let U={};return E&&(U.flexWrap="wrap"),!q&&G&&(U.columnGap=z),!F&&B&&(U.rowGap=L),V(a.createElement("div",Object.assign({ref:t,className:D,style:Object.assign(Object.assign(Object.assign({},U),x),k)},S),a.createElement(d,{value:Q},K)))});f.Compact=u.Ay;let x=f},41842:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>v});var a=l(95155),s=l(12115),r=l(35695),n=l(97605),c=l(56020),i=l(44670),o=l(19868),u=l(95108),m=l(505),d=l(1828),p=l(94600),y=l(20778),h=l(12320),f=l(30662),x=l(49179);let{Title:A}=n.A,{TextArea:g}=c.A;function v(){let[e]=i.A.useForm(),t=(0,r.useRouter)(),[l,n]=(0,s.useState)(!1),[v,j]=(0,s.useState)([]),[b,w]=(0,s.useState)(null),N=async()=>{try{let e=await x.LY.getAll();j(e)}catch(e){o.Ay.error("获取词组列表失败")}},O=async()=>{try{let e=await x.k3.getCount();w(e)}catch(e){o.Ay.error("获取关卡统计失败")}};(0,s.useEffect)(()=>{N(),O()},[]);let I=async e=>{if(!e.phraseIds||0===e.phraseIds.length)return void o.Ay.error("请至少选择词库或词组中的一种");n(!0);try{let l={name:e.name,difficulty:e.difficulty,description:e.description,thesaurusIds:e.thesaurusIds||[],phraseIds:e.phraseIds||[]};await x.k3.create(l),o.Ay.success("关卡创建成功"),t.push("/levels")}catch(e){o.Ay.error("创建关卡失败")}finally{n(!1)}};return(0,a.jsxs)("div",{children:[b&&(0,a.jsx)(u.A,{message:"当前已创建 ".concat(b.total," 个关卡，还可以创建 ").concat(b.remaining," 个关卡（最大限制：").concat(b.maxLevels,"）"),type:b.remaining>0?"info":"warning",style:{marginBottom:16},showIcon:!0}),(0,a.jsxs)(m.A,{children:[(0,a.jsx)(A,{level:2,children:"创建新关卡"}),(0,a.jsxs)(i.A,{form:e,layout:"vertical",onFinish:I,initialValues:{difficulty:3},children:[(0,a.jsx)(i.A.Item,{name:"name",label:"关卡名称",rules:[{required:!0,message:"请输入关卡名称"}],children:(0,a.jsx)(c.A,{placeholder:"例如：第1关 - 基础词汇"})}),(0,a.jsx)(i.A.Item,{name:"difficulty",label:"难度等级 (1-5)",rules:[{required:!0,message:"请选择关卡难度"}],children:(0,a.jsx)(d.A,{min:1,max:5,marks:{1:"简单",2:"容易",3:"中等",4:"困难",5:"极难"}})}),(0,a.jsx)(i.A.Item,{name:"description",label:"关卡描述",children:(0,a.jsx)(g,{rows:3,placeholder:"请输入关卡描述（可选）"})}),(0,a.jsx)(p.A,{children:"选择关卡内容"}),(0,a.jsx)(u.A,{message:"提示：您可以通过选择词库或直接选择词组来创建关卡，至少需要选择其中一种方式。",type:"info",style:{marginBottom:16}}),(0,a.jsx)(i.A.Item,{name:"phraseIds",label:"直接选择词组",help:"如果不选择词库，可以直接选择具体的词组",children:(0,a.jsx)(y.A,{mode:"multiple",allowClear:!0,style:{width:"100%"},placeholder:"请选择要包含的词组",filterOption:(e,t)=>{var l;return null==t||null==(l=t.label)?void 0:l.toLowerCase().includes(e.toLowerCase())},options:v.map(e=>({label:"".concat(e.text," - ").concat(e.meaning),value:e.id}))})}),(0,a.jsx)(i.A.Item,{children:(0,a.jsxs)(h.A,{children:[(0,a.jsx)(f.Ay,{type:"primary",htmlType:"submit",loading:l,disabled:(null==b?void 0:b.remaining)===0,children:"创建关卡"}),(0,a.jsx)(f.Ay,{onClick:()=>t.back(),children:"取消"})]})})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8340,547,7469,7497,44,778,7605,4670,2689,4702,9179,8441,1684,7358],()=>t(4438)),_N_E=e.O()}]);