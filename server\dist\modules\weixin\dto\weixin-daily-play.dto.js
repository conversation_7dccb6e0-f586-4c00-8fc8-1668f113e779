"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinDailyStatusDto = exports.WeixinShareResponseDto = exports.WeixinShareDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class WeixinShareDto {
    openid;
}
exports.WeixinShareDto = WeixinShareDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信用户openid', example: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: 'openid不能为空' }),
    __metadata("design:type", String)
], WeixinShareDto.prototype, "openid", void 0);
class WeixinShareResponseDto {
    status;
    message;
    userId;
    dailyUnlockCount;
    dailyUnlockLimit;
    remainingUnlocks;
    isVip;
    totalShares;
}
exports.WeixinShareResponseDto = WeixinShareResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分享状态', example: 'success' }),
    __metadata("design:type", String)
], WeixinShareResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应消息', example: '分享成功，获得5次额外通关机会！' }),
    __metadata("design:type", String)
], WeixinShareResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: '12345678' }),
    __metadata("design:type", String)
], WeixinShareResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前每日解锁次数', example: 3 }),
    __metadata("design:type", Number)
], WeixinShareResponseDto.prototype, "dailyUnlockCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日解锁限制', example: 20 }),
    __metadata("design:type", Number)
], WeixinShareResponseDto.prototype, "dailyUnlockLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '剩余解锁次数', example: 17 }),
    __metadata("design:type", Number)
], WeixinShareResponseDto.prototype, "remainingUnlocks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否为VIP用户', example: false }),
    __metadata("design:type", Boolean)
], WeixinShareResponseDto.prototype, "isVip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总分享次数', example: 5 }),
    __metadata("design:type", Number)
], WeixinShareResponseDto.prototype, "totalShares", void 0);
class WeixinDailyStatusDto {
    id;
    dailyUnlockCount;
    dailyUnlockLimit;
    remainingUnlocks;
    dailyShared;
    isVip;
    lastPlayDate;
    totalShares;
    canUnlock;
    limitReason;
}
exports.WeixinDailyStatusDto = WeixinDailyStatusDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: '12345678' }),
    __metadata("design:type", String)
], WeixinDailyStatusDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前每日解锁次数', example: 3 }),
    __metadata("design:type", Number)
], WeixinDailyStatusDto.prototype, "dailyUnlockCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日解锁限制', example: 15 }),
    __metadata("design:type", Number)
], WeixinDailyStatusDto.prototype, "dailyUnlockLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '剩余解锁次数', example: 12 }),
    __metadata("design:type", Number)
], WeixinDailyStatusDto.prototype, "remainingUnlocks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当日是否已分享', example: false }),
    __metadata("design:type", Boolean)
], WeixinDailyStatusDto.prototype, "dailyShared", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否为VIP用户', example: false }),
    __metadata("design:type", Boolean)
], WeixinDailyStatusDto.prototype, "isVip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后游戏日期', example: '2025-06-19' }),
    __metadata("design:type", String)
], WeixinDailyStatusDto.prototype, "lastPlayDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总分享次数', example: 5 }),
    __metadata("design:type", Number)
], WeixinDailyStatusDto.prototype, "totalShares", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否可以解锁关卡', example: true }),
    __metadata("design:type", Boolean)
], WeixinDailyStatusDto.prototype, "canUnlock", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '限制原因（如果不能解锁）', required: false }),
    __metadata("design:type", String)
], WeixinDailyStatusDto.prototype, "limitReason", void 0);
//# sourceMappingURL=weixin-daily-play.dto.js.map