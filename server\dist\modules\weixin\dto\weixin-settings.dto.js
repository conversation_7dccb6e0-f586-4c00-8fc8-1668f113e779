"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinAppSettingsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class WeixinAppSettingsDto {
    helpUrl;
    backgroundMusicUrl;
}
exports.WeixinAppSettingsDto = WeixinAppSettingsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '帮助页面链接',
        example: 'https://help.example.com',
        required: true
    }),
    __metadata("design:type", String)
], WeixinAppSettingsDto.prototype, "helpUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '背景音乐链接',
        example: 'https://music.example.com/background.mp3',
        required: true
    }),
    __metadata("design:type", String)
], WeixinAppSettingsDto.prototype, "backgroundMusicUrl", void 0);
//# sourceMappingURL=weixin-settings.dto.js.map