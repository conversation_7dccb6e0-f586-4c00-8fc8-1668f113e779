import { Model } from 'mongoose';
import { CreateLevelDto } from './dto/create-level.dto';
import { UpdateLevelDto } from './dto/update-level.dto';
import { LevelDocument } from './entities/level.entity';
import { LevelResponseDto } from './dto/level-response.dto';
import { ThesaurusService } from '../thesaurus/thesaurus.service';
import { PhraseService } from '../phrase/phrase.service';
export declare class LevelService {
    private levelModel;
    private readonly thesaurusService;
    private readonly phraseService;
    private readonly MAX_LEVELS;
    constructor(levelModel: Model<LevelDocument>, thesaurusService: ThesaurusService, phraseService: PhraseService);
    getLevelEntity(id: string): Promise<LevelDocument>;
    private _mapToLevelResponseDto;
    create(createLevelDto: CreateLevelDto): Promise<LevelResponseDto>;
    findAll(): Promise<LevelResponseDto[]>;
    findOne(id: string): Promise<LevelResponseDto>;
    update(id: string, updateLevelDto: UpdateLevelDto): Promise<LevelResponseDto>;
    remove(id: string): Promise<void>;
    addPhrase(levelId: string, phraseId: string): Promise<LevelResponseDto>;
    removePhrase(levelId: string, phraseId: string): Promise<LevelResponseDto>;
    getLevelCount(): Promise<{
        total: number;
        maxLevels: number;
        remaining: number;
    }>;
    findByDifficulty(difficulty: number): Promise<LevelResponseDto[]>;
    getLevelWithPhrases(levelId: string): Promise<any>;
}
