{"version": 3, "file": "thesaurus.service.js", "sourceRoot": "", "sources": ["../../../src/modules/thesaurus/thesaurus.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAwG;AACxG,+CAA+C;AAC/C,uCAAiC;AAGjC,kEAA4F;AAC5F,+BAAoC;AAEpC,sEAA+D;AAC/D,6DAAyD;AAGlD,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAEY;IAEpB;IAHnB,YACuC,cAAwC,EAE5D,aAA4B;QAFR,mBAAc,GAAd,cAAc,CAA0B;QAE5D,kBAAa,GAAb,aAAa,CAAe;IAC5C,CAAC;IAEI,iBAAiB,CAAC,SAA4B;QACpD,OAAO;YACL,EAAE,EAAE,SAAS,CAAC,EAAE;YAChB,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,WAAW,EAAE,SAAS,CAAC,WAAW;YAClC,SAAS,EAAE,SAAS,CAAC,SAAS;YAC9B,SAAS,EAAE,IAAA,2BAAU,EAAC,SAAS,CAAC,SAAS,CAAC;YAC1C,SAAS,EAAE,IAAA,2BAAU,EAAC,SAAS,CAAC,SAAS,CAAC;SAC3C,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,EAAU;QACjC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QACnE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,0BAAiB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QACtD,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,kBAAsC;QACjD,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC;YAC3C,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,IAAI,EAAE,kBAAkB,CAAC,IAAI;YAC7B,WAAW,EAAE,kBAAkB,CAAC,WAAW;YAC3C,SAAS,EAAE,EAAE;SACd,CAAC,CAAC;QACH,MAAM,cAAc,GAAG,MAAM,YAAY,CAAC,IAAI,EAAE,CAAC;QACjD,OAAO,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;QAC5D,OAAO,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,kBAAsC;QAC7D,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CACjE,EAAE,EAAE,EAAE,EACN,EAAE,GAAG,kBAAkB,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,EAChD,EAAE,GAAG,EAAE,IAAI,EAAE,CACd,CAAC,IAAI,EAAE,CAAC;QAET,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,0BAAiB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAClE,IAAI,MAAM,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,0BAAiB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,WAAmB,EAAE,QAAgB;QACnD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAE7D,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE3C,IAAI,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,4BAAmB,CAAC,UAAU,QAAQ,aAAa,WAAW,KAAK,CAAC,CAAC;QACjF,CAAC;QAED,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnC,MAAM,gBAAgB,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;QAChD,OAAO,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,WAAmB,EAAE,QAAgB;QACtD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAC7D,MAAM,sBAAsB,GAAG,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAErE,IAAI,sBAAsB,KAAK,CAAC,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,0BAAiB,CAAC,UAAU,QAAQ,WAAW,WAAW,KAAK,CAAC,CAAC;QAC7E,CAAC;QAED,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC;QACtD,MAAM,gBAAgB,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;QAChD,OAAO,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,QAAgB,EAAE,YAAsB;QACvE,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;YACvC,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;gBAC7D,IAAI,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC3C,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,OAAO,CAAC,IAAI,CAAC,sBAAsB,WAAW,QAAQ,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AA7GY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,4BAAS,CAAC,IAAI,CAAC,CAAA;IAC3B,WAAA,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,8BAAa,CAAC,CAAC,CAAA;qCADa,gBAAK;QAE1B,8BAAa;GAJpC,gBAAgB,CA6G5B"}