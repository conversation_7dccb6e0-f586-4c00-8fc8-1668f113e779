"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThesaurusResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ThesaurusResponseDto {
    id;
    name;
    description;
    phraseIds;
    createdAt;
    updatedAt;
}
exports.ThesaurusResponseDto = ThesaurusResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词库的唯一ID', example: 't1b2c3d4-e5f6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], ThesaurusResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词库名称', example: '日常用语词库' }),
    __metadata("design:type", String)
], ThesaurusResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '词库描述', example: '包含常见的日常对话用语', required: false }),
    __metadata("design:type", String)
], ThesaurusResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '词库包含的词组ID列表',
        example: ['p1a2b3c4-e5f6-7890-1234-567890abcdef', 'p2a2b3c4-e5f6-7890-1234-567890abcdef'],
        type: [String],
    }),
    __metadata("design:type", Array)
], ThesaurusResponseDto.prototype, "phraseIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2023-10-28 10:30:00' }),
    __metadata("design:type", String)
], ThesaurusResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新时间', example: '2023-10-28 10:35:00' }),
    __metadata("design:type", String)
], ThesaurusResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=thesaurus-response.dto.js.map