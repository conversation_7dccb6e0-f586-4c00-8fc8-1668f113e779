"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinUserBindDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class WeixinUserBindDto {
    openid;
    phone;
    nickname;
    avatarUrl;
}
exports.WeixinUserBindDto = WeixinUserBindDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信用户的openid', example: 'wx_openid_123456' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], WeixinUserBindDto.prototype, "openid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户手机号', example: '13800138000' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.Matches)(/^1[3-9]\d{9}$/, { message: '请输入有效的手机号码' }),
    __metadata("design:type", String)
], WeixinUserBindDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户昵称', required: false, example: '微信用户' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], WeixinUserBindDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户头像URL', required: false, example: 'https://wx.qlogo.cn/mmopen/...' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], WeixinUserBindDto.prototype, "avatarUrl", void 0);
//# sourceMappingURL=weixin-user-bind.dto.js.map